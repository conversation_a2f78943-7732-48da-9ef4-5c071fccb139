import pandas as pd
import os
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import logging

# Configuración del logger
logging.basicConfig(filename='etl.log', level=logging.ERROR)

# Función para verificar si el archivo fue modificado hoy
def is_file_updated_today(file_path):
    modification_time = os.path.getmtime(file_path)
    modification_date = datetime.fromtimestamp(modification_time).date()
    today = datetime.today().date()
    if modification_date != today:
        print(f"El archivo fue modificado por última vez el {modification_date}.")
    return modification_date == today

# Ruta del archivo CSV
url_link = r"C:\Users\<USER>\OneDrive - kayze\ETL PYTHON\CalidadNaranja.txt"

# Verificar si el archivo fue actualizado hoy
if not is_file_updated_today(url_link):
    print("El archivo no ha sido actualizado hoy. El código no se ejecutará.")
    exit()

# Variables para conexiones y sesiones
connection = None
session = None

try:
    # Conexiones a las bases de datos
    engine_mysql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)
    engine_mssql = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')

    # Verificar si ya se ejecutó hoy
    fecha_actual = datetime.today().date()
    proceso_nombre = 'Calidad_naranja'
    query = f"SELECT 1 FROM tb_py_procesoEjecucion WHERE FechaEjecucion = '{fecha_actual}' AND EjecucionExitosa = 1 AND Proceso = '{proceso_nombre}'"
    result = pd.read_sql_query(query, engine_mssql)

    if not result.empty:
        print("El proceso ya se ejecutó exitosamente hoy. No se ejecutará nuevamente.")
        exit()

    df = pd.read_csv(url_link, delimiter='\t', encoding='latin-1', skiprows=2)
    df.columns = df.columns.str.replace(r"Calidad\[|\]| ", "", regex=True)
    # Imprimir la estructura del DataFrame por consola
    df.info()

    # Obtener los valores distintos de la columna "MES_EJE"
    meses_distintos = df['MESEJE'].unique()
    placeholders = ','.join(['?'] * len(meses_distintos))
    delete_query = f"DELETE FROM TB_CALIDAD_NARANJA WHERE MES_EJE IN ({placeholders})"
    
    # Usar `with` para manejar la conexión
    with engine_mssql.connect() as connection:
        result = connection.execute(delete_query, tuple(meses_distintos))
        print(f"{result.rowcount} filas eliminadas.")
    
    df['fecha_carga'] = pd.Timestamp('today')
    df.to_sql('TB_CALIDAD_NARANJA_PASO', engine_mssql, if_exists='replace', index=False)
    
    # Ejecutar procedimiento almacenado
    with sessionmaker(bind=engine_mssql)() as session:
        session.execute("exec SP_INSERT_CALIDAD_NARANJA")
        session.commit()
    

       # Insertar marca de ejecución exitosa
    fecha_actual = datetime.today().date()
    proceso_nombre = 'Calidad_naranja'
    session.execute(f"INSERT INTO tb_py_procesoEjecucion (FechaEjecucion, EjecucionExitosa, Proceso) VALUES ('{fecha_actual}', 1, '{proceso_nombre}')")
    session.commit()


    # Consultar datos y cargar en MySQL
    query_rgu = "SELECT * FROM tb_kpi_gerencia_calidad_tecnico"
    data = pd.read_sql_query(query_rgu, engine_mssql)
    data.to_sql('tb_kpi_gerencia_calidad_tecnico', engine_mysql, if_exists='replace', index=False)
    print("Informacion integrada exitosamente.")
    # Consulta final
    # query_final = """
    # SELECT TP_DESC_EMPRESA,       
    #     CAST(ROUND((CAST(SUM(INCUMPLE_CALIDAD) AS FLOAT) / NULLIF(CAST(SUM(Total_actividad) AS FLOAT), 0)) * 100, 2) AS DECIMAL(10,2)) AS calidad_30,
    #     PERIODO
    # FROM tb_kpi_gerencia_calidad_tecnico
    # GROUP BY TP_DESC_EMPRESA, PERIODO
    # UNION ALL
    # SELECT        
    # 'NACIONAL' as TP_DESC_EMPRESA,
    #     CAST(ROUND((CAST(SUM(INCUMPLE_CALIDAD) AS FLOAT) / NULLIF(CAST(SUM(Total_actividad) AS FLOAT), 0)) * 100, 2) AS DECIMAL(10,2)) AS calidad_30,
    #     PERIODO
    # FROM tb_kpi_gerencia_calidad_tecnico
    # GROUP BY PERIODO
    # """
    # df_final = pd.read_sql(query_final, engine_mssql)

except Exception as e:
    logging.error(f"Ocurrió un error: {e}")
    print(f"Ocurrió un error: {e}")
finally:
    # Cerrar conexiones y sesiones si no se hicieron con `with`
    if 'connection' in locals() and connection:
        connection.close()
    if 'session' in locals() and session:
        session.close()