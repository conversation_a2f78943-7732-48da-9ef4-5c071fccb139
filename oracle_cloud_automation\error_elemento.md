<div id="dialog-download-file-dialog-identifier-365781685988137_layer" role="presentation" class="oj-dialog-layer oj-focus-within" data-oj-surrogate-id="dialog-download-file-dialog-identifier-365781685988137_layer_surrogate" data-oj-overlayid="dialog-download-file-dialog-identifier-365781685988137_layer_overlay" aria-modal="true"><oj-dialog class="ofs-download-file-dialog ofs-dialog oj-dialog oj-component oj-component-initnode oj-complete" :id="[['dialog-' + screenIdentifier]]" dialog-title="[[translations.dialogTitle]]" cancel-behavior="escape" on-oj-close="[[onDialogDismiss]]" id="dialog-download-file-dialog-identifier-365781685988137" tabindex="-1" role="dialog" aria-labelledby="ui-id-58" title="Exportación incompleta" style="display: block; top: 210px; left: 189px;">
    
    
<div class="oj-dialog-container"><div class="oj-dialog-header"><h1 class="oj-dialog-title" id="ui-id-58">Exportación incompleta</h1></div><div class="oj-dialog-content oj-dialog-default-content"><div class="oj-dialog-body-wrapper"><div slot="body" :id="[['body-' + screenIdentifier]]" id="body-download-file-dialog-identifier-365781685988137" class="oj-dialog-body">
        <oj-message-banner data="[[downloadFilePreparationError]]" type="section" class="oj-complete"><div tabindex="-1" class="oj-c-messagebanner MessageBannerBaseTheme_baseTheme__1mzbhvy0 "><div class="_1yl1w5m c6r810 _199le7" style="gap: var(--oj-c-EXPERIMENTAL-DO-NOT-USE-size-units, var(--oj-core-spacing-1x)) var(--oj-c-EXPERIMENTAL-DO-NOT-USE-size-units, var(--oj-core-spacing-1x));"><div style="max-height: none; overflow: initial;"><div role="alert" aria-atomic="true" data-oj-key="string-errorMessage" tabindex="0" class=" MessageBannerVariants_multiVariantStyles_severity_error__10p8l3t0 MessageStyles_messageStyles_base_banner__ie50yz0 oj-c-messagebanner-error MessageStyles_messageStyles_section__ie50yz2"><div class="MessageStyles_messageStyles_content_base__ie50yz3 MessageStyles_messageStyles_content_banner__ie50yz4"><div role="presentation" class="oj-c-messagebanner-start-icon MessageStyles_messageStartIconStyles_base__ie50yz9 MessageStyles_messageStartIconStyles_banner__ie50yza MessageStyles_severityIconStyles_banner__ie50yzl"><div class="_1yl1w5m c6r810 _1fef7u5" style="height: 100%;"><span role="img" title="Error" class="MessageStyles_startIconContainerStyles_banner__ie50yzo"><svg height="1em" width="1em" viewBox="0 0 24 24" class="IconStyle_currentColor__sdo2n67" style="font-size: 1em;"><g fill="none"><path d="M2 12c0 5.523 4.477 10 10 10s10-4.477 10-10S17.523 2 12 2 2 6.477 2 12zm5.293-3.293 1.414-1.414 8 8-1.414 1.414z" fill="currentcolor"></path></g></svg></span></div></div><div class="_1yl1w5m c6r810 _199le7" style="flex: 1 1 0%; gap: var(--oj-c-PRIVATE-DO-NOT-USE-core-spacing-2x) var(--oj-c-PRIVATE-DO-NOT-USE-core-spacing-2x);"><div role="presentation" class="MessageStyles_messageHeaderStyles_base__ie50yzh MessageStyles_messageHeaderStyles_banner__ie50yzi"><div role="heading" class="oj-c-messagebanner-summary MessageStyles_messageSummaryStyles_base__ie50yzr MessageStyles_messageSummaryStyles_banner__ie50yzs">Terminated by timeout</div></div></div></div></div></div><span aria-live="polite" aria-atomic="false" class="l8vdxx">Zona de entrada de mensajes. Pulse F6 para volver al elemento enfocado anterior.</span></div></div></oj-message-banner>
        <!--oj-bind-if test='[[!isErrorMessageDisplayed()]]'--><!--ko if:!isErrorMessageDisplayed()--><!--/ko--><!--/oj-bind-if-->
    </div></div></div><div><div slot="footer" class="oj-dialog-footer">
        <!--oj-bind-if test='[[isErrorMessageDisplayed()]]'--><!--ko if:isErrorMessageDisplayed()-->
            <oj-button :id="[['dismiss-button-' + screenIdentifier]]" on-oj-action="[[onDialogDismiss]]" label="[[translations.dismissButtonText]]" id="dismiss-button-download-file-dialog-identifier-365781685988137" title="Descartar" class="oj-button oj-button-outlined-chrome oj-button-text-only oj-enabled oj-complete oj-default"><button aria-labelledby="dismiss-button-download-file-dialog-identifier-365781685988137_oj27|text" class="oj-button-button"><div class="oj-button-label"><span id="dismiss-button-download-file-dialog-identifier-365781685988137_oj27|text" class="oj-button-text">Descartar</span></div></button></oj-button>
        <!--/ko--><!--/oj-bind-if-->
        <!--oj-bind-if test='[[!isErrorMessageDisplayed()]]'--><!--ko if:!isErrorMessageDisplayed()--><!--/ko--><!--/oj-bind-if-->
    </div></div></div></oj-dialog></div>