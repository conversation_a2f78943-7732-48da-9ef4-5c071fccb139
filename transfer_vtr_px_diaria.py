import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import time
import math
from datetime import datetime
import sys

def transfer_table(source_engine, target_engine, table_name, batch_size=5000):
    """
    Transfiere datos de una tabla entre bases de datos por lotes.
    
    Args:
        source_engine: Conexión SQLAlchemy a la base de datos origen
        target_engine: Conexión SQLAlchemy a la base de datos destino
        table_name: Nombre de la tabla a transferir
        batch_size: Tamaño del lote para la transferencia
    """
    print(f"\nIniciando transferencia de la tabla: {table_name}")
    start_time = datetime.now()
    
    # Obtener el número total de filas
    with source_engine.connect() as conn:
        total_rows = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
    
    if total_rows == 0:
        print(f"La tabla {table_name} está vacía. No hay datos para transferir.")
        return
    
    total_batches = math.ceil(total_rows / batch_size)
    print(f"Total de filas a transferir: {total_rows} en {total_batches} lotes")
    
    # Leer y transferir por lotes
    for i in range(total_batches):
        offset = i * batch_size
        print(f"Procesando lote {i+1}/{total_batches} (filas {offset+1}-{min(offset + batch_size, total_rows)})")
        
        # Leer lote actual
        query = f"""
            SELECT * 
            FROM {table_name}
            ORDER BY (SELECT NULL)
            OFFSET {offset} ROWS 
            FETCH NEXT {batch_size} ROWS ONLY
        """
        
        df = pd.read_sql(query, source_engine)
        
        # Insertar en la base de datos destino
        if_exists = 'replace' if i == 0 else 'append'
        df.to_sql(
            name=table_name,
            con=target_engine,
            if_exists=if_exists,
            index=False,
            chunksize=1000,
            method='multi'
        )
    
    # Verificar la transferencia
    with target_engine.connect() as conn:
        transferred_rows = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
    
    elapsed = datetime.now() - start_time
    print(f"\nTransferencia completada en {elapsed}")
    print(f"Filas transferidas: {transferred_rows}/{total_rows}")
    
    if transferred_rows == total_rows:
        print("¡Verificación exitosa! Todas las filas se transfirieron correctamente.")
    else:
        print(f"Advertencia: Se transfirieron {transferred_rows} filas de {total_rows}.")

def execute_stored_procedure(engine, procedure_name):
    """
    Ejecuta un procedimiento almacenado en la base de datos.
    
    Args:
        engine: Conexión SQLAlchemy
        procedure_name: Nombre del procedimiento almacenado
    """
    print(f"\nEjecutando procedimiento almacenado: {procedure_name}")
    start_time = datetime.now()
    
    try:
        # Usar autocommit para asegurar que el SP se ejecute
        with engine.connect() as conn:
            # Opción 1: Usando EXECUTE (recomendado para SQL Server)
            sql = text(f"EXECUTE {procedure_name}")
            
            print(f"Ejecutando SQL: {sql}")
            
            # Comenzar transacción explícita
            trans = conn.begin()
            try:
                result = conn.execute(sql)
                trans.commit()  # Confirmar la transacción
                print("Transacción confirmada (commit)")
                
                # Si el procedimiento devuelve resultados, los mostramos
                if result.returns_rows:
                    rows = result.fetchall()
                    print(f"El procedimiento devolvió {len(rows)} filas de resultados.")
                else:
                    print("El procedimiento se ejecutó sin devolver resultados.")
                    
            except Exception as e:
                trans.rollback()  # Revertir en caso de error
                print(f"Error en la transacción, rollback ejecutado: {str(e)}")
                raise
                
        elapsed = datetime.now() - start_time
        print(f"Procedimiento ejecutado correctamente en {elapsed}")
        return True
        
    except Exception as e:
        print(f"Error al ejecutar el procedimiento almacenado: {str(e)}")
        print(f"Tipo de error: {type(e).__name__}")
        return False

def execute_stored_procedure_alternative(engine, procedure_name):
    """
    Método alternativo para ejecutar SP usando pyodbc directamente
    """
    print(f"\nMétodo alternativo - Ejecutando SP: {procedure_name}")
    
    try:
        # Obtener conexión raw de pyodbc
        raw_conn = engine.raw_connection()
        cursor = raw_conn.cursor()
        
        # Ejecutar el SP
        cursor.execute(f"EXEC {procedure_name}")
        
        # Confirmar cambios
        raw_conn.commit()
        
        print("SP ejecutado y confirmado usando pyodbc directo")
        
        cursor.close()
        raw_conn.close()
        
        return True
        
    except Exception as e:
        print(f"Error en método alternativo: {str(e)}")
        return False

def test_connection(engine, db_name):
    """
    Prueba la conexión a la base de datos
    """
    try:
        with engine.connect() as conn:
            # Probar conexión básica
            result = conn.execute(text("SELECT 1 as test"))
            test_val = result.scalar()
            
            if test_val == 1:
                print(f"✓ Conexión exitosa a {db_name}")
                
                # Mostrar información del servidor
                if 'mssql' in str(engine.url):
                    server_info = conn.execute(text("SELECT @@VERSION")).scalar()
                    print(f"Servidor SQL Server: {server_info[:100]}...")
                    
                    # Verificar si el SP existe
                    sp_check = conn.execute(text("""
                        SELECT COUNT(*) 
                        FROM sys.procedures 
                        WHERE name = 'sp_vtr_px_diaria'
                    """)).scalar()
                    
                    if sp_check > 0:
                        print("✓ El stored procedure 'sp_vtr_px_diaria' existe")
                    else:
                        print("✗ El stored procedure 'sp_vtr_px_diaria' NO existe")
                        return False
                
                return True
            else:
                print(f"✗ Problema en la conexión a {db_name}")
                return False
                
    except Exception as e:
        print(f"✗ Error de conexión a {db_name}: {str(e)}")
        return False

def main():
    try:
        print("Iniciando el proceso de transferencia...")
        print(f"Hora de inicio: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Configuración de conexiones
        print("\nConectando a las bases de datos...")
        
        # Conexión a SQL Server (origen) - Mejorada
        sqlserver_conn_str = (
            'mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?'
            'driver=ODBC+Driver+17+for+SQL+Server&'
            'Trusted_Connection=no&'
            'TrustServerCertificate=yes&'
            'Encrypt=no&'
            'autocommit=false'  # Manejaremos commits manualmente
        )
        
        # Conexión a MySQL (destino)
        mysql_conn_str = (
            'mysql+mysqlconnector://ncornejo:N1c0l7as17@'
            '170.239.85.233:3306/operaciones_tqw'
        )
        
        # Crear motores de conexión con configuración mejorada
        source_engine = create_engine(
            sqlserver_conn_str, 
            pool_pre_ping=True,
            echo=True,  # Para debug - mostrar SQL ejecutado
            pool_timeout=30,
            pool_recycle=3600
        )
        
        target_engine = create_engine(
            mysql_conn_str, 
            pool_pre_ping=True,
            pool_timeout=30,
            pool_recycle=3600
        )
        
        # Probar conexiones
        if not test_connection(source_engine, "SQL Server"):
            print("Error: No se pudo conectar a SQL Server")
            sys.exit(1)
            
        if not test_connection(target_engine, "MySQL"):
            print("Error: No se pudo conectar a MySQL")
            sys.exit(1)
        
        # 1. Ejecutar el procedimiento almacenado
        sp_name = 'sp_vtr_px_diaria'
        
        print(f"\n{'='*50}")
        print("EJECUTANDO STORED PROCEDURE")
        print(f"{'='*50}")
        
        # Intentar método principal
        success = execute_stored_procedure(source_engine, sp_name)
        
        # Si falla, intentar método alternativo
        if not success:
            print("\nIntentando método alternativo...")
            success = execute_stored_procedure_alternative(source_engine, sp_name)
        
        if not success:
            print("Error: No se pudo ejecutar el procedimiento almacenado con ningún método.")
            print("Verificaciones a realizar:")
            print("1. ¿El SP existe en la base de datos?")
            print("2. ¿El usuario tiene permisos para ejecutar el SP?")
            print("3. ¿El SP requiere parámetros?")
            sys.exit(1)
            
        # 2. Esperar para asegurar que el SP haya terminado
        print(f"\nEsperando 5 segundos para que el SP termine...")
        time.sleep(5)
        
        # Tabla a transferir
        table_name = 'tb_vtr_px_diaria_lv2'
        
        print(f"\n{'='*50}")
        print("VERIFICANDO TABLA Y INICIANDO TRANSFERENCIA")
        print(f"{'='*50}")
        
        # Verificar si la tabla existe en el origen
        with source_engine.connect() as conn:
            table_exists = conn.dialect.has_table(conn, table_name)
        
        if not table_exists:
            print(f"Error: La tabla {table_name} no existe en la base de datos origen.")
            # Intentar listar tablas disponibles
            try:
                with source_engine.connect() as conn:
                    tables = conn.execute(text("""
                        SELECT TABLE_NAME 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_TYPE = 'BASE TABLE' 
                        AND TABLE_NAME LIKE '%vtr%'
                    """)).fetchall()
                    print("Tablas disponibles con 'vtr' en el nombre:")
                    for table in tables:
                        print(f"  - {table[0]}")
            except:
                pass
            sys.exit(1)
        
        # Verificar que la tabla tenga datos
        with source_engine.connect() as conn:
            row_count = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
            print(f"La tabla {table_name} tiene {row_count} filas")
        
        # Iniciar transferencia
        transfer_table(source_engine, target_engine, table_name)
        
        print(f"\n{'='*50}")
        print("PROCESO COMPLETADO EXITOSAMENTE")
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"\nError durante la transferencia: {str(e)}")
        print(f"Tipo de error: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Cerrar conexiones
        if 'source_engine' in locals():
            source_engine.dispose()
        if 'target_engine' in locals():
            target_engine.dispose()
        print("\nConexiones cerradas. Proceso finalizado.")

if __name__ == "__main__":
    main()