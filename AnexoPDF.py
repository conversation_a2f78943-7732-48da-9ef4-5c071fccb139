from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import pandas as pd
import time
import os
import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine



from selenium.webdriver.edge.service import Service
from webdriver_manager.microsoft import EdgeChromiumDriverManager

# Configura el driver automáticamente
service = Service(EdgeChromiumDriverManager().install())
driver = webdriver.Edge(service=service)

# PATH = "C:/Users/<USER>/Desktop/Bot Python/chromedriver3.exe"
# driver = webdriver.Edge(executable_path="C:/Users/<USER>/Desktop/Bot Python/edgedriver_win64/msedgedriver.exe")

# Consulta SQL para obtener los datos de la tabla
consulta_sql = "SELECT [Número de Identificación] FROM TB_HCMFRONT_PASO WHERE [Descriptor de Cargo] IN ('Agente de Servicio','Asistente Técnico','Supervisor Técnico','Coordinador de Despacho','Coordinador Soporte Técnico SME');"

engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server')

# Leer los datos de la tabla en un DataFrame de pandas
Data1 = pd.read_sql_query(consulta_sql, engine)

# Iterar sobre los valores de la columna 'Numero de identificación' 
for identificacion in Data1['Número de Identificación']:
    # Construir la URL con el valor de identificación actual
    url = f"https://appoperaciones.telqway.cl/APP_TQW/dist/PDF_AnexoLiq.php?identificacion={identificacion}"
    
    # Realizar las operaciones necesarias con la URL (por ejemplo, usar Selenium)
    driver.get(url)
    
driver.quit()