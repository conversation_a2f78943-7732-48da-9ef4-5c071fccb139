#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('screenshot_debug.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Función principal para capturar pantallas del sitio"""
    logger.info("=== INICIO DE CAPTURA DE PANTALLAS ===")
    
    driver = None
    try:
        # Verificar si existe msedgedriver.exe en la carpeta actual
        script_dir = os.path.dirname(os.path.abspath(__file__))
        msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")
        
        if not os.path.exists(msedgedriver_path):
            logger.error(f"No se encontró msedgedriver.exe en {script_dir}")
            logger.info("Por favor, ejecute primero descargar_directo.py para obtener el driver")
            return
        
        logger.info(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
        service = Service(msedgedriver_path)
        
        # Inicializar el driver
        driver = webdriver.Edge(service=service)
        logger.info("Driver Edge inicializado correctamente")
        
        # Navegar a la página web
        logger.info("Navegando a la página web...")
        driver.get("http://eps.vtr.cl/eps/declaracion_consumo.do")
        time.sleep(4)
        
        logger.info("Refrescando página...")
        driver.refresh()
        time.sleep(8)
        
        # Capturar pantalla de inicio
        inicio_path = os.path.join(script_dir, "pantalla_inicio.png")
        driver.save_screenshot(inicio_path)
        logger.info(f"Captura de pantalla inicial guardada en: {inicio_path}")
        
        # Login
        logger.info("Iniciando proceso de login...")
        try:
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[1]/td[2]/input"))
            )
            username_field.send_keys("cnawrath")
            logger.info("Username ingresado")
            
            password_field = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[3]/td[2]/input"))
            )
            password_field.send_keys("#Telqway.540")
            logger.info("Password ingresado")
            
            login_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/p/span/a"))
            )
            login_button.click()
            logger.info("Botón de login clickeado")
            
            # Esperar a que cargue la página tras el login
            time.sleep(5)
            
            # Capturar pantalla después del login
            post_login_path = os.path.join(script_dir, "pantalla_post_login.png")
            driver.save_screenshot(post_login_path)
            logger.info(f"Captura de pantalla post-login guardada en: {post_login_path}")
            
            # Capturar HTML de la página
            html_content = driver.page_source
            html_path = os.path.join(script_dir, "pagina_post_login.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"Código HTML guardado en: {html_path}")
            
            # Analizar la estructura para encontrar campos de fecha
            logger.info("Buscando posibles campos de fecha...")
            
            # Lista de posibles selectores para campos de fecha
            date_selectors = [
                "//input[@type='text']",
                "//input[contains(@name, 'fec')]",
                "//input[contains(@name, 'date')]",
                "//input[contains(@id, 'fec')]",
                "//input[contains(@id, 'date')]",
                "//input[contains(@placeholder, 'fecha')]",
                "//input[contains(@class, 'date')]"
            ]
            
            for selector in date_selectors:
                try:
                    date_fields = driver.find_elements(By.XPATH, selector)
                    if date_fields:
                        logger.info(f"Encontrados {len(date_fields)} campos potenciales de fecha con selector: {selector}")
                        for i, field in enumerate(date_fields[:5]):  # Limitar a los primeros 5 para no saturar el log
                            try:
                                field_id = field.get_attribute("id")
                                field_name = field.get_attribute("name")
                                field_class = field.get_attribute("class")
                                field_type = field.get_attribute("type")
                                field_xpath = driver.execute_script("""
                                    function getPathTo(element) {
                                        if (element.id !== '')
                                            return '//*[@id="' + element.id + '"]';
                                        if (element === document.body)
                                            return element.tagName.toLowerCase();
                                        
                                        var ix = 0;
                                        var siblings = element.parentNode.childNodes;
                                        for (var i = 0; i < siblings.length; i++) {
                                            var sibling = siblings[i];
                                            if (sibling === element)
                                                return getPathTo(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                                            if (sibling.nodeType === 1 && sibling.tagName === element.tagName)
                                                ix++;
                                        }
                                    }
                                    return getPathTo(arguments[0]);
                                """, field)
                                
                                logger.info(f"  Campo {i+1}: id='{field_id}', name='{field_name}', class='{field_class}', type='{field_type}'")
                                logger.info(f"  XPath generado: {field_xpath}")
                                
                                # Resaltar el campo en la página y tomar captura
                                driver.execute_script("arguments[0].style.border='3px solid red'", field)
                                field_screenshot = os.path.join(script_dir, f"campo_fecha_{i+1}.png")
                                driver.save_screenshot(field_screenshot)
                                logger.info(f"  Captura del campo guardada en: {field_screenshot}")
                                driver.execute_script("arguments[0].style.border=''", field)
                            except Exception as e:
                                logger.error(f"Error analizando campo {i+1}: {str(e)}")
                except Exception as e:
                    logger.warning(f"Error con selector {selector}: {str(e)}")
            
            # Identificar todos los formularios en la página
            forms = driver.find_elements(By.TAG_NAME, "form")
            logger.info(f"Encontrados {len(forms)} formularios en la página")
            for i, form in enumerate(forms):
                try:
                    form_id = form.get_attribute("id")
                    form_name = form.get_attribute("name")
                    form_action = form.get_attribute("action")
                    logger.info(f"Formulario {i+1}: id='{form_id}', name='{form_name}', action='{form_action}'")
                    
                    # Resaltar el formulario y tomar captura
                    driver.execute_script("arguments[0].style.border='3px solid blue'", form)
                    form_screenshot = os.path.join(script_dir, f"formulario_{i+1}.png")
                    driver.save_screenshot(form_screenshot)
                    logger.info(f"Captura del formulario guardada en: {form_screenshot}")
                    driver.execute_script("arguments[0].style.border=''", form)
                except Exception as e:
                    logger.error(f"Error analizando formulario {i+1}: {str(e)}")
            
        except Exception as e:
            logger.error(f"Error durante la captura: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
    except Exception as e:
        logger.error(f"Error general: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        if driver:
            driver.quit()
            logger.info("Driver cerrado")
        
        logger.info("=== FIN DE CAPTURA DE PANTALLAS ===")

if __name__ == "__main__":
    main()