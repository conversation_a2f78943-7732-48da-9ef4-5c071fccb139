"""
Script de debugging para reproducir el problema exacto del usuario
"""

from playwright.sync_api import sync_playwright
import time

def debug_oracle_issue():
    """Debug del problema específico en el paso 4"""
    
    with sync_playwright() as p:
        # Configurar navegador exactamente igual que el bot
        browser = p.chromium.launch(
            headless=False, 
            slow_mo=1500
        )
        
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        page = context.new_page()
        
        try:
            print("🔍 DEBUGGING: Reproduciendo el problema del paso 4...")
            
            # Paso 1: Navegar
            print("1️⃣ Navegando a Oracle Cloud...")
            page.goto("https://vtr.fs.ocs.oraclecloud.com/", wait_until='networkidle')
            print(f"✅ Página: {page.title()}")
            time.sleep(3)
            
            # Limpiar sesiones
            print("🧹 Limpiando cookies y sesiones...")
            context.clear_cookies()
            page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
            
            # Paso 2: SSO
            print("2️⃣ Haciendo clic en SSO...")
            sso_button = page.wait_for_selector("button:has-text('Conectarse con SSO')", timeout=10000)
            sso_button.click()
            page.wait_for_load_state('networkidle')
            print(f"✅ Después de SSO: {page.url}")
            time.sleep(3)
            
            # Paso 3: DEBUGGING del campo de usuario
            print("🔍 DEBUGGING: Inspeccionando página después de SSO...")
            print(f"URL actual: {page.url}")
            print(f"Título: {page.title()}")
            
            # Verificar si estamos en página de logout
            page_content = page.content()
            if "Cerró la sesión" in page_content or "logout" in page.url.lower():
                print("⚠️ PROBLEMA DETECTADO: Página de logout detectada")
                print("🔄 Navegando de nuevo...")
                page.goto("https://vtr.fs.ocs.oraclecloud.com/")
                page.wait_for_load_state('networkidle')
                sso_button = page.wait_for_selector("button:has-text('Conectarse con SSO')")
                sso_button.click()
                page.wait_for_load_state('networkidle')
            
            # Tomar captura antes de buscar el campo
            page.screenshot(path="debug_before_username_search.png")
            print("📸 Captura tomada: debug_before_username_search.png")
            
            # Buscar TODOS los inputs disponibles
            print("🔍 Buscando TODOS los inputs disponibles...")
            all_inputs = page.locator("input")
            input_count = all_inputs.count()
            print(f"📊 Total de inputs encontrados: {input_count}")
            
            for i in range(input_count):
                input_elem = all_inputs.nth(i)
                try:
                    placeholder = input_elem.get_attribute("placeholder") or "Sin placeholder"
                    input_type = input_elem.get_attribute("type") or "Sin type"
                    input_id = input_elem.get_attribute("id") or "Sin ID"
                    input_name = input_elem.get_attribute("name") or "Sin name"
                    is_visible = input_elem.is_visible()
                    
                    print(f"  Input {i+1}: type='{input_type}', placeholder='{placeholder}', id='{input_id}', name='{input_name}', visible={is_visible}")
                except Exception as e:
                    print(f"  Input {i+1}: Error al inspeccionar - {e}")
            
            # Intentar encontrar el campo de usuario con el selector del error
            print("\\n🎯 Probando el selector exacto del error...")
            try:
                selector = "input[placeholder*='Nombre de usuario'], input[type='text'], textbox"
                username_input = page.wait_for_selector(selector, timeout=5000)
                if username_input:
                    print("✅ Campo encontrado con selector del error")
                    placeholder = username_input.get_attribute("placeholder")
                    print(f"   Placeholder: {placeholder}")
                    
                    # Intentar llenar
                    username_input.fill("ncornejoh")
                    print("✅ Campo llenado exitosamente")
                else:
                    print("❌ Campo NO encontrado con selector del error")
            except Exception as e:
                print(f"❌ ERROR con selector del error: {e}")
            
            # Buscar botón continuar
            print("\\n🔍 Buscando botón 'Continuar con SSO'...")
            try:
                continue_button = page.wait_for_selector("button:has-text('Continuar con SSO')", timeout=5000)
                if continue_button:
                    is_disabled = continue_button.is_disabled()
                    print(f"✅ Botón encontrado - Deshabilitado: {is_disabled}")
                else:
                    print("❌ Botón NO encontrado")
            except Exception as e:
                print(f"❌ ERROR buscando botón: {e}")
            
            # Tomar captura final
            page.screenshot(path="debug_final_state.png")
            print("📸 Captura final: debug_final_state.png")
            
            input("\\nPresiona Enter para cerrar y revisar las capturas...")
            
        except Exception as e:
            print(f"❌ ERROR GENERAL: {e}")
            page.screenshot(path="debug_error.png")
            
        finally:
            browser.close()

if __name__ == "__main__":
    debug_oracle_issue()