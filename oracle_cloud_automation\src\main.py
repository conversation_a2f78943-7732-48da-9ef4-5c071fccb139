"""
Main entry point for Oracle Cloud automation script.
Orchestrates web automation, file handling, and database operations.
"""

import time
import logging
import signal
import sys
import datetime
from typing import Optional

import sys
import os

# Añadir el directorio src al path de Python
src_dir = os.path.dirname(os.path.dirname(__file__))
if src_dir not in sys.path:
    sys.path.append(src_dir)

from src.config import LOGGING_CONFIG, TIMING_CONFIG, BUSINESS_HOURS_CONFIG
from src.web_automation import OracleWebAutomation
from src.utils.file_utils import find_latest_excel_file, wait_for_download
from src.utils.database import upload_excel_to_sql_server

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file'], mode=LOGGING_CONFIG.get('mode', 'a'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class OracleAutomationController:
    """Main controller for Oracle Cloud automation workflow."""

    def __init__(self):
        self.automation = None
        self.cycle_count = 0
        self.first_file_timestamp = None
        self.running = True
        self.consecutive_errors = 0  # Counter for consecutive failed cycles

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"🛑 Received signal {signum}. Initiating graceful shutdown...")
        self.running = False
        if self.automation:
            self.automation.close()

    def _is_business_hours(self) -> bool:
        """
        Check if current time is within business hours.

        Returns:
            bool: True if within business hours, False otherwise
        """
        now = datetime.datetime.now()
        current_hour = now.hour
        start_hour = BUSINESS_HOURS_CONFIG['start_hour']
        end_hour = BUSINESS_HOURS_CONFIG['end_hour']
        return start_hour <= current_hour < end_hour

    def _get_seconds_until_business_hours(self) -> int:
        """
        Calculate seconds until next business hours start.

        Returns:
            int: Seconds until next business hours start
        """
        now = datetime.datetime.now()
        start_hour = BUSINESS_HOURS_CONFIG['start_hour']

        # If we're past today's start time, wait until tomorrow
        if now.hour >= start_hour:
            tomorrow = now + datetime.timedelta(days=1)
            next_business_start = datetime.datetime(tomorrow.year, tomorrow.month, tomorrow.day, start_hour, 0, 0)
        else:
            # Still today, wait until today's start time
            next_business_start = datetime.datetime(now.year, now.month, now.day, start_hour, 0, 0)

        time_diff = next_business_start - now
        return int(time_diff.total_seconds())

    def _handle_outside_business_hours(self):
        """Handle operations when outside business hours."""
        if self.automation:
            logger.info("🕘 Outside business hours - closing webdriver...")
            self.automation.close()
            self.automation = None

        seconds_until_business = self._get_seconds_until_business_hours()
        logger.info(f"⏰ Sleeping until 8am (in {seconds_until_business} seconds)...")
        time.sleep(seconds_until_business)

        # Reinitialize automation after waking up
        logger.info("🌅 Business hours started - reinitializing automation...")
        if not self.initialize_automation():
            logger.error("❌ Failed to reinitialize automation after business hours")
            self.running = False

    def run_automation_cycle(self) -> bool:
        """
        Run a single automation cycle using unified VTR button (all zones).

        Returns:
            bool: True if cycle completed successfully, False otherwise
        """
        try:
            self.cycle_count += 1
            cycle_start_time = time.time()

            logger.info("=" * 100)
            logger.info(f"🚀 STARTING CYCLE #{self.cycle_count} - {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Log business hours status
            start_hour = BUSINESS_HOURS_CONFIG['start_hour']
            end_hour = BUSINESS_HOURS_CONFIG['end_hour']
            if self._is_business_hours():
                logger.info(f"🕐 Within business hours ({start_hour}am-{end_hour}pm)")
            else:
                logger.info("🌙 Outside business hours")

            logger.info("=" * 100)
            logger.info("📊 Cycle: Unified VTR button (all zones)")
            logger.info("⏹️  Press Ctrl+C to stop the permanent cycle")
            logger.info("=" * 100)

            # Click unified VTR button (includes all zones)
            if not self._click_vtr_unified_button():
                return False

            # Process unified data export and upload
            if not self._process_unified_data():
                return False

            # Complete cycle
            cycle_duration = time.time() - cycle_start_time
            logger.info("=" * 80)
            logger.info(f"✅ CYCLE #{self.cycle_count} COMPLETED - Duration: {cycle_duration:.2f}s")
            logger.info("=" * 80)
            logger.info("📊 Cycle Summary:")
            logger.info("   • VTR Unified: All zones processed and integrated (APPEND mode)")
            logger.info(f"   • Total cycles completed: {self.cycle_count}")
            logger.info("=" * 80)
            logger.info(f"⏳ Waiting {TIMING_CONFIG['cycle_pause']} seconds before next cycle...")
            logger.info("=" * 80)

            time.sleep(TIMING_CONFIG['cycle_pause'])

            logger.info("🔄" * 40)
            logger.info("RESTARTING CYCLE - USING UNIFIED VTR BUTTON")
            logger.info("🔄" * 40)

            return True

        except Exception as cycle_error:
            cycle_duration = time.time() - cycle_start_time
            logger.error("=" * 50)
            logger.error(f"❌ ERROR IN CYCLE #{self.cycle_count} - Duration: {cycle_duration:.2f}s")
            logger.error("=" * 50)
            logger.error(f"Error: {str(cycle_error)}")
            logger.error("📊 Cycle Statistics:")
            logger.error(f"   • Completed cycles: {self.cycle_count - 1}")
            logger.error(f"   • Failed cycle duration: {cycle_duration:.2f}s")
            logger.error(f"🔄 Cycle will retry in {TIMING_CONFIG['error_retry_delay']} seconds...")
            logger.error("=" * 50)

            time.sleep(TIMING_CONFIG['error_retry_delay'])
            return False

    def _click_vtr_unified_button(self) -> bool:
        """
        Click the unified VTR button that includes all zones.

        Returns:
            bool: True if VTR button click successful, False otherwise
        """
        try:
            logger.info("🎯 Clicking unified VTR button (all zones)...")

            if not self.automation.click_vtr_button():
                logger.error("❌ Failed to click unified VTR button")
                return False

            logger.info("✅ Unified VTR button clicked successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error clicking VTR button: {str(e)}")
            return False

    def _process_unified_data(self) -> bool:
        """
        Process unified data extraction and upload for all zones.

        Returns:
            bool: True if processing successful, False otherwise
        """
        try:
            logger.info("🏭 Processing unified data (all zones)...")

            # Perform export actions
            if not self.automation.perform_export_actions():
                logger.error("❌ Failed to perform export actions")
                # Debug: Show available elements on the page
                logger.info("🔍 Debugging page elements to find Exportar button...")
                self.automation.debug_page_elements("Exportar")
                return False

            # Wait for download with increased timeout for larger files
            logger.info("⏳ Waiting for Excel download (unified data - larger file)...")
            excel_file = wait_for_download(timeout_seconds=180)

            if not excel_file:
                logger.error("❌ No Excel file downloaded")
                return False

            # Wait additional time for larger file processing
            logger.info(f"⏳ Waiting {TIMING_CONFIG['download_wait']} seconds for file processing...")
            time.sleep(TIMING_CONFIG['download_wait'])

            # Upload to database using staging approach
            logger.info("📤 Uploading unified data to SQL Server (staging approach)...")
            success = upload_excel_to_sql_server(
                excel_file,
                table_name='tb_toa_reporte_diario',
                mode='append',  # Historical table will accumulate data
                use_staging_approach=True  # Use staging table + SP approach
            )

            if success:
                logger.info("✅ Unified data processing completed successfully")
                return True
            else:
                logger.error("❌ Failed to upload unified data to database")
                return False

        except Exception as e:
            logger.error(f"❌ Error processing unified data: {str(e)}")
            return False


    def _restart_automation(self) -> bool:
        """
        Restart the automation by closing current instance and reinitializing with cleanup.

        Returns:
            bool: True if restart successful, False otherwise
        """
        try:
            logger.info("🔄 Restarting automation (closing browser and logging in again)...")

            # Step 1: Close current automation gracefully
            if self.automation:
                logger.info("   🧹 Closing current automation instance...")
                self.automation.close()
                self.automation = None

            # Step 2: Add delay to ensure complete cleanup
            logger.info("   ⏳ Waiting for system cleanup...")
            time.sleep(5)  # Increased from 3 to 5 seconds

            # Step 3: Force garbage collection to free memory
            import gc
            gc.collect()
            logger.info("   🗑️ Memory cleanup completed")

            # Step 4: Reinitialize automation (includes login)
            logger.info("   🚀 Reinitializing automation...")
            if not self.initialize_automation():
                logger.error("❌ Failed to restart automation")
                return False

            logger.info("✅ Automation restarted successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error restarting automation: {str(e)}")
            return False

    def initialize_automation(self) -> bool:
        """
        Initialize the web automation system.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            logger.info("🚀 Initializing Oracle Cloud automation...")

            # Create automation instance
            self.automation = OracleWebAutomation()

            # Setup WebDriver
            if not self.automation.setup_driver():
                logger.error("❌ Failed to setup WebDriver")
                return False

            # Navigate to Oracle Cloud
            if not self.automation.navigate_to_oracle():
                logger.error("❌ Failed to navigate to Oracle Cloud")
                return False

            # Check for existing session
            if self.automation.check_existing_session():
                logger.info("✅ Active session detected - skipping login")
            else:
                # Perform SSO login
                if not self.automation.perform_sso_login():
                    logger.error("❌ SSO login failed")
                    return False

            # Always interact with elId50 after login (whether new or existing session)
            logger.info("🎯 Interacting with elId50 element...")
            if not self.automation.interact_with_elid50():
                logger.error("❌ Failed to interact with elId50 element")
                return False

            logger.info("✅ Oracle Cloud automation initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Initialization failed: {str(e)}")
            return False

    def run_permanent_cycle(self):
        """Run the permanent automation cycle until stopped."""
        try:
            logger.info("=" * 80)
            logger.info(f"INICIANDO SCRIPT DE NAVEGACIÓN A ORACLE CLOUD - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("=" * 80)

            # Initialize automation
            if not self.initialize_automation():
                logger.error("❌ Failed to initialize automation")
                return

            # Run permanent cycle
            while self.running:
                # Check if within business hours
                if not self._is_business_hours():
                    self._handle_outside_business_hours()
                    continue  # Skip to next iteration after reinitialization

                # Ensure automation is initialized
                if not self.automation:
                    logger.info("🔄 Reinitializing automation...")
                    if not self.initialize_automation():
                        logger.error("❌ Failed to reinitialize automation")
                        self.running = False
                        break

                if not self.run_automation_cycle():
                    logger.warning("⚠️ Cycle failed, but continuing...")
                    self.consecutive_errors += 1
                    logger.warning(f"⚠️ Consecutive errors: {self.consecutive_errors}/3")

                    # Check if we need to restart automation
                    if self.consecutive_errors >= 3:
                        logger.warning("🚨 3 consecutive errors detected - restarting browser and login...")
                        if self._restart_automation():
                            logger.info("✅ Automation restarted successfully")
                            self.consecutive_errors = 0  # Reset counter after successful restart
                        else:
                            logger.error("❌ Failed to restart automation - stopping script")
                            self.running = False
                            break
                else:
                    # Reset consecutive errors counter on successful cycle
                    if self.consecutive_errors > 0:
                        logger.info(f"✅ Cycle successful - resetting error counter (was {self.consecutive_errors})")
                        self.consecutive_errors = 0

        except KeyboardInterrupt:
            logger.info("🛑 Script stopped by user")
        except Exception as e:
            logger.error(f"❌ Fatal error in permanent cycle: {str(e)}")
        finally:
            self.cleanup()

    def cleanup(self):
        """Clean up resources and provide final summary."""
        try:
            end_time = time.time()

            logger.info("=" * 80)
            logger.info(f"SCRIPT FINALIZADO - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info("=" * 80)

            if self.automation:
                logger.info("🛑 Closing browser...")
                self.automation.close()

            logger.info("✅ Cleanup completed")
            logger.info("💡 Browser will remain closed for manual inspection if needed")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {str(e)}")

def main():
    """Main entry point function."""
    try:
        # Create controller and run automation
        controller = OracleAutomationController()
        controller.run_permanent_cycle()

    except Exception as e:
        logger.error(f"❌ Fatal error in main: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()