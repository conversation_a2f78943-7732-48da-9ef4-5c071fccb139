"""
Script mejorado para Oracle Cloud con manejo correcto de navegaciones
"""

from playwright.sync_api import sync_playwright
import time

def oracle_login_with_navigation_handling():
    """Login completo con manejo correcto de navegaciones"""
    
    with sync_playwright() as p:
        # Configurar navegador
        browser = p.chromium.launch(headless=False, slow_mo=1500)
        page = browser.new_page()
        
        try:
            print("🚀 Iniciando proceso de login con manejo de navegaciones...")
            
            # Paso 1: Navegar a Oracle Cloud
            print("1️⃣ Navegando a Oracle Cloud...")
            page.goto("https://vtr.fs.ocs.oraclecloud.com/")
            page.wait_for_load_state('networkidle')
            print(f"✅ Página inicial: {page.title()}")
            
            # Paso 2: Hacer clic en "Conectarse con SSO"
            print("2️⃣ Haciendo clic en 'Conectarse con SSO'...")
            sso_button = page.locator("button:has-text('Conectarse con SSO')")\n            sso_button.click()\n            page.wait_for_load_state('networkidle')\n            print("✅ Botón SSO clickeado")\n            \n            # Paso 3: Llenar usuario\n            print("3️⃣ Llenando usuario 'ncornejoh'...")\n            username_input = page.locator("input[type='text'], textbox").first\n            username_input.fill("ncornejoh")\n            print("✅ Usuario ingresado")\n            \n            # Paso 4: Continuar con SSO (CON MANEJO DE NAVEGACIÓN)\n            print("4️⃣ Haciendo clic en 'Continuar con SSO' - ESPERANDO REDIRECCIÓN...")\n            continue_button = page.locator("button:has-text('Continuar con SSO')")\n            \n            # ESTE ES EL PASO CRÍTICO - MANEJAR LA NAVEGACIÓN\n            try:\n                with page.expect_navigation(timeout=30000):\n                    continue_button.click()\n                print("✅ Redirección a Microsoft Login detectada")\n            except Exception as e:\n                print(f"⚠️ Navegación no detectada inmediatamente: {e}")\n                continue_button.click()\n                time.sleep(3)\n            \n            page.wait_for_load_state('networkidle')\n            print(f"✅ Nueva página: {page.url}")\n            \n            # Paso 5: Llenar email en Microsoft Login\n            print("5️⃣ Llenando email '<EMAIL>'...")\n            email_input = page.locator("input").first\n            email_input.fill("<EMAIL>")\n            print("✅ Email ingresado")\n            \n            # Paso 6: Hacer clic en siguiente (CON MANEJO DE NAVEGACIÓN)\n            print("6️⃣ Haciendo clic en 'Siguiente' - ESPERANDO NAVEGACIÓN...")\n            next_button = page.locator("button:has-text('Siguiente'), button[type='submit']").first\n            \n            try:\n                with page.expect_navigation(timeout=30000):\n                    next_button.click()\n                print("✅ Navegación a página de contraseña detectada")\n            except Exception as e:\n                print(f"⚠️ Navegación no detectada: {e}")\n                next_button.click()\n                time.sleep(3)\n            \n            page.wait_for_load_state('networkidle')\n            print(f"✅ Página de contraseña: {page.url}")\n            \n            # Paso 7: Llenar contraseña\n            print("7️⃣ Llenando contraseña...")\n            password_input = page.locator("input[type='password'], input").first\n            password_input.fill("Telqway.202517")\n            print("✅ Contraseña ingresada")\n            \n            # Paso 8: Iniciar sesión (CON MANEJO DE NAVEGACIÓN)\n            print("8️⃣ Haciendo clic en 'Iniciar sesión' - ESPERANDO AUTENTICACIÓN...")\n            login_button = page.locator("button:has-text('Iniciar sesión'), button[type='submit']").first\n            \n            try:\n                with page.expect_navigation(timeout=30000):\n                    login_button.click()\n                print("✅ Autenticación exitosa - navegación detectada")\n            except Exception as e:\n                print(f"⚠️ Navegación no detectada: {e}")\n                login_button.click()\n                time.sleep(3)\n            \n            page.wait_for_load_state('networkidle')\n            print(f"✅ Después del login: {page.url}")\n            \n            # Paso 9: Manejar "Mantener sesión iniciada"\n            keep_signed_button = page.locator("button:has-text('Sí'), button:has-text('Yes')")\n            if keep_signed_button.is_visible():\n                print("9️⃣ Respondiendo 'Sí' a 'Mantener sesión iniciada'...")\n                try:\n                    with page.expect_navigation(timeout=30000):\n                        keep_signed_button.click()\n                    print("✅ Redirección final a Oracle detectada")\n                except Exception as e:\n                    print(f"⚠️ Navegación final no detectada: {e}")\n                    keep_signed_button.click()\n                    time.sleep(5)\n                \n                page.wait_for_load_state('networkidle')\n            \n            # Resultado final\n            print("\\n🎉 ¡LOGIN COMPLETADO EXITOSAMENTE!")\n            print(f"📄 URL final: {page.url}")\n            print(f"📋 Título: {page.title()}")\n            \n            # Verificar si estamos logueados\n            if "vtr.fs.ocs.oraclecloud.com" in page.url and "despacho" in page.title().lower():\n                print("✅ CONFIRMADO: Login exitoso en Oracle Field Service")\n            else:\n                print("⚠️ Login posiblemente incompleto")\n            \n            # Tomar captura final\n            page.screenshot(path="oracle_login_success_fixed.png")\n            print("📸 Captura guardada: oracle_login_success_fixed.png")\n            \n            input("\\nPresiona Enter para cerrar...")\n            \n        except Exception as e:\n            print(f"❌ Error: {e}")\n            page.screenshot(path="oracle_error_fixed.png")\n            print("📸 Captura de error guardada: oracle_error_fixed.png")\n            \n        finally:\n            browser.close()\n\nif __name__ == "__main__":\n    oracle_login_with_navigation_handling()