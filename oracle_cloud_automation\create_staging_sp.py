#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the staging stored procedure in SQL Server.
Run this script once to set up the SP before using the staging approach.
"""

import pyodbc
import logging
import sys
import os

# Add src directory to Python path
src_dir = os.path.join(os.path.dirname(__file__), 'src')
if src_dir not in sys.path:
    sys.path.append(src_dir)

from config import SQL_SERVER_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_stored_procedure():
    """Create the stored procedure for staging to historical table migration."""

    try:
        # Connect to SQL Server
        conn_str = (
            f"DRIVER={{{SQL_SERVER_CONFIG['driver']}}};"
            f"SERVER={SQL_SERVER_CONFIG['server']};"
            f"DATABASE={SQL_SERVER_CONFIG['database']};"
            f"UID={SQL_SERVER_CONFIG['uid']};"
            f"PWD={SQL_SERVER_CONFIG['pwd']};"
        )

        logger.info("Connecting to SQL Server...")
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        logger.info("Executing staging SP creation script...")

        # Read and execute the SQL file
        sql_file_path = os.path.join(os.path.dirname(__file__), 'staging_sp.sql')
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()

        # Split the script into individual statements
        statements = sql_script.split('GO')

        for i, statement in enumerate(statements):
            statement = statement.strip()
            if statement:  # Skip empty statements
                try:
                    logger.info(f"Executing statement {i+1}/{len(statements)}...")
                    cursor.execute(statement)
                    conn.commit()
                except Exception as stmt_error:
                    logger.warning(f"Statement {i+1} failed: {str(stmt_error)}")
                    # Continue with next statement

        logger.info("✅ Stored procedure creation completed!")
        logger.info("📋 Created:")
        logger.info("   • sp_insert_from_staging_to_tb_toa_reporte_diario")
        logger.info("   • audit_log table (if not exists)")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"❌ Failed to create stored procedure: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    logger.info("🚀 Starting stored procedure creation...")
    success = create_stored_procedure()

    if success:
        logger.info("✅ Setup completed successfully!")
        logger.info("💡 You can now run the main automation script with staging approach.")
    else:
        logger.error("❌ Setup failed. Please check the logs above.")