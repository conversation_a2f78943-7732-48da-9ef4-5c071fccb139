2025-06-03 20:51:11,486 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_c
onfig.json
2025-06-03 20:53:56,289 - INFO - Ejecutando manualmente: Py_INSERT_SQL_SERVER
2025-06-03 20:53:56,290 - INFO - Iniciando tarea: Py_INSERT_SQL_SERVER
2025-06-03 20:53:56,290 - INFO - Ejecutando: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\\Users\\<USER>\\N
extcloud\\03 Reportes Operaciones\\02FlujosDatos\Py_INSERT_SQL_SERVER.py
2025-06-03 20:53:56,293 - INFO - Comando completo: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\\Users\\<USER>\\Nextcloud\\03 Reportes Operaciones\\02FlujosDatos\Py_INSERT_SQL_SERVER.py
2025-06-03 20:53:58,310 - INFO - Tarea Py_INSERT_SQL_SERVER iniciada correctamente (PID: 316)
2025-06-03 20:53:58,310 - INFO - Tarea Py_INSERT_SQL_SERVER iniciada con éxito (2.02 segundos)
2025-06-03 20:53:58,310 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_c
onfig.json
2025-06-03 20:53:59,435 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:228: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 20:53:59,435 - WARNING - [Py_INSERT_SQL_SERVER] df3.to_sql('tb_paso_pyNdc', engine, if_exists='replace', index=False)
2025-06-03 20:53:59,435 - WARNING - [Py_INSERT_SQL_SERVER] Traceback (most recent call last):
2025-06-03 20:53:59,435 - INFO - [Py_INSERT_SQL_SERVER] Iniciando la carga de datos...
2025-06-03 20:53:59,435 - WARNING - [Py_INSERT_SQL_SERVER] File "c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_IN
SERT_SQL_SERVER.py", line 228, in <module>
2025-06-03 20:53:59,450 - INFO - [Py_INSERT_SQL_SERVER] Leyendo archivos Excel...
2025-06-03 20:53:59,450 - WARNING - [Py_INSERT_SQL_SERVER] df3.to_sql('tb_paso_pyNdc', engine, if_exists='replace', index=False)
2025-06-03 20:53:59,450 - INFO - [Py_INSERT_SQL_SERVER] Archivos Excel cargados correctamente en DataFrames.
2025-06-03 20:53:59,450 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\util\_decorators.py", line 333, in wrapper
2025-06-03 20:53:59,450 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos en SQL Server...
2025-06-03 20:53:59,450 - WARNING - [Py_INSERT_SQL_SERVER] return func(*args, **kwargs)
2025-06-03 20:53:59,467 - INFO - [Py_INSERT_SQL_SERVER] Error durante la ejecución: 'Engine' object has no attribute 'cursor'
2025-06-03 20:53:59,475 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\core\generic.py", line 3087, in to_sql
2025-06-03 20:53:59,478 - WARNING - [Py_INSERT_SQL_SERVER] return sql.to_sql(
2025-06-03 20:53:59,481 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 842, in to_sql
2025-06-03 20:53:59,484 - WARNING - [Py_INSERT_SQL_SERVER] return pandas_sql.to_sql(
2025-06-03 20:53:59,487 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 2850, in to_sql
2025-06-03 20:53:59,492 - WARNING - [Py_INSERT_SQL_SERVER] table.create()
2025-06-03 20:53:59,494 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 984, in create
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] if self.exists():
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 970, in exists
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] return self.pd_sql.has_table(self.name, self.schema)
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 2865, in has_table
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] return len(self.execute(query, [name]).fetchall()) > 0
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-pack
ages\pandas\io\sql.py", line 2672, in execute
2025-06-03 20:53:59,497 - WARNING - [Py_INSERT_SQL_SERVER] cur = self.con.cursor()
2025-06-03 20:53:59,515 - WARNING - [Py_INSERT_SQL_SERVER] AttributeError: 'Engine' object has no attribute 'cursor'
2025-06-03 20:53:59,569 - INFO - Proceso Py_INSERT_SQL_SERVER completado exitosamente. Tiempo de ejecución: 3.27 segundos
2025-06-03 20:53:59,569 - INFO - Salida de Py_INSERT_SQL_SERVER: Iniciando la carga de datos...
Leyendo archivos Excel...
Archivos Excel cargados correctamente en DataFrames.
Insertando datos en SQL Server...
Error durante la ejecución: 'Engine' object has no attribute 'cursor'
2025-06-03 20:53:59,585 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_c
onfig.json
2025-06-03 21:02:26,857 - INFO - Ejecutando manualmente: Py_INSERT_SQL_SERVER
2025-06-03 21:02:26,857 - INFO - Iniciando tarea: Py_INSERT_SQL_SERVER
2025-06-03 21:02:26,858 - INFO - Ejecutando: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\\Users\\<USER>\\N
extcloud\\03 Reportes Operaciones\\02FlujosDatos\Py_INSERT_SQL_SERVER.py
2025-06-03 21:02:26,869 - INFO - Comando completo: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\\Users\\<USER>\\Nextcloud\\03 Reportes Operaciones\\02FlujosDatos\Py_INSERT_SQL_SERVER.py
2025-06-03 21:02:28,880 - INFO - Tarea Py_INSERT_SQL_SERVER iniciada correctamente (PID: 35156)
2025-06-03 21:02:28,880 - INFO - Tarea Py_INSERT_SQL_SERVER iniciada con éxito (2.02 segundos)
2025-06-03 21:02:28,880 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_c
onfig.json
2025-06-03 21:03:07,946 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:62: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 c
onnection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:07,946 - WARNING - [Py_INSERT_SQL_SERVER] df_problematico = pd.read_sql_query(query_analisis, sql_conn)
2025-06-03 21:03:15,113 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:383: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:15,113 - WARNING - [Py_INSERT_SQL_SERVER] Data = pd.read_sql_query("SELECT * from ProduccionNDC WHERE FORMAT(mes_contab
le,'yyyyMM') >= '202410'", sql_conn)
2025-06-03 21:03:20,017 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:386: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:20,017 - WARNING - [Py_INSERT_SQL_SERVER] Data2 = pd.read_sql_query("SELECT * FROM TB_TQW_COMISION_2023", sql_conn)
2025-06-03 21:03:20,069 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:395: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:20,069 - WARNING - [Py_INSERT_SQL_SERVER] Data3 = pd.read_sql_query(consulta_sql, sql_conn)
2025-06-03 21:03:20,069 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:398: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:20,069 - WARNING - [Py_INSERT_SQL_SERVER] Data4 = pd.read_sql_query(consulta_sql2, sql_conn)
2025-06-03 21:03:20,085 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:401: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:20,085 - WARNING - [Py_INSERT_SQL_SERVER] Data5 = pd.read_sql_query(consulta_comisiones_new, sql_conn)
2025-06-03 21:03:20,085 - WARNING - [Py_INSERT_SQL_SERVER] c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\Py_INSERT_S
QL_SERVER.py:407: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2
connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
2025-06-03 21:03:20,085 - WARNING - [Py_INSERT_SQL_SERVER] vw_CalidadFlujo_kpi = pd.read_sql_query(consulta_calidad_flujo, sql_conn)
2025-06-03 21:04:07,527 - INFO - [Py_INSERT_SQL_SERVER] Iniciando la carga de datos...
2025-06-03 21:04:07,543 - INFO - [Py_INSERT_SQL_SERVER] Conectando a SQL Server...
2025-06-03 21:04:07,543 - INFO - [Py_INSERT_SQL_SERVER] Leyendo archivos Excel...
2025-06-03 21:04:07,543 - INFO - [Py_INSERT_SQL_SERVER] Archivos Excel cargados correctamente en DataFrames.
2025-06-03 21:04:07,559 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos en SQL Server...
2025-06-03 21:04:07,576 - INFO - [Py_INSERT_SQL_SERVER] Truncando tabla tb_paso_pyNdc...
2025-06-03 21:04:07,579 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos de Metro...
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Insertando 1418 filas en tb_paso_pyNdc en 2 lotes...
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/2 insertado en tb_paso_pyNdc
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Lote 2/2 insertado en tb_paso_pyNdc
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos de Sur...
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Insertando 64 filas en tb_paso_pyNdc en 1 lotes...
2025-06-03 21:04:07,581 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 insertado en tb_paso_pyNdc
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos de Norte...
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Insertando 24 filas en tb_paso_pyNdc en 1 lotes...
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 insertado en tb_paso_pyNdc
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos de Centro...
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Insertando 762 filas en tb_paso_pyNdc en 1 lotes...
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 insertado en tb_paso_pyNdc
2025-06-03 21:04:07,597 - INFO - [Py_INSERT_SQL_SERVER] Ejecutando procedimientos almacenados en SQL Server...
2025-06-03 21:04:07,615 - INFO - [Py_INSERT_SQL_SERVER] SP_INSERT_NDC ejecutado exitosamente
2025-06-03 21:04:07,618 - INFO - [Py_INSERT_SQL_SERVER] Ejecutando procedimientos almacenados adicionales...
2025-06-03 21:04:07,620 - INFO - [Py_INSERT_SQL_SERVER] SP_CREATE_NDC_PRODUCCION '202506' ejecutado exitosamente
2025-06-03 21:04:07,621 - INFO - [Py_INSERT_SQL_SERVER]
2025-06-03 21:04:07,621 - INFO - [Py_INSERT_SQL_SERVER] ============================================================
2025-06-03 21:04:07,621 - INFO - [Py_INSERT_SQL_SERVER] 🔬 INICIANDO ANÁLISIS RGU PROBLEMÁTICO
2025-06-03 21:04:07,621 - INFO - [Py_INSERT_SQL_SERVER] ============================================================
2025-06-03 21:04:07,824 - INFO - [Py_INSERT_SQL_SERVER] 📊 Iniciando análisis RGU problemático para período 202506...
2025-06-03 21:04:07,824 - INFO - [Py_INSERT_SQL_SERVER] 🔍 Ejecutando query de análisis en SQL Server...
2025-06-03 21:04:07,840 - INFO - [Py_INSERT_SQL_SERVER] ✅ Encontrados 7 registros RGU problemático
2025-06-03 21:04:07,856 - INFO - [Py_INSERT_SQL_SERVER] 📋 Resumen por Tipo de Red:
2025-06-03 21:04:07,874 - INFO - [Py_INSERT_SQL_SERVER] - FTTH: 6 registros
2025-06-03 21:04:07,877 - INFO - [Py_INSERT_SQL_SERVER] - HFC: 1 registros
2025-06-03 21:04:07,893 - INFO - [Py_INSERT_SQL_SERVER] 🗑️  Limpiando registros anteriores de análisis problemático...
2025-06-03 21:04:07,893 - INFO - [Py_INSERT_SQL_SERVER] 🗑️  Eliminados 4 registros anteriores de análisis problemático
2025-06-03 21:04:07,917 - INFO - [Py_INSERT_SQL_SERVER] ✅ Análisis RGU problemático completado exitosamente
2025-06-03 21:04:07,917 - INFO - [Py_INSERT_SQL_SERVER] 📊 Total registros procesados: 7
2025-06-03 21:04:07,917 - INFO - [Py_INSERT_SQL_SERVER] 🎯 Período: 202506
2025-06-03 21:04:07,932 - INFO - [Py_INSERT_SQL_SERVER] ⏰ Procesado: 2025-06-03 21:03:08
2025-06-03 21:04:07,954 - INFO - [Py_INSERT_SQL_SERVER] ✅ Análisis RGU problemático completado exitosamente
2025-06-03 21:04:07,954 - INFO - [Py_INSERT_SQL_SERVER] ============================================================
2025-06-03 21:04:07,954 - INFO - [Py_INSERT_SQL_SERVER]
2025-06-03 21:04:07,954 - INFO - [Py_INSERT_SQL_SERVER] Ejecutando SP_TQW_COMISION_2023 '202506'...
2025-06-03 21:04:07,970 - INFO - [Py_INSERT_SQL_SERVER] SP_TQW_COMISION_2023 ejecutado exitosamente
2025-06-03 21:04:07,970 - INFO - [Py_INSERT_SQL_SERVER] Ejecutando SP_TQW_COMISION_2023_V2 '202506'...
2025-06-03 21:04:07,970 - INFO - [Py_INSERT_SQL_SERVER] SP_TQW_COMISION_2023_V2 ejecutado exitosamente
2025-06-03 21:04:07,970 - INFO - [Py_INSERT_SQL_SERVER] Leyendo datos de SQL Server para transferir a MySQL...
2025-06-03 21:04:07,988 - INFO - [Py_INSERT_SQL_SERVER] Leídos 110746 registros de ProduccionNDC
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Leídos 2241 registros de TB_TQW_COMISION_2023
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Leídos 12 registros de TB_MYSQL_CHART_NDC_MENSUAL
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Leídos 9 registros de TB_MYSQL_CHART_NDC_DIA
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Leídos 350 registros de TB_TQW_COMISION_RENEW
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Leyendo datos de vw_CalidadFlujo_kpi...
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Se obtuvieron 1092 registros de vw_CalidadFlujo_kpi
2025-06-03 21:04:07,992 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos en MySQL por lotes...
2025-06-03 21:04:08,009 - INFO - [Py_INSERT_SQL_SERVER] Insertando 12 registros en 'tb_mysql_chart_ndc_mensual' en 1 lotes...
2025-06-03 21:04:08,011 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de tb_mysql_chart_ndc_mensual...
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 completado (12 registros)
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'tb_mysql_chart_ndc_mensual' completada.
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Insertando 9 registros en 'tb_mysql_chart_ndc_dia' en 1 lotes...
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de tb_mysql_chart_ndc_dia...
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 completado (9 registros)
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'tb_mysql_chart_ndc_dia' completada.
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Insertando 350 registros en 'tb_tqw_comision_renew' en 1 lotes...
2025-06-03 21:04:08,014 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de tb_tqw_comision_renew...
2025-06-03 21:04:08,032 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 completado (350 registros)
2025-06-03 21:04:08,035 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'tb_tqw_comision_renew' completada.
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Iniciando inserción de datos en tb_paso_pyndc (110746 registros)...
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Insertando 110746 registros en 'tb_paso_pyndc' en 23 lotes...
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de tb_paso_pyndc...
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/23 completado (5000 registros)
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Lote 2/23 completado (5000 registros)
2025-06-03 21:04:08,036 - INFO - [Py_INSERT_SQL_SERVER] Lote 3/23 completado (5000 registros)
2025-06-03 21:04:08,052 - INFO - [Py_INSERT_SQL_SERVER] Lote 4/23 completado (5000 registros)
2025-06-03 21:04:08,057 - INFO - [Py_INSERT_SQL_SERVER] Lote 5/23 completado (5000 registros)
2025-06-03 21:04:08,059 - INFO - [Py_INSERT_SQL_SERVER] Lote 6/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 7/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 8/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 9/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 10/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 11/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 12/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 13/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 14/23 completado (5000 registros)
2025-06-03 21:04:08,061 - INFO - [Py_INSERT_SQL_SERVER] Lote 15/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 16/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 17/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 18/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 19/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 20/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 21/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 22/23 completado (5000 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Lote 23/23 completado (746 registros)
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'tb_paso_pyndc' completada.
2025-06-03 21:04:08,076 - INFO - [Py_INSERT_SQL_SERVER] Iniciando inserción de datos en tb_paso_kpi2023 (2241 registros)...
2025-06-03 21:04:08,095 - INFO - [Py_INSERT_SQL_SERVER] Insertando 2241 registros en 'tb_paso_kpi2023' en 1 lotes...
2025-06-03 21:04:08,098 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de tb_paso_kpi2023...
2025-06-03 21:04:08,100 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 completado (2241 registros)
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'tb_paso_kpi2023' completada.
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] ¡Proceso de transferencia de datos completado exitosamente!
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Insertando datos en vw_calidadflujo_kpi (1092 registros)...
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Insertando 1092 registros en 'vw_calidadflujo_kpi' en 1 lotes...
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Eliminando registros existentes de vw_calidadflujo_kpi...
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Lote 1/1 completado (1092 registros)
2025-06-03 21:04:08,101 - INFO - [Py_INSERT_SQL_SERVER] Conexión a MySQL cerrada. Inserción a 'vw_calidadflujo_kpi' completada.
2025-06-03 21:04:08,116 - INFO - [Py_INSERT_SQL_SERVER] Cerrando conexiones...
2025-06-03 21:04:08,116 - INFO - [Py_INSERT_SQL_SERVER] Conexión a SQL Server cerrada
2025-06-03 21:04:08,116 - INFO - [Py_INSERT_SQL_SERVER] Proceso finalizado
2025-06-03 21:04:08,116 - INFO - Proceso Py_INSERT_SQL_SERVER completado exitosamente. Tiempo de ejecución: 101.25 segundos
2025-06-03 21:04:08,116 - INFO - Salida de Py_INSERT_SQL_SERVER: Iniciando la carga de datos...
Conectando a SQL Server...
Leyendo archivos Excel...
Archivos Excel cargados correctamente en DataFrames.
Insertando datos en SQL Server...
Truncando tabla tb_paso_pyNdc...
Insertando datos de Metro...
Insertando 1418 filas en tb_paso_pyNdc en 2 lotes...
Lote 1/2 insertado en tb_paso_pyNdc
Lote 2/2 insertado en tb_paso_pyNdc
Insertando datos de Sur...
Insertando 64 filas en tb_paso_pyNdc en 1 lotes...
Lote 1/1 insertado en tb_paso_pyNdc
Insertando datos de Norte...
Insertando 24 filas en tb_paso_pyNdc en 1 lotes...
Lote 1/1 insertado en tb_paso_pyNdc
Insertando datos de Centro...
Insertando 762 filas en tb_paso_pyNdc en 1 lotes...
Lote 1/1 insertado en tb_paso_pyNdc
Ejecutando procedimientos almacenados en SQL Server...
SP_INSERT_NDC ejecutado exitosamente
Ejecutando procedimientos almacenados adicionales...
SP_CREATE_NDC_PRODUCCION '202506' ejecutado exitosamente

============================================================
🔬 INICIANDO ANÁLISIS RGU PROBLEMÁTICO
============================================================
📊 Iniciando análisis RGU problemático para período 202506...
🔍 Ejecutando query de análisis en SQL Server...
✅ Encontrados 7 registros RGU problemático
📋 Resumen por Tipo de Red:
- FTTH: 6 registros
- HFC: 1 registros
🗑️  Limpiando registros anteriores de análisis problemático...
🗑️  Eliminados 4 registros anteriores de análisis problemático
✅ Análisis RGU problemático completado exitosamente
📊 Total registros procesados: 7
🎯 Período: 202506
⏰ Procesado: 2025-06-03 21:03:08
✅ Análisis RGU problemático completado exitosamente
============================================================

Ejecutando SP_TQW_COMISION_2023 '202506'...
SP_TQW_COMISION_2023 ejecutado exitosamente
Ejecutando SP_TQW_COMISION_2023_V2 '202506'...
SP_TQW_COMISION_2023_V2 ejecutado exitosamente
Leyendo datos de SQL Server para transferir a MySQL...
Leídos 110746 registros de ProduccionNDC
Leídos 2241 registros de TB_TQW_COMISION_2023
Leídos 12 registros de TB_MYSQL_CHART_NDC_MENSUAL
Leídos 9 registros de TB_MYSQL_CHART_NDC_DIA
Leídos 350 registros de TB_TQW_COMISION_RENEW
Leyendo datos de vw_CalidadFlujo_kpi...
Se obtuvieron 1092 registros de vw_CalidadFlujo_kpi
Insertando datos en MySQL por lotes...
Insertando 12 registros en 'tb_mysql_chart_ndc_mensual' en 1 lotes...
Eliminando registros existentes de tb_mysql_chart_ndc_mensual...
Lote 1/1 completado (12 registros)
Conexión a MySQL cerrada. Inserción a 'tb_mysql_chart_ndc_mensual' completada.
Insertando 9 registros en 'tb_mysql_chart_ndc_dia' en 1 lotes...
Eliminando registros existentes de tb_mysql_chart_ndc_dia...
Lote 1/1 completado (9 registros)
Conexión a MySQL cerrada. Inserción a 'tb_mysql_chart_ndc_dia' completada.
Insertando 350 registros en 'tb_tqw_comision_renew' en 1 lotes...
Eliminando registros existentes de tb_tqw_comision_renew...
Lote 1/1 completado (350 registros)
Conexión a MySQL cerrada. Inserción a 'tb_tqw_comision_renew' completada.
Iniciando inserción de datos en tb_paso_pyndc (110746 registros)...
Insertando 110746 registros en 'tb_paso_pyndc' en 23 lotes...
Eliminando registros existentes de tb_paso_pyndc...
Lote 1/23 completado (5000 registros)
Lote 2/23 completado (5000 registros)
Lote 3/23 completado (5000 registros)
Lote 4/23 completado (5000 registros)
Lote 5/23 completado (5000 registros)
Lote 6/23 completado (5000 registros)
Lote 7/23 completado (5000 registros)
Lote 8/23 completado (5000 registros)
Lote 9/23 completado (5000 registros)
Lote 10/23 completado (5000 registros)
Lote 11/23 completado (5000 registros)
Lote 12/23 completado (5000 registros)
Lote 13/23 completado (5000 registros)
Lote 14/23 completado (5000 registros)
Lote 15/23 completado (5000 registros)
Lote 16/23 completado (5000 registros)
Lote 17/23 completado (5000 registros)
Lote 18/23 completado (5000 registros)
Lote 19/23 completado (5000 registros)
Lote 20/23 completado (5000 registros)
Lote 21/23 completado (5000 registros)
Lote 22/23 completado (5000 registros)
Lote 23/23 completado (746 registros)
Conexión a MySQL cerrada. Inserción a 'tb_paso_pyndc' completada.
Iniciando inserción de datos en tb_paso_kpi2023 (2241 registros)...
Insertando 2241 registros en 'tb_paso_kpi2023' en 1 lotes...
Eliminando registros existentes de tb_paso_kpi2023...
Lote 1/1 completado (2241 registros)
Conexión a MySQL cerrada. Inserción a 'tb_paso_kpi2023' completada.
¡Proceso de transferencia de datos completado exitosamente!
Insertando datos en vw_calidadflujo_kpi (1092 registros)...
Insertando 1092 registros en 'vw_calidadflujo_kpi' en 1 lotes...
Eliminando registros existentes de vw_calidadflujo_kpi...
Lote 1/1 completado (1092 registros)
Conexión a MySQL cerrada. Inserción a 'vw_calidadflujo_kpi' completada.
Cerrando conexiones...
Conexión a SQL Server cerrada
Proceso finalizado