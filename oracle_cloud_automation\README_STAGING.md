# Oracle Cloud Automation - Staging Table Approach

## Overview

This document describes the new staging table approach implemented for better data management and reliability.

## Architecture

### Before (Legacy Approach)
```
Excel Download → Direct INSERT INTO tb_toa_reporte_diario (APPEND)
```

### After (Staging Approach)
```
Excel Download → REPLACE on tb_toa_reporte_diario_staging → SP INSERT INTO tb_toa_reporte_diario
```

## Benefits

✅ **Data Validation**: Staging table allows data validation before inserting to historical table
✅ **Error Recovery**: Failed migrations don't corrupt the historical data
✅ **Audit Trail**: Complete logging of all operations
✅ **Performance**: Stored Procedure optimization
✅ **Maintainability**: Clean separation of concerns

## Components

### 1. Staging Table
- **Name**: `tb_toa_reporte_diario_staging`
- **Purpose**: Temporary storage for fresh Excel data
- **Operation**: Always REPLACE (drop and recreate)
- **Lifetime**: Dropped after successful migration

### 2. Historical Table
- **Name**: `tb_toa_reporte_diario`
- **Purpose**: Long-term data storage (historical data)
- **Operation**: APPEND via Stored Procedure
- **Features**: Accumulates all historical data

### 3. Stored Procedure
- **Name**: `sp_insert_from_staging_to_tb_toa_reporte_diario`
- **Purpose**: Migrate data from staging to historical table
- **Features**:
  - Error handling and logging
  - Duplicate prevention
  - Transaction management
  - Performance optimization

### 4. Audit Log Table
- **Name**: `audit_log`
- **Purpose**: Track all operations and errors
- **Fields**: operation, table_name, timestamps, records_processed, status

## Setup Instructions

### 1. Create Stored Procedure
```bash
cd oracle_cloud_automation
python create_staging_sp.py
```

### 2. Run Automation
The main script now automatically uses the staging approach:
```bash
python run.py
```

## Process Flow

1. **Download Excel**: Script downloads XLSX from Oracle Cloud
2. **Create Staging**: Drops and recreates staging table
3. **Load Staging**: Inserts Excel data into staging table
4. **Validate Data**: (Future enhancement - data validation)
5. **Migrate Data**: SP moves data from staging to historical table
6. **Log Operation**: Records success/failure in audit log
7. **Cleanup**: Staging table can be dropped (optional)

## Configuration

The staging approach is controlled by the `use_staging_approach` parameter in `upload_excel_to_sql_server()`:

```python
# In main.py
success = upload_excel_to_sql_server(
    excel_file,
    table_name='tb_toa_reporte_diario',
    use_staging_approach=True  # Enables staging approach
)
```

## Error Handling

- **Staging Failures**: Don't affect historical data
- **SP Failures**: Logged in audit_log table
- **Rollback**: Automatic transaction rollback on errors
- **Recovery**: Failed operations can be retried

## Monitoring

### Check Recent Operations
```sql
SELECT TOP 10 * FROM audit_log
WHERE operation = 'STAGING_TO_HISTORICAL_MIGRATION'
ORDER BY start_time DESC;
```

### Check Historical Data
```sql
SELECT COUNT(*) as total_records FROM tb_toa_reporte_diario;
SELECT MAX(fecha_integracion) as latest_data FROM tb_toa_reporte_diario;
```

## Troubleshooting

### Common Issues

1. **SP Not Found**: Run `create_staging_sp.py` first
2. **Permission Errors**: Ensure database user has CREATE/EXECUTE permissions
3. **Staging Table Exists**: SP handles cleanup automatically

### Debug Mode
Enable detailed logging by setting log level to DEBUG in `config.py`.

## Future Enhancements

- **Data Validation**: Add validation rules in staging phase
- **Duplicate Handling**: Enhanced duplicate detection logic
- **Performance**: Batch processing optimizations
- **Monitoring**: Real-time dashboards for operation status