import os
import subprocess
import time
import psutil
import sys

def kill_process_by_name(name):
    """Mata todos los procesos con el nombre indicado"""
    killed = False
    for proc in psutil.process_iter():
        try:
            # Verificar si el proceso es Schedule_ETL_TQW.py
            if name.lower() in proc.name().lower() or name.lower() in ' '.join(proc.cmdline()).lower():
                print(f"Terminando proceso: {proc.name()} (PID: {proc.pid})")
                proc.terminate()
                killed = True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return killed

# Ruta al script del scheduler
scheduler_path = r"C:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py"

# Verificar si el archivo existe
if not os.path.exists(scheduler_path):
    print(f"ERROR: No se encontró el archivo {scheduler_path}")
    sys.exit(1)

# Matar procesos existentes
print("Buscando y terminando instancias existentes del scheduler...")
killed = kill_process_by_name("Schedule_ETL_TQW.py")
killed = killed or kill_process_by_name("python.exe Schedule_ETL_TQW.py")

if killed:
    print("Esperando a que los procesos terminen...")
    time.sleep(3)

# Iniciar una nueva instancia
print(f"Iniciando nueva instancia de {scheduler_path}...")
subprocess.Popen(["python", scheduler_path], creationflags=subprocess.CREATE_NEW_CONSOLE)

print("¡Reinicio completado!")
print("NOTA: Si el problema persiste, intenta eliminar el archivo scheduler_config.json para que se recree desde cero")
