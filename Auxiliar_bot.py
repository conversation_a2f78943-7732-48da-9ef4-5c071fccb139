from selenium import webdriver
from selenium.webdriver.common.by import By  # Importa la clase By
from selenium.webdriver.common.keys import Keys
import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np




engine = create_engine('mssql+pyodbc://sa:N1c0l7as@************/master?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()



# # session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_DIRECTAX"))
# # session.commit()

# list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
# latest_file = max(list_of_files, key=os.path.getctime)

# print(latest_file)

# df = pd.read_csv(latest_file, sep='\t')
# # Crear el diccionario de tipos de datos personalizados
# custom_dtypes = {
# 'Serial': sqlalchemy.types.String(50),
# 'Item': sqlalchemy.types.String(50),
# 'Org': sqlalchemy.types.String(50),
# 'Revision': sqlalchemy.types.String(50),
# 'Subinventory': sqlalchemy.types.String(50),
# 'Locator': sqlalchemy.types.String(50),
# 'Operation': sqlalchemy.types.String(50),
# 'Job': sqlalchemy.types.String(50),
# 'Step': sqlalchemy.types.String(50),
# 'Lot': sqlalchemy.types.String(50),
# 'State': sqlalchemy.types.String(50),
# 'Status': sqlalchemy.types.String(50),
# 'Receipt Date': sqlalchemy.types.String(50),
# 'Ship Date': sqlalchemy.types.String(50),
# 'Supplier Name': sqlalchemy.types.String(50),
# 'Supplier Lot': sqlalchemy.types.String(50),
# 'Supplier Serial': sqlalchemy.types.String(50),
# 'Unit Number': sqlalchemy.types.String(50),
# 'Attributes': sqlalchemy.types.String(50),
# '[  ]': sqlalchemy.types.String(50),
# 'Unnamed: 20': sqlalchemy.types.String(50)
# }

# # Truncar las cadenas a un máximo de 100 caracteres
# #  df_truncated = df_dtype_str.applymap(lambda x: x[:100])
# # Insertar el DataFrame en la tabla SQL
# df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
# print(f"Total de registros en el DataFrame Santiago Directa: {len(df)}")





session.execute(text("EXEC SP_INSERT_ORACLE_DIRECTA"))
session.commit()

Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_DIRECTAX", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@*************:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('TB_FERRET_DIRECTA1', engineMYsql, if_exists='replace',index=False)





seriesNuevas = pd.read_sql_query("SELECT * FROM VW_LOGIS_SERIES_ASIGNADAS", engine)

# engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@*************:3306/telqwayc_db_operacion', echo=False)

seriesNuevas.to_sql('TB_LOGIS_MOVIMIENTOS', engineMYsql, if_exists='append',index=False)


# Define la sentencia SQL para crear el índice
sql = text("CREATE INDEX idx_serial ON TB_FERRET_DIRECTA1 (Serial(255));")





session.close()

# Ejecuta la sentencia SQL
with engineMYsql.connect() as connection:
    connection.execute(sql)

# Cierra la conexión
engineMYsql.dispose()

