from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import pandas as pd
import time
import os
import sys
from selenium.webdriver.edge.service import Service
from datetime import datetime

# Configuración básica
start_time = datetime.now()

# Mostrar mensajes de inicio
print("="*50)
print("INICIO DE NDCBOT - DESCARGA DE REPORTES VTR")
print(f"Fecha y hora: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*50)

def set_date_range(driver, start_date, end_date):
    try:
        time.sleep(2)
        driver.find_element_by_xpath("/html/body/div[5]/div[3]/table/tbody/tr[1]/td[2]/input").send_keys(start_date)
        time.sleep(1)
        driver.find_element_by_xpath("/html/body/div[5]/div[3]/table/tbody/tr[1]/td[4]/input").send_keys(end_date)
    except Exception as e:
        print(f"Error al establecer el rango de fechas: {str(e)}")

def validar_y_eliminar_archivo(archivo):
    try:
        if os.path.exists(archivo):
            os.remove(archivo)
            print(f"El archivo '{archivo}' ha sido eliminado.")
        else:
            print(f"El archivo '{archivo}' no existe.")
    except Exception as e:
        print(f"Error al eliminar el archivo {archivo}: {str(e)}")

try:
    # Usar el driver local en vez de la descarga automática
    script_dir = os.path.dirname(os.path.abspath(__file__))
    msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")
    
    if not os.path.exists(msedgedriver_path):
        print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
        print("Por favor, ejecute descargar_directo.py para obtener el driver")
        exit()
    
    print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
    service = Service(msedgedriver_path)
    driver = webdriver.Edge(service=service)
except Exception as e:
    print(f"Error de inicialización: {str(e)}")
    exit()

try:
    driver.get("http://eps.vtr.cl/eps/declaracion_consumo.do")
    time.sleep(4)
    driver.refresh()
    time.sleep(8)

    print("Iniciando proceso de login...")
    driver.find_element_by_xpath("/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[1]/td[2]/input").send_keys("cnawrath")
    driver.find_element_by_xpath("/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[3]/td[2]/input").send_keys("#Telqway.540")
    driver.find_element_by_xpath("/html/body/div[2]/table/tbody/tr/td[1]/div[2]/p/span/a").click()
    print("Login completado")
    

    time.sleep(5)
    driver.find_element_by_xpath("/html/body/div[5]/div[1]/div[1]/span/span/a").click()

    time.sleep(4)
    driver.find_element_by_xpath("/html/body/div[9]/div[2]/table/tbody/tr[2]/td/select").click()
    time.sleep(2)
    driver.find_element_by_xpath("/html/body/div[9]/div[2]/table/tbody/tr[2]/td/select/option[1]").click()

    time.sleep(2)
    driver.find_element_by_xpath("/html/body/div[5]/div[1]/div[3]/ul/li/a").click()
    driver.find_element_by_xpath("/html/body/div[5]/div[1]/div[3]/ul/li/ul/li[3]").click()


    print("\n" + "-"*50)
    print("SELECCION DE RANGO DE FECHAS")
    print("-"*50)
    set_date_range(driver, "25-08-2025", "24-09-2025")
    print("Rango de fechas establecido")

    time.sleep(3)
    webdriver.ActionChains(driver).send_keys(Keys.ESCAPE).perform()

    archivos_a_eliminar = [
        'C:\\Users\\<USER>\\Downloads\\Reporte_Ordenes_Trabajo_Valorizadas.csv',
        'C:\\Users\\<USER>\\Downloads\\Reporte_Ordenes_Trabajo_Valorizadas (3).csv',
        'C:\\Users\\<USER>\\Downloads\\Reporte_Ordenes_Trabajo_Valorizadas (2).csv',
        'C:\\Users\\<USER>\\Downloads\\Reporte_Ordenes_Trabajo_Valorizadas (1).csv'
    ]

    for archivo in archivos_a_eliminar:
        validar_y_eliminar_archivo(archivo)

    print("\n" + "-"*50)
    print("SELECCION DE LAS ZONAS Y DESCARGA DE REPORTES")
    print("-"*50)
    zonas = [
        ("/html/body/div[5]/div[3]/table/tbody/tr[2]/td[4]/div/select/option[2]", "Centro"),
        ("/html/body/div[5]/div[3]/table/tbody/tr[2]/td[4]/div/select/option[3]", "Metro"),
        ("/html/body/div[5]/div[3]/table/tbody/tr[2]/td[4]/div/select/option[4]", "Norte"),
        ("/html/body/div[5]/div[3]/table/tbody/tr[2]/td[4]/div/select/option[5]", "Sur")
    ]
    
    print(f"Procesando {len(zonas)} zonas: Centro, Metro, Norte, Sur")
    
    for xpath, nombre in zonas:
        try:
            print(f"\nProcesando zona: {nombre}")
            driver.find_element_by_xpath("/html/body/div[5]/div[3]/table/tbody/tr[2]/td[4]/div/select").click()
            driver.find_element_by_xpath(xpath).click()
            print(f"  Seleccionada zona {nombre}")
            driver.find_element_by_xpath("/html/body/div[5]/div[3]/table/tbody/tr[6]/td[2]/button[1]").click()
            print(f"  Generando reporte para {nombre}...")
            time.sleep(4)
            driver.find_element_by_xpath("/html/body/div[5]/div[6]/a").click()
            print(f"  Descargando reporte de {nombre}")
            time.sleep(1)
            print(f"  Zona {nombre} procesada correctamente")
        except Exception as e:
            print(f"Error al procesar la zona {nombre}: {str(e)}")

    print("\n" + "-"*50)
    print("CONVIRTIENDO ARCHIVOS CSV A EXCEL")
    print("-"*50)
    
    conversiones = [
        (r'C:\Users\<USER>\Downloads\Reporte_Ordenes_Trabajo_Valorizadas.csv', r'C:\Users\<USER>\Dropbox\PythonNDC\Centro.xlsx', 'Centro'),
        (r'C:\Users\<USER>\Downloads\Reporte_Ordenes_Trabajo_Valorizadas (1).csv', r'C:\Users\<USER>\Dropbox\PythonNDC\Metro.xlsx', 'Metro'),
        (r'C:\Users\<USER>\Downloads\Reporte_Ordenes_Trabajo_Valorizadas (2).csv', r'C:\Users\<USER>\Dropbox\PythonNDC\Norte.xlsx', 'Norte'),
        (r'C:\Users\<USER>\Downloads\Reporte_Ordenes_Trabajo_Valorizadas (3).csv', r'C:\Users\<USER>\Dropbox\PythonNDC\Sur.xlsx', 'Sur')
    ]
    
    for csv_path, excel_path, nombre in conversiones:
        try:
            print(f"Convirtiendo {nombre}: {csv_path} -> {excel_path}")
            # Verificar que exista el archivo
            if not os.path.exists(csv_path):
                print(f"  ADVERTENCIA: Archivo {csv_path} no encontrado")
                continue
                
            # Leer CSV y convertir a Excel
            if nombre == 'Sur':
                # Para el Sur usar parámetro error_bad_lines
                data_xls = pd.read_csv(csv_path, sep=";", encoding='Latin-1', on_bad_lines='skip')
            else:
                data_xls = pd.read_csv(csv_path, sep=";", encoding='Latin-1')
                
            # Guardar como Excel
            data_xls.to_excel(excel_path, index=False)
            print(f"  ✓ Conversión exitosa: {nombre} ({len(data_xls)} filas)")
            
        except Exception as e:
            print(f"  ✗ Error al convertir archivo {nombre}: {str(e)}")

except Exception as e:
    print(f"Error durante la ejecución: {str(e)}")
finally:
    driver.quit()
    
    # Mostrar resumen final
    end_time = datetime.now()
    duration = end_time - start_time
    print("\n" + "="*50)
    print("FINALIZADO")
    print(f"Tiempo de ejecución: {duration.total_seconds():.2f} segundos")
    print(f"Fecha y hora: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50)