import pandas as pd
import pyodbc
import time

class DatabaseManager:
    def __init__(self):
        # Usar directamente la IP y puerto proporcionados
        self.server_ip = '************'
        
        # Cadena de conexión simple y directa
        self.connection_string = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={self.server_ip};DATABASE=telqway;UID=ncornejo;PWD=N1c0l7as17;Connection Timeout=30;TrustServerCertificate=yes'
        print(f"Configurado para conectar a SQL Server en: {self.server_ip}")
    
            
    def upload_excel(self, file_path, table_name, progress_callback):
        try:
            # Leer el archivo Excel con pandas
            print(f"Leyendo archivo Excel: {file_path}")
            
            # Detectar si el archivo tiene encabezados
            try:
                # Intentar leer las primeras filas para ver si hay encabezados
                sample = pd.read_excel(file_path, nrows=5, engine='openpyxl')
                has_header = True
                
                # Verificar si los encabezados parecen ser datos (todos numéricos)
                if all(isinstance(col, (int, float)) for col in sample.columns):
                    print("Detectado: archivo sin encabezados, usando primera fila como datos")
                    has_header = False
                else:
                    print("Detectado: archivo con encabezados")
            except Exception as e:
                print(f"Error al analizar encabezados: {str(e)}")
                has_header = True  # Valor predeterminado
            
            # Leer el archivo completo con o sin encabezados
            if has_header:
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                # Si no hay encabezados, generar nombres de columnas
                df = pd.read_excel(file_path, header=None, engine='openpyxl')
                df.columns = [f"Column_{i+1}" for i in range(len(df.columns))]
            
            total_rows = len(df)
            if total_rows == 0:
                return False, "El archivo Excel está vacío."
            
            print(f"Archivo cargado con {total_rows} filas y {len(df.columns)} columnas")
            
            # Reemplazar NaN con None
            df = df.where(pd.notna(df), None)
            
            # Crear conexión y cursor
            print("Conectando a SQL Server...")
            conn = pyodbc.connect(self.connection_string)
            cursor = conn.cursor()
            print("Conexión establecida")
            
            try:
                # Preparar columnas para crear tabla si no existe
                columns = []
                for col in df.columns:
                    # Extraer una muestra de datos no nulos para analizar
                    sample_data = df[col].dropna().head(10).tolist()
                    
                    # Como solicitado, usaremos VARCHAR/TEXT para todas las columnas
                    print(f"Analizando columna: {col} - Tipo pandas: {df[col].dtype}")
                    
                    # Convertir a string de forma segura para todas las columnas
                    str_col = df[col].fillna('').astype(str)
                    max_len = str_col.str.len().max()
                    
                    # Añadimos un 50% extra como margen de seguridad
                    safe_len = int(max_len * 1.5) if not pd.isna(max_len) else 255
                    
                    # Decidir entre NVARCHAR o TEXT basado en la longitud
                    if safe_len <= 4000:
                        # Para columnas cortas, usamos NVARCHAR con tamaño adecuado
                        safe_len = max(255, safe_len)  # Mínimo 255 caracteres
                        col_type = f'NVARCHAR({safe_len})'
                    else:
                        # Para columnas muy largas, usamos TEXT (hasta 2GB)
                        col_type = 'TEXT'
                    
                    print(f"  -> Tipo SQL decidido: {col_type}")
                    
                    print(f"  -> Tipo SQL decidido: {col_type}")
                    
                    columns.append(f"[{col}] {col_type}")
                
                # Limpiar la tabla si existe
                print(f"Eliminando tabla {table_name} si existe...")
                drop_table_sql = f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE {table_name}"
                cursor.execute(drop_table_sql)
                
                # Crear la tabla
                print(f"Creando tabla {table_name} con esquema optimizado...")
                print(f"Tipos de columnas: {', '.join(columns)}")
                create_table_sql = f"CREATE TABLE {table_name} ({', '.join(columns)})"
                cursor.execute(create_table_sql)
                conn.commit()
                print("Tabla creada correctamente")
                
                # Tamaño de cada chunk para actualización de barra de progreso
                chunk_size = 100
                print(f"Iniciando carga de datos en chunks de {chunk_size} filas...")
                errors_count = 0
                rows_inserted = 0
                
                for i in range(0, total_rows, chunk_size):
                    chunk = df.iloc[i:i + chunk_size]
                    chunk_rows = len(chunk)
                    
                    # Preparar consulta de inserción
                    columns_list = [f"[{col}]" for col in chunk.columns]
                    placeholders = ["?" for _ in chunk.columns]
                    insert_sql = f"INSERT INTO {table_name} ({', '.join(columns_list)}) VALUES ({', '.join(placeholders)})"
                    
                    print(f"Procesando chunk {i//chunk_size + 1}/{(total_rows+chunk_size-1)//chunk_size} ({i}-{min(i+chunk_size, total_rows)})")
                    start_time = time.time()
                    
                    # Vamos a imprimir cada columna, su tipo SQL y la primera fila para depurar
                    print("\nINFORME DE DEPURACIÓN DE COLUMNAS:")
                    for idx, col in enumerate(chunk.columns):
                        col_def = next((c for c in columns if c.startswith(f'[{col}]')), '')
                        sample_val = chunk[col].iloc[0] if not chunk.empty else 'N/A'
                        print(f"Columna[{idx}]: '{col}' - Tipo SQL: {col_def} - Primer valor: {sample_val} ({type(sample_val).__name__})")
                    
                    # Construir consulta modificada usando valores codificados directamente
                    print("\nCreando consulta SQL personalizada...")
                    
                    # Insertar filas una por una con manejo de errores
                    for idx, row in chunk.iterrows():
                        try:
                            # Primero convertimos todo a strings (excepto None)
                            row_strings = ["NULL" if pd.isna(x) else "'" + str(x).replace("'", "''") + "'"
                                          for x in row]
                            
                            # Construir consulta INSERT con valores literales
                            columns_list = [f"[{col}]" for col in chunk.columns]
                            values_literal = ", ".join(row_strings)
                            insert_literal_sql = f"INSERT INTO {table_name} ({', '.join(columns_list)}) VALUES ({values_literal})"
                            
                            # Ejecutar consulta directa sin parámetros
                            cursor.execute(insert_literal_sql)
                            
                            # Commit después de cada fila para evitar perder todo si hay un error
                            conn.commit()
                            rows_inserted += 1
                            
                        except pyodbc.Error as e:
                            # Capturar error específico y mostrar información detallada
                            errors_count += 1
                            print(f"\nERROR EN FILA {idx}:")
                            print(f"Error: {str(e)}")
                            print("Datos de la fila:")
                            for col_idx, (col, val) in enumerate(zip(chunk.columns, row)):
                                print(f"  {col} ({type(val).__name__}): {val}")
                            print()
                            
                            # Continuar con la siguiente fila
                            rows_inserted += 1
                        except pyodbc.Error as e:
                            # Registrar la fila problemática pero continuar con las demás
                            errors_count += 1
                            print(f"Error en fila: {row}\nError: {str(e)}")
                            # Si es error crítico, relanzar la excepción
                            if '22001' not in str(e):
                                raise
                    
                    end_time = time.time()
                    print(f"Chunk procesado en {end_time - start_time:.2f} segundos")
                    
                    # Ya no es necesario commit aquí, se hace por fila
                    
                    # Actualizar progreso
                    progress = min(100, ((i + chunk_size) / total_rows) * 100)
                    progress_callback(progress)
                
                print(f"Importación completada: {rows_inserted} filas insertadas, {errors_count} errores")
                return True, f"Datos importados exitosamente a SQL Server. {rows_inserted} filas procesadas."
            finally:
                # Cerrar cursor y conexión
                cursor.close()
                conn.close()
        except Exception as e:
            # Capturar error y retornar mensaje detallado
            error_msg = str(e)
            print(f"Error detallado: {error_msg}")
            
            # Proporcionar mensajes específicos para errores comunes
            if '22001' in error_msg and 'String or binary data would be truncated' in error_msg:
                return False, "Error: Datos demasiado largos para algunas columnas. Intente nuevamente; el sistema ajustará automáticamente los tamaños."
            elif "invalid literal for int()" in error_msg:
                return False, "Error: Se encontró texto en una columna que debería ser numérica. Revise sus datos o inténtelo nuevamente para usar tipos de datos más flexibles."
            elif "could not convert string to float" in error_msg:
                return False, "Error: No se pudo convertir texto a número decimal. Revise sus datos o inténtelo nuevamente para usar tipos de datos más flexibles."
            
            return False, f"Error al importar datos: {error_msg}"
