"""
Test script to verify all modules can be imported correctly.
Run this to ensure the modular structure is working.
"""

def test_imports():
    """Test that all modules can be imported without errors."""
    try:
        print("Testing imports...")

        # Test config module
        from config import SQL_SERVER_CONFIG, ORACLE_CONFIG, ELEMENT_SELECTORS
        print("✅ config.py imported successfully")

        # Test database module
        from database import DatabaseManager, upload_excel_to_sql_server
        print("✅ database.py imported successfully")

        # Test file_utils module
        from file_utils import find_latest_excel_file, validate_excel_file
        print("✅ file_utils.py imported successfully")

        # Test web_automation module
        from web_automation import OracleWebAutomation, setup_and_navigate
        print("✅ web_automation.py imported successfully")

        # Test main module
        import main
        print("✅ main.py imported successfully")

        print("\n🎉 All modules imported successfully!")
        print("The modular structure is working correctly.")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_imports()