import pandas as pd
from sqlalchemy import create_engine, text
import time
from datetime import datetime
import os
import sys

# Tiempo de inicio
start_time = time.time()

# Argumentos de línea de comandos para opciones
import argparse

parser = argparse.ArgumentParser(description='Importar archivo de productividad a SQL Server')
parser.add_argument('--file', type=str, help='Ruta al archivo de productividad',
                    default=r'C:\Users\<USER>\OneDrive - kayze\ETL PYTHON\Productividad.txt')
parser.add_argument('--header-row', type=int, help='Fila de encabezados (base 0)', default=2)
parser.add_argument('--encoding', type=str, help='Codificación del archivo', default=None)
parser.add_argument('--mode', choices=['normal', 'direct'], help='Modo de importación', default='normal')
parser.add_argument('--force', action='store_true', help='Forzar importación sin confirmación')

args = parser.parse_args()

# Configuración de la conexión
server = "************"
database = "telqway" 
username = "ncornejo"
password = "N1c0l7as17"
conn_str = f'mssql+pyodbc://{username}:{password}@{server}/{database}?driver=ODBC Driver 17 for SQL Server'

# Ruta del archivo desde argumentos
file_path = args.file

# Configuración de análisis del archivo
header_row = args.header_row  # Índice base 0 de la fila de encabezados (tercera fila por defecto)
force_mode = args.force       # Si se debe forzar la importación sin confirmación
override_encoding = args.encoding  # Codificación especificada por usuario

try:
    # Leer archivo manualmente para asegurarnos de que procesamos correctamente
    print("Leyendo archivo línea por línea...")
    
    # Usar codificación especificada por el usuario si se proporciona
    if override_encoding:
        encoding = override_encoding
        print(f"Usando codificación especificada por el usuario: {encoding}")
    else:
        # Detectar automáticamente la codificación
        with open(file_path, 'rb') as binary_file:
            # Leer primeros bytes para detectar BOM o problemas binarios
            header = binary_file.read(4)
            if header.startswith(b'\xff\xfe') or header.startswith(b'\xfe\xff'):
                print("Detectado archivo con BOM Unicode")
                encoding = 'utf-16'
            elif header.startswith(b'\xef\xbb\xbf'):
                print("Detectado archivo con BOM UTF-8")
                encoding = 'utf-8-sig'
            else:
                # Verificar si hay caracteres nulos que indiquen un archivo binario
                binary_file.seek(0)
                sample = binary_file.read(4096)  # Leer una muestra más grande
                if b'\x00' in sample:
                    print("ADVERTENCIA: Detectados bytes nulos en el archivo. "
                          "Este podría ser un archivo binario o un archivo de texto UTF-16.")
                    # Intentar con UTF-16 primero
                    encoding = 'utf-16'
                else:
                    encoding = 'latin-1'  # Valor predeterminado
    
    print(f"Usando codificación: {encoding}")
    
    # Ahora leemos el archivo con la codificación detectada
    try:
        with open(file_path, 'r', encoding=encoding, errors='replace') as file:
            lines = [line.replace('\x00', '') for line in file.readlines()]
    except UnicodeError:
        print("Error con la codificación detectada. Intentando con codificación alternativa...")
        if encoding == 'utf-16':
            encoding = 'latin-1'
        else:
            encoding = 'utf-16'
        print(f"Intentando con: {encoding}")
        with open(file_path, 'r', encoding=encoding, errors='replace') as file:
            lines = [line.replace('\x00', '') for line in file.readlines()]
    
    # Información del archivo
    print(f"Total de líneas en el archivo: {len(lines)}")
    
    # Examinar las primeras líneas para ver qué contienen
    print("\nExaminando las primeras 5 líneas:")
    for i, line in enumerate(lines[:5]):
        cols = line.strip().split('\t')
        print(f"Línea {i+1}: {len(cols)} columnas. Contenido: {line[:50]}...")
    
    # Extraer encabezados usando la configuración definida
    header_line = lines[header_row].strip()
    headers = header_line.split('\t')
    print(f"\nEncabezados detectados: {len(headers)} columnas")
    
    # Limpiar encabezados
    clean_headers = [header.replace('Consolidado[', '').replace(']', '') for header in headers]
    
    # Mostrar los encabezados limpios
    print("\nPrimeros 5 encabezados limpios:")
    for i, header in enumerate(clean_headers[:5]):
        print(f"  {i+1}. {header}")
    
    # Crear DataFrame con los datos a partir de la siguiente línea después del encabezado
    data = []
    skipped_rows = 0
    empty_rows = 0
    
    for line_num, line in enumerate(lines[header_row+1:], start=header_row+2):
        # Verificar y limpiar caracteres problemáticos
        line = line.replace('\x00', '')
        
        if not line.strip():  # Línea vacía
            empty_rows += 1
            continue
        
        # Verificar si hay caracteres nulos o problemáticos
        has_null = '\x00' in line
        if has_null:
            print(f"Línea {line_num} contiene caracteres nulos incluso después de la limpieza")
            line = line.replace('\x00', '')  # Intentar limpiar nuevamente
        
        # Manejar diferentes tipos de separadores
        if '\t' in line:
            values = line.strip().split('\t')
        elif ',' in line:
            values = line.strip().split(',')
        else:
            # Intentar detectar el separador basado en la línea de encabezado
            if '\t' in header_line:
                values = line.strip().split('\t')
            elif ',' in header_line:
                values = line.strip().split(',')
            else:
                # Último recurso: dividir por espacios
                values = line.strip().split()
                
        # Limpiar espacios en blanco al inicio y final de cada valor
        values = [v.strip() for v in values]
        
        # Verificar que la línea tiene la cantidad correcta de columnas
        if len(values) == len(clean_headers):
            data.append(values)
        else:
            # Si estamos cerca (por ejemplo, 1-2 columnas menos), intentar rellenar con valores vacíos
            if len(values) > len(clean_headers) * 0.9 and len(values) < len(clean_headers):
                # Añadir columnas faltantes
                values.extend([''] * (len(clean_headers) - len(values)))
                data.append(values)
                print(f"Corregida línea {line_num}: Se añadieron {len(clean_headers) - len(values)} columnas vacías")
            else:
                skipped_rows += 1
                # Imprimir más información para diagnóstico
                print(f"Omitiendo línea {line_num}: {len(values)} columnas vs {len(clean_headers)} esperadas")
                if len(values) < 5:  # Si tiene pocas columnas, mostrar todo
                    print(f"  Contenido: {values}")
                else:  # Si tiene muchas, mostrar las primeras 3
                    print(f"  Primeras columnas: {values[:3]}...")
    
    print(f"\nEstadísticas de procesamiento:")
    print(f"  Filas procesadas correctamente: {len(data)}")
    print(f"  Filas omitidas por columnas incorrectas: {skipped_rows}")
    print(f"  Filas vacías: {empty_rows}")
    
    # Crear DataFrame
    df = pd.DataFrame(data, columns=clean_headers)
    
    # Información del DataFrame
    print(f"DataFrame creado con forma: {df.shape}")
    print("\nPrimeras 3 filas:")
    print(df.head(3))
    
    # Verificar si tenemos datos para insertar
    if len(df) == 0:
        raise Exception("No se pudieron procesar datos válidos del archivo. Verifique el formato del archivo.")
        
    # Verificar si hay un porcentaje bajo de líneas procesadas vs. total
    total_data_lines = len(lines) - (header_row + 1)  # Líneas después de los encabezados
    processed_percentage = (len(data) / total_data_lines) * 100 if total_data_lines > 0 else 0
    
    print(f"\nPorcentaje de líneas procesadas: {processed_percentage:.2f}%")
    if processed_percentage < 50 and total_data_lines > 100:
        print("ADVERTENCIA: Se procesó menos del 50% de las líneas del archivo.")
        print("Considere revisar el formato o la codificación del archivo.")
        
        # Verificar si se debe forzar la importación sin confirmación
        if not force_mode:
            if input("\n¿Desea continuar con la importación a pesar de los errores? (s/n): ").lower() != 's':
                print("Operación cancelada por el usuario.")
                sys.exit(0)
        else:
            print("Continuando con la importación (modo forzado)...")
            
    # Definir el nombre de la tabla
    table_name = 'TB_SHARE_PRODUCTIVIDAD'
        
    # Verificar tipos de datos antes de insertar
    print("\nTipos de datos en el DataFrame:")
    print(df.dtypes)
    
    # Conexión a SQL Server
    print("\nConectando a SQL Server...")
    engine = create_engine(conn_str)
    
    # Insertar en la base de datos
    print("Insertando datos en la base de datos...")
    CHUNKSIZE = 100  # Reducir tamaño de chunk para mejor control
    total_rows = len(df)
    table_name = 'TB_SHARE_PRODUCTIVIDAD'  # Definir el nombre de la tabla como variable
    
    # Forzar el uso del modo directo para evitar el error de cursor
    if True:  # Siempre usar el modo directo
        print("\nUsando modo de importación directa (sin SQLAlchemy)...")
        # Importación directa usando pyodbc
        try:
            import pyodbc
            # Crear conexión ODBC directa
            conn_str_direct = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
            conn = pyodbc.connect(conn_str_direct)
            cursor = conn.cursor()
            
            # Verificar conexión al servidor
            print("Verificando conexión a SQL Server...")
            try:
                cursor.execute("SELECT @@VERSION")
                version = cursor.fetchone()
                print(f"Conectado a: {version[0]}")
            except pyodbc.Error as e:
                print(f"Error de conexión: {str(e)}")
                raise
            
            # Insertar registros por lotes
            print("Insertando datos directamente via ODBC...")
            batch_size = 100
            count = 0
            
            # Eliminar la tabla si existe y crearla de nuevo
            print("Eliminando la tabla si existe y creando nueva...")
            drop_table_sql = f"IF OBJECT_ID('dbo.TB_SHARE_PRODUCTIVIDAD', 'U') IS NOT NULL DROP TABLE dbo.TB_SHARE_PRODUCTIVIDAD"
            cursor.execute(drop_table_sql)
            conn.commit()
            
            # Crear la tabla nueva
            columns = [f"[{col}] VARCHAR(255)" for col in df.columns]
            create_table_sql = f"CREATE TABLE dbo.TB_SHARE_PRODUCTIVIDAD ({', '.join(columns)})"
            cursor.execute(create_table_sql)
            conn.commit()
            print("Tabla recreada correctamente")
            
            # Preparar consulta de inserción
            columns_sql = ', '.join([f"[{col}]" for col in df.columns])
            placeholders = ', '.join(['?'] * len(df.columns))
            insert_sql = f"INSERT INTO dbo.TB_SHARE_PRODUCTIVIDAD ({columns_sql}) VALUES ({placeholders})"
            
            # Ejecutar inserciones en lotes
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i+batch_size]
                for _, row in batch.iterrows():
                    # Convertir todos los valores a string con manejo seguro de valores nulos
                    values = []
                    for val in row:
                        # Tratamiento correcto de valores NULL/NaN según CLAUDE.md
                        if pd.isna(val) or val is None:
                            values.append(None)
                        else:
                            # Limitar la longitud de strings muy largos
                            val_str = str(val)
                            if len(val_str) > 254:  # VARCHAR(255) tiene límite de 255 caracteres
                                val_str = val_str[:254]
                            values.append(val_str)
                    cursor.execute(insert_sql, values)
                    count += 1
                    
                # Commit cada lote
                conn.commit()
                print(f"Insertados {count}/{total_rows} registros ({count/total_rows*100:.1f}%)")
            
            print(f"Importación directa completada. {count} registros insertados.")
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"Error en importación directa: {str(e)}")
            raise
    # Se ha eliminado el modo SQLAlchemy para evitar errores de cursor
    # Movido todo a pyodbc directo
    
    print(f"\nProceso completado exitosamente. Se insertaron {total_rows} registros.")

except Exception as e:
    print(f"Error detallado: {str(e)}")
    # Registrar error en log
    log_file = os.path.join(os.path.dirname(file_path), 'error_productividad.log')
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()} - Error: {str(e)}\n")

finally:
    # Tiempo total
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"Tiempo total de ejecución: {execution_time:.2f} segundos")