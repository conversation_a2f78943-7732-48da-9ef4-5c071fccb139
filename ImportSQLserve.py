import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from db_manager import DatabaseManager
from config import MESSAG<PERSON>, WINDOW_CONFIG

class ExcelUploaderApp:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title(WINDOW_CONFIG['title'])
        self.window.geometry(f"{WINDOW_CONFIG['width']}x{WINDOW_CONFIG['height']}")
        
        self.db_manager = DatabaseManager()
        self.setup_ui()
        
    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding=WINDOW_CONFIG['padding'])
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Variables
        self.file_path = tk.StringVar()
        self.table_name = tk.StringVar()
        
        # Componentes
        ttk.Label(main_frame, text="Archivo Excel:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        file_entry = ttk.Entry(main_frame, textvariable=self.file_path, width=50)
        file_entry.grid(row=0, column=1, padx=5, pady=5)
        
        browse_btn = ttk.Button(main_frame, text="Examinar", command=self.browse_file)
        browse_btn.grid(row=0, column=2, pady=5)
        
        ttk.Label(main_frame, text="Nombre de tabla:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        table_entry = ttk.Entry(main_frame, textvariable=self.table_name, width=50)
        table_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5)
        
        # Barra de progreso
        self.progress = ttk.Progressbar(main_frame, length=400, mode='determinate')
        self.progress.grid(row=2, column=0, columnspan=3, pady=20)
        
        # Botón de carga
        upload_btn = ttk.Button(main_frame, text="Cargar datos", command=self.upload_data)
        upload_btn.grid(row=3, column=0, columnspan=3, pady=10)
        
        # Estado
        self.status_label = ttk.Label(main_frame, text="")
        self.status_label.grid(row=4, column=0, columnspan=3, pady=5)
        
    def browse_file(self):
        """Abre diálogo para seleccionar archivo"""
        filename = filedialog.askopenfilename(
            title=MESSAGES['select_file'],
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.file_path.set(filename)
            
    def update_progress(self, value):
        """Actualiza barra de progreso"""
        self.progress['value'] = value
        self.window.update_idletasks()
        
    def upload_data(self):
        """Maneja el proceso de carga de datos"""
        # Validaciones
        if not self.file_path.get():
            messagebox.showerror("Error", MESSAGES['select_file'])
            return
            
        if not self.table_name.get():
            messagebox.showerror("Error", MESSAGES['enter_table'])
            return
            
        # Iniciar carga
        self.status_label.config(text=MESSAGES['loading'])
        self.progress['value'] = 0
        
        # Ejecutar carga
        success, message = self.db_manager.upload_excel(
            self.file_path.get(),
            self.table_name.get(),
            self.update_progress
        )
        
        # Mostrar resultado
        if success:
            messagebox.showinfo("Éxito", message)
            self.status_label.config(text=MESSAGES['success'])
        else:
            messagebox.showerror("Error", message)
            self.status_label.config(text="")
            self.progress['value'] = 0
            
    def run(self):
        """Inicia la aplicación"""
        self.window.mainloop()

if __name__ == "__main__":
    app = ExcelUploaderApp()
    app.run()
