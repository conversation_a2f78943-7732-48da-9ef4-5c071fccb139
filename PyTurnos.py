import pandas as pd
import requests
import mysql.connector
import pyodbc
import numpy as np
from datetime import datetime, timedelta
import sys
import logging
from typing import List, Dict, Optional, Tuple, Any

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('PyTurnos.log'),
        logging.StreamHandler()
    ]
)

def standardize_date_format(df: pd.DataFrame, date_column: str = 'FECHA') -> pd.DataFrame:
    """
    Estandariza el formato de fechas en el DataFrame
    
    Args:
        df: DataFrame con los datos
        date_column: Nombre de la columna con fechas
        
    Returns:
        DataFrame con fechas estandarizadas
    """
    if date_column not in df.columns:
        logging.warning(f"Columna {date_column} no encontrada en el DataFrame")
        return df
    
    logging.info(f"Estandarizando formato de fechas en columna: {date_column}")
    
    # Función para convertir fechas con múltiples formatos
    def parse_date(date_str):
        if pd.isna(date_str) or date_str is None:
            return None
            
        date_str = str(date_str).strip()
        
        # Lista de formatos posibles
        formats = [
            '%m/%d/%Y',    # 9/6/2025
            '%d/%m/%Y',    # 25/11/2024
            '%Y-%m-%d',    # 2025-06-09
            '%d-%m-%Y',    # 09-06-2025
            '%m-%d-%Y',    # 06-09-2025
        ]
        
        for fmt in formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # Convertir a formato estándar MM/dd/yyyy
                return parsed_date.strftime('%m/%d/%Y')
            except ValueError:
                continue
        
        logging.warning(f"No se pudo parsear la fecha: {date_str}")
        return None
    
    # Aplicar la función de conversión
    original_count = len(df)
    df[date_column] = df[date_column].apply(parse_date)
    
    # Contar fechas válidas vs inválidas
    valid_dates = df[date_column].notna().sum()
    invalid_dates = original_count - valid_dates
    
    logging.info(f"Fechas procesadas: {valid_dates} válidas, {invalid_dates} inválidas")
    
    if invalid_dates > 0:
        logging.warning("Registros con fechas inválidas:")
        invalid_samples = df[df[date_column].isna()].head()
        for idx, row in invalid_samples.iterrows():
            logging.warning(f"  Fila {idx}: {row.get('RUT', 'N/A')} - {row.get('NOMBRE', 'N/A')}")
    
    return df

def validate_data_quality(df: pd.DataFrame) -> List[str]:
    """
    Valida la calidad de los datos
    
    Args:
        df: DataFrame con los datos
        
    Returns:
        Lista de problemas de calidad detectados
    """
    logging.info("Validando calidad de datos...")
    
    issues = []
    
    # Verificar columnas críticas
    critical_columns = ['FECHA', 'RUT', 'NOMBRE', 'ZONA']
    for col in critical_columns:
        if col in df.columns:
            null_count = df[col].isna().sum()
            if null_count > 0:
                issues.append(f"Columna {col}: {null_count} valores nulos")
    
    # Verificar rango de fechas
    if 'FECHA' in df.columns:
        valid_dates = pd.to_datetime(df['FECHA'], format='%m/%d/%Y', errors='coerce')
        min_date = valid_dates.min()
        max_date = valid_dates.max()
        
        if min_date and max_date:
            logging.info(f"Rango de fechas: {min_date.date()} a {max_date.date()}")
            
            # Verificar si hay fechas muy antiguas o muy futuras
            today = datetime.now()
            if min_date < today - timedelta(days=365):
                issues.append(f"Hay fechas muy antiguas (anterior a {(today - timedelta(days=365)).date()})")
            if max_date > today + timedelta(days=365):
                issues.append(f"Hay fechas muy futuras (posterior a {(today + timedelta(days=365)).date()})")
    
    if issues:
        logging.warning("Problemas de calidad detectados:")
        for issue in issues:
            logging.warning(f"  - {issue}")
    else:
        logging.info("Validación de calidad: OK")
    
    return issues

def fetch_data(url: str) -> pd.DataFrame:
    """
    Descarga datos desde un CSV en Google Sheets
    
    Args:
        url: URL del CSV en Google Sheets
        
    Returns:
        DataFrame con los datos descargados
    """
    logging.info("Descargando datos CSV desde Google Sheets...")
    df = pd.read_csv(url, low_memory=False)
    logging.info(f"Datos descargados correctamente. {len(df)} filas obtenidas.")
    logging.info(f"Columnas encontradas: {list(df.columns)}")
    return df


def preprocess_data(df: pd.DataFrame) -> Tuple[pd.DataFrame, int, List[str]]:
    """
    Preprocesa los datos: estandariza fechas, valida calidad y limpia valores nulos
    
    Args:
        df: DataFrame con los datos descargados
        
    Returns:
        DataFrame procesado, conteo de registros filtrados, lista de problemas de calidad
    """
    # Estandarizar formato de fechas
    df = standardize_date_format(df, 'FECHA')
    
    # Validar calidad de datos
    quality_issues = validate_data_quality(df)
    
    # Reemplazar NaN con None para que SQL los maneje como NULL
    logging.info("Limpiando datos...")
    df = df.replace({np.nan: None})
    
    # Filtrar solo registros con fechas válidas
    original_count = len(df)
    df = df[df['FECHA'].notna()]
    filtered_count = len(df)
    
    if original_count != filtered_count:
        logging.warning(f"Se filtraron {original_count - filtered_count} registros con fechas inválidas")
    
    logging.info(f"Registros a procesar: {filtered_count}")
    
    return df, filtered_count, quality_issues


def create_column_mapping(df_columns: List[str], table_columns: List[str]) -> Dict[str, str]:
    """
    Crea un mapeo entre las columnas del DataFrame y las columnas de la tabla de la base de datos
    
    Args:
        df_columns: Lista de columnas del DataFrame
        table_columns: Lista de columnas de la tabla de la base de datos
        
    Returns:
        Diccionario con el mapeo de columnas {columna_df: columna_db}
    """
    column_mapping = {}
    for db_col in table_columns:
        for csv_col in df_columns:
            # Comprobamos equivalencia ignorando mayúsculas/minúsculas y espacios por guiones bajos
            if csv_col.lower().replace(' ', '_') == db_col.lower() or csv_col.lower() == db_col.lower():
                column_mapping[csv_col] = db_col
    
    return column_mapping


def prepare_dataframe_for_db(df: pd.DataFrame, column_mapping: Dict[str, str]) -> pd.DataFrame:
    """
    Prepara el DataFrame para insertarlo en la base de datos
    
    Args:
        df: DataFrame con los datos procesados
        column_mapping: Mapeo de columnas {columna_df: columna_db}
        
    Returns:
        DataFrame preparado para la base de datos
    """
    # Verificar si hay mapeo de columnas
    if not column_mapping:
        raise ValueError("No hay columnas coincidentes para insertar en la base de datos")
    
    # Renombrar las columnas en el DataFrame según el mapeo y seleccionar solo las que están en la tabla
    df_db = df[list(column_mapping.keys())].copy()
    df_db.columns = [column_mapping[col] for col in df_db.columns]
    
    # Mostrar columnas que están en el CSV pero no en la tabla
    missing_cols = [col for col in df.columns if col not in column_mapping]
    if missing_cols:
        logging.warning(f"Las siguientes columnas del CSV no existen en la tabla y serán ignoradas: {missing_cols}")
    
    logging.info(f"Columnas que se insertarán: {list(df_db.columns)}")
    
    return df_db


def convert_value_for_sqlserver(value: Any, column: str, sql_type: str) -> Any:
    """
    Convierte un valor al tipo adecuado para SQL Server
    
    Args:
        value: Valor a convertir
        column: Nombre de la columna
        sql_type: Tipo de dato en SQL Server
        
    Returns:
        Valor convertido al tipo adecuado
    """
    # Manejar valores nulos o vacíos de forma robusta
    if pd.isna(value) or value is None or value == '' or (isinstance(value, str) and value.strip() == ''):
        return None
    
    # Convertir explícitamente según el tipo de destino
    try:
        if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
            # Para columnas numéricas de punto flotante
            if isinstance(value, str):
                # Eliminar caracteres no numéricos excepto el punto decimal y signo negativo
                value = ''.join(c for c in value if c.isdigit() or c == '.' or c == '-')
                if not value or value == '-' or value == '.':
                    return None
            # Asegurarse de que el valor sea float
            return float(value)
        elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint'):
            # Para columnas numéricas enteras
            if isinstance(value, float):
                return int(value)
            
            if isinstance(value, str):
                # Eliminar caracteres no numéricos excepto signo negativo
                value = ''.join(c for c in value if c.isdigit() or c == '-')
                if not value or value == '-':
                    return None
            # Asegurarse de que el valor sea int
            return int(value)
        
        # Para otros tipos, devolver el valor tal cual
        return value
    except (ValueError, TypeError):
        logging.warning(f"No se pudo convertir el valor '{value}' para la columna {column} al tipo {sql_type}")
        return None


def prepare_sqlserver_dataframe(df: pd.DataFrame, column_mapping: Dict[str, str], column_types: Dict[str, str]) -> pd.DataFrame:
    """
    Prepara el DataFrame para SQL Server, convirtiendo los tipos de datos según sea necesario
    
    Args:
        df: DataFrame con los datos procesados
        column_mapping: Mapeo de columnas {columna_df: columna_db}
        column_types: Tipos de datos de las columnas {columna: tipo}
        
    Returns:
        DataFrame preparado para SQL Server
    """
    # Preparar el DataFrame para la base de datos
    df_sql = prepare_dataframe_for_db(df, column_mapping)
    
    # Aplicar conversiones a cada columna según su tipo en SQL Server
    for column in df_sql.columns:
        if column in column_types:
            sql_type = column_types[column]
            logging.info(f"Convirtiendo columna {column} al tipo {sql_type}")
            df_sql[column] = df_sql[column].apply(lambda x: convert_value_for_sqlserver(x, column, sql_type))
    
    # Verificación adicional para columnas numéricas
    logging.info("Realizando verificación adicional de tipos para columnas numéricas...")
    for column in df_sql.columns:
        if column in column_types:
            sql_type = column_types[column]
            try:
                if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
                    # Verificar que valores para columnas float sean realmente float o None
                    df_sql[column] = df_sql[column].apply(lambda x: float(x) if x is not None else None)
                elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint'):
                    # Verificar que valores para columnas int sean realmente int o None
                    df_sql[column] = df_sql[column].apply(lambda x: int(x) if x is not None else None)
            except Exception as e:
                logging.error(f"Error en verificación adicional para columna {column}: {str(e)}")
                problematic = df_sql[~df_sql[column].isna()].head(5)
                logging.error(f"Ejemplos de valores en {column}: {problematic[column].tolist()}")
                logging.error(f"Tipos de datos: {[type(x).__name__ for x in problematic[column].tolist()]}")
                
                # Intentar corregir los valores problemáticos
                if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
                    df_sql[column] = df_sql[column].apply(lambda x: float(x) if x is not None and not pd.isna(x) else None)
    
    return df_sql


def insert_into_mysql(df: pd.DataFrame, db_config: Dict[str, str], table_name: str) -> None:
    """
    Inserta datos en una tabla MySQL
    
    Args:
        df: DataFrame con los datos a insertar
        db_config: Configuración de la base de datos
        table_name: Nombre de la tabla
    """
    logging.info(f"Conectando a MySQL ({db_config['host']})...")
    mysql_conn = mysql.connector.connect(**db_config)
    mysql_cursor = mysql_conn.cursor()
    
    try:
        # Obtener columnas de la tabla
        logging.info(f"Obteniendo esquema de la tabla MySQL {table_name}...")
        mysql_cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        table_columns = [row[0] for row in mysql_cursor.fetchall()]
        logging.info(f"Columnas en MySQL: {table_columns}")
        
        # Truncar tabla
        logging.info(f"Truncando tabla MySQL {table_name}...")
        mysql_cursor.execute(f"TRUNCATE TABLE {table_name}")
        mysql_conn.commit()
        
        # Preparar el DataFrame para la base de datos
        column_mapping = create_column_mapping(df.columns, table_columns)
        df_mysql = prepare_dataframe_for_db(df, column_mapping)
        
        # Insertar datos
        logging.info(f"Insertando datos en tabla MySQL {table_name}...")
        cols = df_mysql.columns.tolist()
        placeholders = ", ".join(["%s"] * len(cols))
        columns = ", ".join([f"`{col}`" for col in cols])
        
        # Preparar consulta INSERT
        mysql_insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Insertar por lotes para mejorar rendimiento
        batch_size = 1000
        total_records = len(df_mysql)
        
        for i in range(0, total_records, batch_size):
            batch_end = min(i + batch_size, total_records)
            # Convertir los valores a lista de tuplas, reemplazando NaN por None
            batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df_mysql.iloc[i:batch_end].values]
            mysql_cursor.executemany(mysql_insert_query, batch_data)
            mysql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records} registros en MySQL...")
        
        logging.info("Datos insertados correctamente en MySQL.")
        
    except Exception as e:
        logging.error(f"Error al insertar en MySQL: {str(e)}")
        # Mostrar una muestra de los datos para diagnóstico
        if 'i' in locals() and 'df_mysql' in locals():
            sample = df_mysql.iloc[i:i+5] if i < len(df_mysql) else df_mysql.iloc[-5:]
            logging.error("Muestra de datos que causaron el error:")
            logging.error(str(sample))
            if 'columns' in locals():
                logging.error(f"Columnas en consulta SQL: {columns}")
        raise
    finally:
        mysql_cursor.close()
        mysql_conn.close()


def insert_into_sqlserver(df: pd.DataFrame, connection_string: str, table_name: str) -> None:
    """
    Inserta datos en una tabla SQL Server
    
    Args:
        df: DataFrame con los datos a insertar
        connection_string: Cadena de conexión a SQL Server
        table_name: Nombre de la tabla
    """
    logging.info("Conectando a SQL Server...")
    mssql_conn = pyodbc.connect(connection_string)
    mssql_cursor = mssql_conn.cursor()
    
    try:
        # Truncar tabla
        logging.info(f"Truncando tabla SQL Server {table_name}...")
        mssql_cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL TRUNCATE TABLE {table_name}")
        mssql_conn.commit()
        
        # Obtener columnas de la tabla
        logging.info(f"Obteniendo esquema de la tabla SQL Server {table_name}...")
        mssql_cursor.execute(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}'")
        sql_table_columns = [row[0] for row in mssql_cursor.fetchall()]
        logging.info(f"Columnas en SQL Server: {sql_table_columns}")
        
        # Obtener tipos de datos de las columnas
        logging.info("Obteniendo tipos de datos de las columnas de SQL Server...")
        mssql_cursor.execute(f"""
            SELECT COLUMN_NAME, DATA_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
        """)
        column_types = {row[0]: row[1] for row in mssql_cursor.fetchall()}
        logging.info(f"Tipos de datos en SQL Server: {column_types}")
        
        # Crear mapeo de columnas
        sql_column_mapping = create_column_mapping(df.columns, sql_table_columns)
        
        # Preparar el DataFrame para SQL Server
        df_sql = prepare_sqlserver_dataframe(df, sql_column_mapping, column_types)
        
        # Insertar datos
        logging.info(f"Insertando datos en tabla SQL Server {table_name}...")
        cols = df_sql.columns.tolist()
        placeholders = ", ".join(["?"] * len(cols))
        columns = ", ".join([f"[{col}]" for col in cols])
        
        mssql_insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Insertar por lotes para mejorar rendimiento
        batch_size = 1000
        total_records_sql = len(df_sql)
        
        for i in range(0, total_records_sql, batch_size):
            batch_end = min(i + batch_size, total_records_sql)
            # Preparar lote de datos
            batch_data = []
            for idx, row in df_sql.iloc[i:batch_end].iterrows():
                # Verificar cada valor y ajustar según el tipo de columna
                converted_row = []
                for j, (col, val) in enumerate(zip(cols, row)):
                    if pd.isna(val) or val is None:
                        converted_row.append(None)
                    elif col in column_types:
                        sql_type = column_types[col]
                        # Verificación final de tipo
                        try:
                            if sql_type.lower() in ('float', 'real', 'decimal', 'numeric') and not isinstance(val, float):
                                converted_row.append(float(val))
                            elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint') and not isinstance(val, int):
                                converted_row.append(int(val))
                            else:
                                converted_row.append(val)
                        except (ValueError, TypeError):
                            logging.warning(f"Conversión final fallida para {col} en fila {idx}. Estableciendo a NULL.")
                            converted_row.append(None)
                    else:
                        converted_row.append(val)
                batch_data.append(tuple(converted_row))
            
            mssql_cursor.executemany(mssql_insert_query, batch_data)
            mssql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records_sql} registros en SQL Server...")
        
        logging.info("Datos insertados correctamente en SQL Server.")
        
        # Ejecutar stored procedure
        logging.info("Ejecutando stored procedure SP_INSERT_PY_TURNO...")
        mssql_cursor.execute("exec SP_INSERT_PY_TURNO")
        mssql_conn.commit()
        logging.info("Stored procedure ejecutado correctamente.")
        
    except Exception as e:
        logging.error(f"Error al insertar en SQL Server: {str(e)}")
        
        # Diagnóstico específico para errores de conversión de tipo
        if "Error converting data type nvarchar to float" in str(e) and 'i' in locals() and 'batch_end' in locals() and 'df_sql' in locals() and 'column_types' in locals():
            logging.error("Este es un error de conversión de tipos de datos. Se ha detectado un intento de insertar texto en una columna numérica.")
            
            try:
                # Obtener el lote actual de datos que falló
                current_batch = df_sql.iloc[i:batch_end]
                
                # Revisar cada columna numérica para encontrar valores problemáticos
                for col, tipo in column_types.items():
                    if tipo.lower() in ('float', 'real', 'decimal', 'numeric', 'int', 'bigint', 'smallint', 'tinyint'):
                        problematic_values = current_batch[current_batch[col].apply(
                            lambda x: not (pd.isna(x) or x is None or 
                                        (isinstance(x, (int, float))) or 
                                        (isinstance(x, str) and x.replace('.', '', 1).isdigit()))
                        )]
                        
                        if not problematic_values.empty:
                            logging.error(f"Posible columna problemática: {col} (tipo {tipo})")
                            logging.error(f"Ejemplos de valores problemáticos en {col}:")
                            for idx, row in problematic_values.head().iterrows():
                                logging.error(f"  Fila {idx}: '{row[col]}' (tipo: {type(row[col]).__name__})")
            except Exception as inner_e:
                logging.error(f"Error al intentar diagnosticar el problema: {str(inner_e)}")
        
        # Mostrar una muestra de los datos para diagnóstico general
        if 'i' in locals() and 'df_sql' in locals():
            sample = df_sql.iloc[i:i+5] if i < len(df_sql) else df_sql.iloc[-5:]
            logging.error("Muestra de datos que causaron el error:")
            logging.error(str(sample))
            if 'columns' in locals():
                logging.error(f"Columnas en consulta SQL: {columns}")
        
        raise
    finally:
        mssql_cursor.close()
        mssql_conn.close()


def main() -> None:
    """
    Función principal que ejecuta todo el proceso
    """
    try:
        logging.info("Iniciando proceso de obtención y carga de turnos...")
        
        # Configuración
        url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRyHAgqQ3Rh19AvtOm621ddIUNMXXF8dRjjLKXqu_7rrZ39bYnXOmWNqbUNveqPLkuK19_qQ2H9tBgY/pub?gid=1062603265&single=true&output=csv'
        
        mysql_config = {
            'host': "**************",
            'user': "ncornejo",
            'password': "N1c0l7as17",
            'database': "operaciones_tqw"
        }
        
        sqlserver_conn_string = (
            'DRIVER={ODBC Driver 17 for SQL Server};'
            'SERVER=************;'
            'DATABASE=telqway;'
            'UID=ncornejo;'
            'PWD=N1c0l7as17'
        )
        
        # Obtener y procesar datos
        df = fetch_data(url)
        df, filtered_count, quality_issues = preprocess_data(df)
        
        # Insertar en MySQL
        insert_into_mysql(df, mysql_config, 'tb_turnos_py')
        
        # Insertar en SQL Server
        insert_into_sqlserver(df, sqlserver_conn_string, 'tb_paso_pyTurnos')
        
        # Resumen final
        logging.info("Proceso completado con éxito.")
        logging.info("=== RESUMEN DEL PROCESO ===")
        logging.info(f"Registros procesados: {filtered_count}")
        logging.info(f"Problemas de calidad: {len(quality_issues)}")
        if quality_issues:
            for issue in quality_issues:
                logging.info(f"  - {issue}")
    
    except requests.exceptions.RequestException as e:
        logging.error(f"Error al obtener datos del CSV: {str(e)}")
        sys.exit(1)
    except mysql.connector.Error as e:
        logging.error(f"Error en MySQL: {str(e)}")
        sys.exit(1)
    except pyodbc.Error as e:
        logging.error(f"Error en SQL Server: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Error inesperado: {str(e)}")
        sys.exit(1)
    finally:
        logging.info("Proceso finalizado.")


if __name__ == "__main__":
    main()