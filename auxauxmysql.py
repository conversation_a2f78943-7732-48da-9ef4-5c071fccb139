import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta

import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

try:
    engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')
    Session = sessionmaker(bind=engine)
    session = Session()

    Session2 = sessionmaker(bind=engine)
    session2 = Session()

    engineMYsql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)

    

    Data = pd.read_sql_query("SELECT * from TB_ORACLE_DIRECTA_HISTORICO_pasox", engine)
    
    Data.to_sql('TB_ORACLE_DIRECTA_HISTORICO_pasox', engineMYsql, if_exists='replace', index=False)
   

except Exception as e:
    print(f"An error occurred: {e}")
