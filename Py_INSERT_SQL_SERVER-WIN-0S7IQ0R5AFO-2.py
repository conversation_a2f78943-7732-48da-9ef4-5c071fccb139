import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import time
import numpy as np
import math

import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine


PERIODO_ACTUAL = '202505'

# Función para insertar datos por lotes
def insert_in_batches(dataframe, table_name, engine, if_exists='replace', batch_size=10000):
    """
    Inserta un DataFrame en una base de datos SQL por lotes para evitar problemas de tamaño máximo de paquete.
    
    Args:
        dataframe: DataFrame de pandas a insertar
        table_name: Nombre de la tabla destino
        engine: Conexión SQLAlchemy
        if_exists: Estrategia si la tabla existe ('replace', 'append', 'fail')
        batch_size: Número de filas por lote
    """
    total_rows = len(dataframe)
    total_batches = math.ceil(total_rows / batch_size)
    
    print(f"Insertando {total_rows} registros en '{table_name}' en {total_batches} lotes...")
    
    # Para el primer lote, podemos reemplazar o usar la estrategia indicada
    if total_rows > 0:
        # Primer lote
        first_batch = dataframe.iloc[:min(batch_size, total_rows)]
        first_batch.to_sql(table_name, engine, if_exists=if_exists, index=False)
        print(f"Lote 1/{total_batches} completado ({min(batch_size, total_rows)} registros)")
        
        # Resto de lotes (si hay más de uno)
        for i in range(1, total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch_df = dataframe.iloc[start_idx:end_idx]
            
            # Intentamos varias veces en caso de error temporal de conexión
            max_retries = 3
            retry_count = 0
            success = False
            
            while not success and retry_count < max_retries:
                try:
                    batch_df.to_sql(table_name, engine, if_exists='append', index=False)
                    success = True
                    print(f"Lote {i+1}/{total_batches} completado ({end_idx - start_idx} registros)")
                except Exception as e:
                    retry_count += 1
                    print(f"Error en lote {i+1}, intento {retry_count}: {e}")
                    if retry_count >= max_retries:
                        raise
                    time.sleep(2)  # Esperamos antes de reintentar
    else:
        print(f"No hay datos para insertar en la tabla '{table_name}'")

# Configurar el tamaño máximo de lote
BATCH_SIZE = 5000

try:
    print("Iniciando la carga de datos...")
    
    # Conexiones a las bases de datos
    engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')
    Session = sessionmaker(bind=engine)
    session = Session()

    Session2 = sessionmaker(bind=engine)
    session2 = Session()

    engineMYsql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)

    # Lectura de archivos Excel
    print("Leyendo archivos Excel...")
    data_xls = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Norte.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls2 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Sur.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls3 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Metro.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls4 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Centro.xlsx', 'Sheet1', dtype=str, index_col=None)

    df = pd.DataFrame(data_xls)
    df2 = pd.DataFrame(data_xls2)
    df3 = pd.DataFrame(data_xls3)
    df4 = pd.DataFrame(data_xls4)

    print("Archivos Excel cargados correctamente en DataFrames.")

    # Inserción de datos en SQL Server
    print("Insertando datos en SQL Server...")
    df3.to_sql('tb_paso_pyNdc', engine, if_exists='replace', index=False)
    df2.to_sql('tb_paso_pyNdc', engine, if_exists='append', index=False)
    df.to_sql('tb_paso_pyNdc', engine, if_exists='append', index=False)
    df4.to_sql('tb_paso_pyNdc', engine, if_exists='append', index=False)

    # Ejecución de procedimientos almacenados
    print("Ejecutando procedimientos almacenados en SQL Server...")
    session.execute("exec SP_INSERT_NDC")
    session.commit()
    session.close()

    print("Ejecutando procedimientos almacenados adicionales...")
    # Definir el periodo como variable estática
    
    # Ejecutar procedimientos almacenados con manejo de errores detallado
    sp_list = [
        f"exec SP_CREATE_NDC_PRODUCCION '{PERIODO_ACTUAL}'",
        f"EXEC SP_TQW_COMISION_2023 '{PERIODO_ACTUAL}'",
        f"EXEC SP_TQW_COMISION_2023_V2 '{PERIODO_ACTUAL}'"
    ]
    
    for sp in sp_list:
        try:
            print(f"Ejecutando: {sp}")
            inicio = time.time()
            
            # Usar sintaxis más robusta para SQL Server
            result = session2.execute(f"DECLARE @RC int; {sp}; SELECT @@ROWCOUNT as RowsAffected")
            session2.commit()
            
            # Obtener el resultado si existe
            rows_affected = 0
            try:
                for row in result:
                    rows_affected = row[0]
                    break
            except:
                pass
            
            tiempo_ejecucion = round(time.time() - inicio, 2)
            print(f"  - Completado en {tiempo_ejecucion} segundos ({rows_affected} filas afectadas)")
                
        except Exception as e:
            print(f"  - ERROR: {e}")
            session2.rollback()
            # Decidir si continuar o detener según el SP
            if "SP_CREATE_NDC_PRODUCCION" in sp:
                print("    Error crítico en SP_CREATE_NDC_PRODUCCION. Deteniendo ejecución.")
                raise
            else:
                print("    Continuando con el siguiente SP...")
                continue
    
    session2.close()
    print("Procedimientos almacenados completados.")

    # Lectura de datos para inserción en MySQL
    print("Leyendo datos de SQL Server para transferir a MySQL...")
    Data = pd.read_sql_query("SELECT * from ProduccionNDC WHERE FORMAT(mes_contable,'yyyyMM') >= '202410'", engine)
    Data2 = pd.read_sql_query("SELECT * FROM TB_TQW_COMISION_2023", engine)

    consulta_sql = "SELECT * FROM TB_MYSQL_CHART_NDC_MENSUAL"
    consulta_sql2 = "SELECT * FROM TB_MYSQL_CHART_NDC_DIA"
    consulta_comisiones_new = "SELECT * FROM TB_TQW_COMISION_RENEW"
    consulta_calidad_flujo = "SELECT * FROM vw_CalidadFlujo_kpi"
    tb_vtr_px_diaria = "SELECT * FROM tb_vtr_px_diaria"
    
    Data3 = pd.read_sql_query(consulta_sql, engine)
    Data4 = pd.read_sql_query(consulta_sql2, engine)
    Data5 = pd.read_sql_query(consulta_comisiones_new, engine)
    Data6 = pd.read_sql_query(tb_vtr_px_diaria, engine)
    
    print("Leyendo datos de vw_CalidadFlujo_kpi...")
    vw_CalidadFlujo_kpi = pd.read_sql_query(consulta_calidad_flujo, engine)
    print(f"Se obtuvieron {len(vw_CalidadFlujo_kpi)} registros de vw_CalidadFlujo_kpi")
    
    # Nueva consulta para produccion_ndc_rank_red
    print("Leyendo datos de produccion_ndc_rank_red...")
    consulta_produccion_rank = f"SELECT * FROM produccion_ndc_rank_red WHERE FORMAT(mes_contable,'yyyyMM') = '{PERIODO_ACTUAL}'"
    produccion_rank_data = pd.read_sql_query(consulta_produccion_rank, engine)
    print(f"Se obtuvieron {len(produccion_rank_data)} registros de produccion_ndc_rank_red")

    # Inserción de datos en MySQL por lotes
    print("Insertando datos en MySQL por lotes...")
    
    # Tablas más pequeñas primero
    insert_in_batches(Data3, 'tb_mysql_chart_ndc_mensual', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    insert_in_batches(Data4, 'tb_mysql_chart_ndc_dia', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    insert_in_batches(Data5, 'tb_tqw_comision_renew', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    insert_in_batches(Data6, 'tb_vtr_px_diaria', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    
    # Insertar vw_CalidadFlujo_kpi
    print(f"Insertando datos en vw_CalidadFlujo_kpi ({len(vw_CalidadFlujo_kpi)} registros)...")
    insert_in_batches(vw_CalidadFlujo_kpi, 'vw_calidadflujo_kpi', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    
    # Insertar produccion_ndc_rank_red
    print(f"Insertando datos en produccion_ndc_rank_red ({len(produccion_rank_data)} registros)...")
    insert_in_batches(produccion_rank_data, 'produccion_ndc_rank_red', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    
    # Tablas más grandes ahora
    print(f"Iniciando inserción de datos en tb_paso_pyndc ({len(Data)} registros)...")
    insert_in_batches(Data, 'tb_paso_pyndc', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)
    
    print(f"Iniciando inserción de datos en tb_paso_kpi2023 ({len(Data2)} registros)...")
    insert_in_batches(Data2, 'tb_paso_kpi2023', engineMYsql, if_exists='replace', batch_size=BATCH_SIZE)

    print("¡Proceso de transferencia de datos completado exitosamente!")

    
    
except Exception as e:
    print(f"Error durante la ejecución: {e}")
    import traceback
    traceback.print_exc()
