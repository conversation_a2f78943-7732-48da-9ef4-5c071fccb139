from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import sys
import logging

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('edge_script.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Mostrar banner de inicio
start_time = time.time()
print("="*60)
print(f"INICIANDO SCRIPT DE NAVEGACIÓN EDGE - {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*60)

try:
    # Configuración de opciones para Edge
    print("1. Configurando navegador...")
    edge_options = Options()
    edge_options.add_argument('--start-maximized')
    edge_options.add_argument('--disable-gpu')
    edge_options.add_argument('--no-sandbox')
    edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    # Usar el driver local en vez de la descarga automática
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")
        
        if not os.path.exists(msedgedriver_path):
            print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
            print("Por favor, asegúrese de tener el driver de Edge en la misma carpeta que este script")
            sys.exit(1)
        
        print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
        service = Service(msedgedriver_path)
        driver = webdriver.Edge(service=service, options=edge_options)
        print("Driver Edge inicializado correctamente")
    except Exception as e:
        print(f"Error de inicialización del driver: {str(e)}")
        sys.exit(1)
        
    # Configurar tiempo de espera para elementos
    wait = WebDriverWait(driver, 10)
    
    # Navegar a una página web
    print("2. Navegando a una página web de prueba...")
    try:
        # URL de Oracle Cloud
        url_prueba = "https://vtr.fs.ocs.oraclecloud.com/"
        print(f"   - Accediendo a: {url_prueba}")
        driver.get(url_prueba)
        
        # Esperar a que la página cargue
        print("   - Esperando que la página cargue completamente...")
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        
        # Verificar título de la página
        titulo = driver.title
        print(f"   - Página cargada. Título: {titulo}")
        
        # No hay espera limitada
        print("   - Navegador abierto y listo para uso")
        
        print("   - Navegación completada exitosamente")
    except Exception as e:
        print(f"   - ERROR: No se pudo navegar a la página: {str(e)}")
        raise

except Exception as e:
    print(f"ERROR GENERAL: {e}")
    logger.error(f"Error en la ejecución: {str(e)}")
    import traceback
    print("Detalles:")
    print(traceback.format_exc())

finally:
    # Mostrar resumen final con información de tiempo transcurrido
    end_time = time.time()
    duration = end_time - start_time
    print("\n" + "="*60)
    print(f"PROCESO FINALIZADO - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Tiempo de ejecución: {duration:.2f} segundos")
    print("="*60)
    
    # No cerramos el navegador automáticamente
    print("Navegador abierto. No se cerrará automáticamente.")