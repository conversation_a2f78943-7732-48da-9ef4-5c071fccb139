import pandas as pd
import datetime as date
import requests
import json
from datetime import datetime, timedelta
import os
import logging
import pyodbc
# mysql.connector se importa dinámicamente en la función para evitar problemas de importación

# Configurar logging con nombre del flujo
LOG_NAME = 'PyTOA_Flujo23'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [' + LOG_NAME + '] %(message)s',
    handlers=[
        logging.FileHandler('pytoa_flujo.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def configure_test_mode():
    """
    Configurar el modo de ejecución del proceso
    
    Returns:
        tuple: (TEST_MODE, TEST_ROWS, TEST_TYPE)
            - TEST_MODE (bool): True para modo prueba, False para modo completo
            - TEST_ROWS (int): Número de filas a procesar en modo prueba
            - TEST_TYPE (str): Tipo de prueba ('small', 'medium', 'full')
    """
    # CONFIGURACIÓN AQUÍ - Fácil de cambiar
    
    # Opciones disponibles:
    # 1. TEST_MODE = False: Procesa TODO el archivo (291,000+ registros)
    # 2. TEST_MODE = True y TEST_TYPE = 'small': Procesa 1,000 registros (prueba rápida)
    # 3. TEST_MODE = True y TEST_TYPE = 'medium': Procesa 10,000 registros (prueba media)
    
    TEST_MODE = False    # True = modo prueba, False = modo completo
    TEST_TYPE = 'full'  # 'small' = 1000 filas, 'medium' = 10000 filas, 'full' = todas las filas
    
    # Determinar el número de filas basado en TEST_TYPE
    if TEST_TYPE == 'small':
        TEST_ROWS = 1000
    elif TEST_TYPE == 'medium':
        TEST_ROWS = 10000
    else:  # 'full' o cualquier otro valor
        TEST_ROWS = None  # Procesar todo el archivo
        
    return TEST_MODE, TEST_ROWS, TEST_TYPE

def insert_with_pyodbc_direct(df, table_name='tb_andes_python'):
    """
    Inserción directa usando pyodbc sin SQLAlchemy
    """
    logger.info("Intentando inserción directa con pyodbc...")
    
    try:
        # Conexión directa con pyodbc
        conn_str = (
            'DRIVER={ODBC Driver 17 for SQL Server};'
            'SERVER=************;'
            'DATABASE=telqway;'
            'UID=ncornejo;'
            'PWD=N1c0l7as17'
        )
        
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Eliminar tabla si existe
        logger.info(f"Eliminando tabla {table_name} si existe...")
        cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE {table_name}")
        conn.commit()
        
        # Agregar columna fecha_actualizacion al DataFrame primero
        fecha_actualizacion = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"Agregando columna fecha_actualizacion con valor: {fecha_actualizacion}")
        # Crear una copia del DataFrame para evitar la fragmentación
        df = df.copy()
        df['fecha_actualizacion'] = fecha_actualizacion
        
        # Crear tabla con columnas más grandes para evitar truncamiento
        logger.info("Creando tabla con columnas ampliadas...")
        columns_sql = []
        for col in df.columns:
            # Escape nombres de columnas con espacios o caracteres especiales
            col_name = f"[{col}]"
            # Utilizar NVARCHAR(MAX) para todas las columnas para evitar truncamiento
            # Esto podría usar más espacio de almacenamiento, pero es más seguro para datos variables
            columns_sql.append(f"{col_name} NVARCHAR(MAX)")
        
        create_table_sql = f"CREATE TABLE {table_name} ({', '.join(columns_sql)})"
        cursor.execute(create_table_sql)
        conn.commit()
        logger.info("Tabla creada exitosamente con columna fecha_actualizacion incluida")
        
        # Insertar datos en lotes
        # Aumentar el tamaño del lote para mejorar rendimiento
        batch_size = 500
        total_rows = len(df)
        
        logger.info(f"Insertando {total_rows} filas en lotes de {batch_size}...")
        
        # Preparar statement de inserción
        placeholders = ', '.join(['?' for _ in df.columns])
        columns_names = ', '.join([f"[{col}]" for col in df.columns])
        insert_sql = f"INSERT INTO {table_name} ({columns_names}) VALUES ({placeholders})"
        
        # Insertar en lotes
        for i in range(0, total_rows, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_data = []
            
            for _, row in batch_df.iterrows():
                # Convertir cada valor a string y limpiar
                row_data = []
                for val in row:
                    if pd.isna(val) or val is None:
                        row_data.append(None)  # Usar None en lugar de cadenas vacías
                    else:
                        # Convertir a string
                        str_val = str(val)
                        row_data.append(str_val)
                        
                        # Detectar valores potencialmente problemáticos (muy largos)
                        if len(str_val) > 4000:
                            logger.warning(f"Valor muy largo detectado en fila {i+batch_df.index.get_loc(row.name)} columna: {len(str_val)} caracteres")
                batch_data.append(tuple(row_data))
            
            # Ejecutar inserción del lote
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            logger.info(f"Lote {i//batch_size + 1}/{(total_rows-1)//batch_size + 1} insertado")
        
        # Cerrar conexión
        cursor.close()
        conn.close()
        
        logger.info(f"Inserción directa exitosa: {total_rows} registros")
        return True
        
    except Exception as e:
        logger.error(f"Error en inserción directa con pyodbc: {e}")
        # Imprimir más detalles del error para diagnóstico
        if hasattr(e, 'args') and len(e.args) > 1:
            logger.error(f"Detalles del error: {e.args}")
        return False

def insert_mysql_direct(df, table_name, mysql_config):
    """
    Inserción directa en MySQL usando mysql.connector
    """
    try:
        import mysql.connector
        
        logger.info(f"Insertando en MySQL tabla {table_name} con conexión directa...")
        
        # Crear conexión
        conn = mysql.connector.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )
        cursor = conn.cursor()
        
        # Eliminar tabla si existe
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        
        # Crear tabla
        columns_sql = []
        for col in df.columns:
            # Escapar nombres de columnas y usar TEXT para compatibilidad
            col_name = f"`{col}`"
            columns_sql.append(f"{col_name} TEXT")
        
        create_table_sql = f"CREATE TABLE {table_name} ({', '.join(columns_sql)})"
        cursor.execute(create_table_sql)
        conn.commit()
        
        # Insertar datos en lotes
        batch_size = 1000
        total_rows = len(df)
        
        # Preparar statement
        placeholders = ', '.join(['%s' for _ in df.columns])
        columns_names = ', '.join([f"`{col}`" for col in df.columns])
        insert_sql = f"INSERT INTO {table_name} ({columns_names}) VALUES ({placeholders})"
        
        # Insertar en lotes
        for i in range(0, total_rows, batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_data = []
            
            for _, row in batch_df.iterrows():
                row_data = []
                for val in row:
                    if pd.isna(val) or val is None:
                        row_data.append(None)
                    else:
                        row_data.append(str(val))
                batch_data.append(tuple(row_data))
            
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            
            logger.info(f"MySQL: Lote {i//batch_size + 1}/{(total_rows-1)//batch_size + 1} insertado")
        
        cursor.close()
        conn.close()
        
        logger.info(f"Inserción MySQL exitosa: {total_rows} registros en {table_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error en inserción MySQL directa: {e}")
        return False

def main():
    try:
        # Obtener configuración de modo de prueba
        TEST_MODE, TEST_ROWS, TEST_TYPE = configure_test_mode()
        
        logger.info("="*50)
        if TEST_MODE:
            if TEST_TYPE == 'small':
                logger.info(f"INICIANDO PROCESO PyTOA_Flujo23 - MODO PRUEBA PEQUEÑA ({TEST_ROWS} filas)")
            elif TEST_TYPE == 'medium':
                logger.info(f"INICIANDO PROCESO PyTOA_Flujo23 - MODO PRUEBA MEDIA ({TEST_ROWS} filas)")
            else:
                logger.info(f"INICIANDO PROCESO PyTOA_Flujo23 - MODO PRUEBA COMPLETA")
        else:
            logger.info("INICIANDO PROCESO PyTOA_Flujo23 - MODO COMPLETO")
        logger.info("="*50)
        
        # Configuración de períodos
        periodo = 'Sep'  
        periodo2 = '202509'
        
        # Lectura del archivo Excel
        logger.info("PASO 1: Leyendo archivo Excel...")
        excel_path = fr'C:\Users\<USER>\Downloads\Consolidado Actividades Andes VTR RGU {periodo}-25.xlsx'
        logger.info(f"Ruta del archivo: {excel_path}")
        
        if TEST_MODE and TEST_ROWS:
            logger.info(f"MODO PRUEBA: Leyendo solo las primeras {TEST_ROWS} filas...")
            data_xls = pd.read_excel(excel_path, 'Base', dtype=str, index_col=None, nrows=TEST_ROWS)
            
            # Mostramos el tipo de prueba seleccionada
            if TEST_TYPE == 'small':
                logger.info("Prueba PEQUEÑA seleccionada: procesando 1,000 registros para prueba rápida")
            elif TEST_TYPE == 'medium':
                logger.info("Prueba MEDIA seleccionada: procesando 10,000 registros para prueba más completa")
        else:
            logger.info("MODO COMPLETO: Leyendo todo el archivo...")
            data_xls = pd.read_excel(excel_path, 'Base', dtype=str, index_col=None)
        
        logger.info(f"Archivo leído exitosamente. Dimensiones: {data_xls.shape}")
        if TEST_MODE and TEST_ROWS:
            logger.info(f"RECORDATORIO: Estás en modo prueba con {data_xls.shape[0]} filas")
        
        # Configuración de conexiones directas sin SQLAlchemy
        logger.info("PASO 2: Configurando conexiones de base de datos...")
        
        # Ya no usamos SQLAlchemy, las conexiones se crean directamente en las funciones
        # cuando son necesarias, usando pyodbc y mysql.connector
        
        # Configuración MySQL
        mysql_config = {
            'host': '**************',
            'port': 3306,
            'user': 'ncornejo',
            'password': 'N1c0l7as17',
            'database': 'operaciones_tqw'
        }
        
        # Configuración SQL Server - Solo guardamos los parámetros, no creamos conexión aún
        sql_server_config = {
            'driver': 'ODBC Driver 17 for SQL Server',
            'server': '************',
            'database': 'telqway',
            'uid': 'ncornejo',
            'pwd': 'N1c0l7as17'
        }
        
        logger.info("Conexiones configuradas exitosamente")
        
        # Transformación de datos (usando tu lógica original)
        logger.info("PASO 3: Transformando datos...")
        
        df = pd.DataFrame(data_xls)
        logger.info(f"DataFrame creado: {df.shape}")
        
        # Añadir campo fecha_carga con la fecha actual
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        df.insert(110, 'Tipo_work', df['tipo_actividad'])
        df['fecha_carga'] = current_date
        logger.info("Campos agregados: Tipo_work y fecha_carga")
        
        # Renombrar columnas
        df.rename(columns={
            'TipoWorkSkill':'tipo_actividad', 
            'tipo_actividad':'evalua_dpendiente', 
            'evalua_dpendiente':'Origen', 
            'Origen':'RGU_OPERACIONES'
        }, inplace=True)
        logger.info("Columnas renombradas exitosamente")
        
        # LIMPIEZA DE DATOS - Asegurar que todos los valores sean strings válidos
        logger.info("Limpiando datos para asegurar compatibilidad...")
        
        # Convertir todas las columnas a string y limpiar valores problemáticos
        for col in df.columns:
            # Convertir a string, reemplazar NaN y valores problemáticos
            df[col] = df[col].astype(str)
            df[col] = df[col].replace('nan', '')
            df[col] = df[col].replace('<NA>', '')
            df[col] = df[col].replace('None', '')
            df[col] = df[col].fillna('')
        
        # Verificación específica para la columna Técnico
        if 'Técnico' in df.columns:
            logger.info("Verificando columna Técnico...")
            # Limpiar la columna Técnico específicamente
            df['Técnico'] = df['Técnico'].astype(str)
            df['Técnico'] = df['Técnico'].str.strip()  # Eliminar espacios
            df['Técnico'] = df['Técnico'].replace('', 'SIN_TECNICO')  # Reemplazar vacíos
            
            # Truncar a 100 caracteres para cumplir con VARCHAR(100)
            df['Técnico'] = df['Técnico'].str[:100]
            
            # Verificar algunos valores
            unique_tecnicos = df['Técnico'].unique()[:5]
            logger.info(f"Ejemplos de técnicos: {unique_tecnicos}")
            logger.info(f"Longitud máxima en Técnico: {df['Técnico'].str.len().max()}")
        
        logger.info("Limpieza de datos completada")
        
        # Ya no necesitamos definir tipos de datos para SQLAlchemy
        # Los tipos de columnas se definen directamente en la creación de tablas
        # dentro de las funciones insert_with_pyodbc_direct e insert_mysql_direct
        
        logger.info("Datos transformados listos para inserción")
        logger.info("Transformación de datos completada")
        
        # VERIFICACIÓN FINAL antes de insertar
        logger.info("Realizando verificación final de datos...")
        
        # Verificar que no haya valores nulos en columnas críticas
        critical_columns = ['Técnico', 'Orden de Trabajo', 'Cliente']
        for col in critical_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    logger.warning(f"Columna {col} tiene {null_count} valores nulos - limpiando...")
                    df[col] = df[col].fillna('').astype(str)
        
        # Verificación específica del DataFrame antes de SQL
        logger.info(f"Verificación final - Shape: {df.shape}")
        logger.info(f"Tipos de datos únicos: {df.dtypes.unique()}")
        
        # Verificar la columna Técnico una vez más
        if 'Técnico' in df.columns:
            tecnico_info = {
                'count': len(df['Técnico']),
                'nulls': df['Técnico'].isnull().sum(),
                'empty': (df['Técnico'] == '').sum(),
                'max_length': df['Técnico'].str.len().max(),
                'data_type': df['Técnico'].dtype
            }
            logger.info(f"Info columna Técnico: {tecnico_info}")
        
        logger.info("Verificación completada - procediendo con inserción...")
        
        # Inserción en SQL Server
        logger.info("PASO 4: Insertando datos en SQL Server...")
        start_time = datetime.now()
        
        # Intentar inserción directa con pyodbc
        success = insert_with_pyodbc_direct(df, 'tb_andes_python')
        
        if not success:
            raise Exception("La inserción en SQL Server falló")
        
        insert_time = datetime.now() - start_time
        logger.info(f"Inserción completada en {insert_time.total_seconds():.2f} segundos")
        logger.info(f"Registros insertados: {len(df)}")
        
        # Ejecución de Stored Procedures directamente con pyodbc
        logger.info("PASO 5: Ejecutando Stored Procedures...")
        
        try:
            # Ejecutar stored procedures con pyodbc
            conn_str = (
                'DRIVER={ODBC Driver 17 for SQL Server};'
                'SERVER=************;'
                'DATABASE=telqway;'
                'UID=ncornejo;'
                'PWD=N1c0l7as17'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            logger.info(f"Ejecutando SP_INSERT_TOA_FIN con parámetro: {periodo2}")
            cursor.execute(f"EXEC SP_INSERT_TOA_FIN '{periodo2}'")
            conn.commit()
            logger.info("SP_INSERT_TOA_FIN ejecutado exitosamente")
            
            logger.info(f"Ejecutando SP_CREATE_SME_FACTURA con parámetro: {periodo2}")
            cursor.execute(f"EXEC SP_CREATE_SME_FACTURA '{periodo2}'")
            conn.commit()
            logger.info("SP_CREATE_SME_FACTURA ejecutado exitosamente")
            
            cursor.close()
            conn.close()
            logger.info("Conexión SQL Server cerrada después de stored procedures")
            
        except Exception as e:
            logger.error(f"Error en stored procedures: {e}")
            logger.warning("Stored procedures fallaron, pero la inserción principal fue exitosa")
        
        # Transferencia de datos a MySQL
        logger.info("PASO 6: Transfiriendo datos a MySQL...")
        
        # Configuración MySQL
        mysql_config = {
            'host': '**************',
            'port': 3306,
            'user': 'ncornejo',
            'password': 'N1c0l7as17',
            'database': 'operaciones_tqw'
        }
        
        try:
            # Primera consulta: TB_KPI_GERENCIA_RGU_EPS
            logger.info("Consultando TB_KPI_GERENCIA_RGU_EPS...")
            query_RGU = "SELECT * FROM TB_KPI_GERENCIA_RGU_EPS"
            
            # Consulta directa con pyodbc
            conn_str = (
                'DRIVER={ODBC Driver 17 for SQL Server};'
                'SERVER=************;'
                'DATABASE=telqway;'
                'UID=ncornejo;'
                'PWD=N1c0l7as17'
            )
            conn = pyodbc.connect(conn_str)
            Data = pd.read_sql_query(query_RGU, conn)
            conn.close()
            logger.info(f"Datos obtenidos con pyodbc: {len(Data)} registros")
            
            # Insertar en MySQL usando método directo
            success = insert_mysql_direct(Data, 'tb_kpi_gerencia_rgu_eps', mysql_config)
            if not success:
                logger.error("La inserción MySQL directa falló y no hay fallback a SQLAlchemy")
            
        except Exception as e:
            logger.error(f"Error en primera transferencia MySQL: {e}")
            logger.info("Continuando con la segunda consulta...")
        
        try:
            # Segunda consulta: TB_KPI_GERENCIA_RGU_TECNICOS
            logger.info("Consultando TB_KPI_GERENCIA_RGU_TECNICOS...")
            query_RGU = "SELECT * FROM TB_KPI_GERENCIA_RGU_TECNICOS"
            
            # Consulta directa con pyodbc
            conn_str = (
                'DRIVER={ODBC Driver 17 for SQL Server};'
                'SERVER=************;'
                'DATABASE=telqway;'
                'UID=ncornejo;'
                'PWD=N1c0l7as17'
            )
            conn = pyodbc.connect(conn_str)
            Data = pd.read_sql_query(query_RGU, conn)
            conn.close()
            logger.info(f"Datos obtenidos con pyodbc: {len(Data)} registros")
            
            # Insertar en MySQL usando método directo
            success = insert_mysql_direct(Data, 'tb_kpi_gerencia_rgu_tecnicos', mysql_config)
            if not success:
                logger.error("La inserción MySQL directa falló y no hay fallback a SQLAlchemy")
            
        except Exception as e:
            logger.error(f"Error en segunda transferencia MySQL: {e}")
            logger.info("Proceso principal completado, solo falló transferencia MySQL secundaria")
        
        # Finalización
        logger.info("="*50)
        if TEST_MODE:
            logger.info("PROCESO DE PRUEBA COMPLETADO EXITOSAMENTE")
            logger.info(f"Se procesaron {len(df)} registros en modo prueba {TEST_TYPE}")
            if TEST_TYPE == 'small':
                logger.info("Para ejecutar una prueba más completa, cambia TEST_TYPE = 'medium' en configure_test_mode()")
            elif TEST_TYPE == 'medium':
                logger.info("Para ejecutar el proceso completo, cambia TEST_MODE = False en configure_test_mode()")
            else:
                logger.info("Para ejecutar el proceso completo con todos los registros, cambia TEST_MODE = False")
        else:
            logger.info("PROCESO COMPLETO COMPLETADO EXITOSAMENTE")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"ERROR CRÍTICO EN EL PROCESO: {str(e)}")
        logger.error(f"Tipo de error: {type(e).__name__}")
        
        # Cerrar conexiones en caso de error
        try:
            if 'session' in locals():
                session.close()
        except:
            pass
        
        raise

if __name__ == "__main__":
    main()