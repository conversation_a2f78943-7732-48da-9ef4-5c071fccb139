import pandas as pd
import datetime 
import requests
import json
from sqlalchemy import create_engine, <PERSON><PERSON><PERSON>, Inte<PERSON>, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import time
import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

from selenium import webdriver
from selenium.webdriver.common.keys import Keys
from openpyxl import Workbook


import os  # Importar la librería os

from selenium.webdriver.edge.service import Service
from webdriver_manager.microsoft import EdgeChromiumDriverManager

# Configura el driver automáticamente
service = Service(EdgeChromiumDriverManager().install())
driver = webdriver.Edge(service=service)


engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')


Session = sessionmaker(bind=engine)
session = Session()

# Ruta del archivo Excel
archivo_excel = r'C:\Users\<USER>\Downloads\Data Reporte OT Digita  Agosto a Diciembre 2024.xlsx'

# Verificar si el archivo existe
if os.path.exists(archivo_excel):
    os.remove(archivo_excel)
    print("El archivo Excel ha sido eliminado.")
else:
    print("El archivo Excel no existe.")







driver.get("https://desarrollo.telqway.cl/index.php/s/2CerKzYBT4KoLFY/download?path=%2F&files=Data%20Reporte%20OT%20Digita%20%20Agosto%20a%20Diciembre%202024.xlsx")



time.sleep(80)


# # Importar el archivo XLSX a un DataFrame
data_xls = pd.read_excel(archivo_excel, 'Hoja1', dtype=str, index_col=None)




# Data.to_sql('TB_LOGIS_CIERRE_INVENTARIO2', engineMYsql, if_exists='replace',index=False)

data_xls.to_sql('TB_REPORT_OT_DIGITAL', engine, if_exists='replace',index=False)
time.sleep(20)

session.execute("EXEC SP_REPORTE_OT_DIGITAL")  
session.commit()

consulta_sql = "SELECT * FROM TB_MYSQL_CHART_OT_DIGITAL_EPS"
consulta_sql2 = "SELECT * FROM TB_MYSQL_CHART_OT_DIGITAL_TECNICO"
consulta_sql3 = "SELECT * FROM TB_REPORT_OT_DIGITAL_TQW"
consulta_sql4 = "SELECT * FROM TB_MYSQL_CHART_OT_DIGITAL"
consulta_sql5 = "SELECT * FROM TB_MYSQL_CHART_OT_DIGITAL_SUPER_DIA"

consulta_sql6 = "SELECT * FROM TB_MYSQL_BASE_OT_DIGITAL"

# consulta_sql5 = "SELECT * FROM TB_MYSQL_CHART_NDC_MENSUAL"
# consulta_sql6 = "SELECT * FROM TB_MYSQL_CHART_NDC_DIA"


Data1 = pd.read_sql_query(consulta_sql, engine)
Data2 = pd.read_sql_query(consulta_sql2, engine)
Data3 = pd.read_sql_query(consulta_sql3, engine)
Data4 = pd.read_sql_query(consulta_sql4, engine)

Data6 = pd.read_sql_query(consulta_sql6, engine)



# engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)
engineMYsql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)
Data1.to_sql('tb_mysql_chart_ot_digital_eps', engineMYsql, if_exists='replace',index=False)
Data2.to_sql('tb_mysql_chart_ot_digital_tecnico', engineMYsql, if_exists='replace',index=False)
# Data3.to_sql('tb_report_ot_digital_tqw', engineMYsql, if_exists='replace',index=False)
Data4.to_sql('tb_mysql_chart_ot_digital', engineMYsql, if_exists='replace',index=False)
Data6.to_sql('tb_mysql_base_ot_digital', engineMYsql, if_exists='replace',index=False)





# Assuming Data1, Data2, Data3, Data4, and Data6 are your DataFrames

# Get the current script's directory
# current_dir = os.path.dirname(os.path.abspath(__file__))

# # Create a new Excel file
# excel_file = os.path.join(current_dir, 'ReporteOtDigital_Output.xlsx')

# # Create a Pandas Excel writer using openpyxl as the engine
# with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
#     # Write each DataFrame to a different sheet
#     Data1.to_excel(writer, sheet_name='Sheet1', index=False)
#     Data2.to_excel(writer, sheet_name='Sheet2', index=False)
#     Data3.to_excel(writer, sheet_name='Sheet3', index=False)
#     Data4.to_excel(writer, sheet_name='Sheet4', index=False)
#     Data6.to_excel(writer, sheet_name='Sheet6', index=False)

# print(f"Excel file created successfully at: {excel_file}")

