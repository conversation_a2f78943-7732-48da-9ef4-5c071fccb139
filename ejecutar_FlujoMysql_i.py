"""
Script independiente para ejecutar FlujoMysql_i.py cada 3 minutos
"""
import subprocess
import time
import os
from datetime import datetime
import logging

# Configurar logging
LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILE = os.path.join(LOG_DIR, f"ejecutor_FlujoMysql_i_{datetime.now().strftime('%Y%m%d')}.log")

# Configurar el logger
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# También enviar logs a consola
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger = logging.getLogger()
logger.addHandler(console_handler)

# Ruta del script a ejecutar
SCRIPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "FlujoMysql_i.py")

# Verificar que el archivo exista
if not os.path.exists(SCRIPT_PATH):
    logger.error(f"Error: El archivo {SCRIPT_PATH} no existe.")
    # Buscar en el directorio actual
    alternative_path = "FlujoMysql_i.py"
    if os.path.exists(alternative_path):
        SCRIPT_PATH = alternative_path
        logger.info(f"Se encontró una ruta alternativa: {SCRIPT_PATH}")
    else:
        logger.error("No se pudo encontrar el archivo. Saliendo...")
        exit(1)

logger.info("==================================================")
logger.info(f"Iniciando ejecutor para {SCRIPT_PATH}")
logger.info("Este script ejecutará FlujoMysql_i.py cada 3 minutos")
logger.info("==================================================")

def ejecutar_script():
    """Ejecuta el script y registra la salida"""
    try:
        logger.info(f"Ejecutando: python {SCRIPT_PATH}")
        # Ejecutar el proceso y capturar la salida
        process = subprocess.Popen(
            ["python", SCRIPT_PATH],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Esperar a que termine y obtener la salida
        stdout, stderr = process.communicate()
        
        # Registrar el resultado
        if process.returncode == 0:
            logger.info(f"Ejecución exitosa (código de salida: {process.returncode})")
            if stdout.strip():
                logger.info(f"Salida del proceso:\n{stdout.strip()}")
        else:
            logger.error(f"Error en la ejecución (código de salida: {process.returncode})")
            if stderr.strip():
                logger.error(f"Error del proceso:\n{stderr.strip()}")
    
    except Exception as e:
        logger.error(f"Excepción al ejecutar el script: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

# Bucle principal
ejecuciones = 0
try:
    while True:
        # Ejecutar el script
        hora_inicio = datetime.now()
        logger.info(f"Ejecución #{ejecuciones+