"""
Script para probar el envío de reportes por correo
"""
import pandas as pd
from sqlalchemy import create_engine, text
from datetime import datetime
import logging

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# Configurar la consola para UTF-8 en Windows
import sys
if sys.platform.startswith('win'):
    try:
        import os
        os.system('chcp 65001 > nul 2>&1')  # Cambiar a UTF-8 sin mostrar salida
    except:
        pass
logger = logging.getLogger(__name__)

def get_db_connection():
    """Crea y devuelve una conexión a la base de datos SQL Server"""
    # Usar la misma cadena de conexión que en PyTOA30.py
    connection_string = 'mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server'
    return create_engine(connection_string, fast_executemany=True)

def send_report():
    """Envía el reporte de técnicos Px0 por correo"""
    try:
        logger.info("Conectando a la base de datos...")
        engine = get_db_connection()
        
        # Obtener la fecha actual en formato MM/DD/YY
        fecha_actual = datetime.now().strftime('%m/%d/%y')
        # Para pruebas, mostrar la fecha que se está usando
        logger.info(f"Usando fecha: {fecha_actual} para las consultas")
        
        # Consulta para obtener la hora de descarga y actualización
        metadata_query = f"""
        SELECT 
            MAX(fecha_carga) as ultima_carga,
            MAX(CASE WHEN [Estado] IN ('Completado', 'No realizada') THEN [Fin] ELSE NULL END) as ultima_actualizacion
        FROM TB_TOA_30DIAS_CLOUD 
        WHERE [Técnico] LIKE '%TQW%' 
            AND [Fecha] = '{fecha_actual}'
            AND [Técnico] NOT LIKE '%SME%'
            AND [Técnico] != 'Virtual_TQW_Sergio Acevedo P'
            AND [Técnico] != 'Virtual TQW Negocios'
        """
        
        # Consulta principal para los técnicos
        query = f"""
        SELECT  
            cloud.[Técnico],
            ISNULL(dotacion.supervisor, 'Sin supervisor') as supervisor,
            COUNT(*) as Total_Ordenes,
            SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) as Completados,
            SUM(CASE WHEN cloud.[Estado] = 'Pendiente' THEN 1 ELSE 0 END) as Pendientes,
            SUM(CASE WHEN cloud.[Estado] = 'Iniciado' THEN 1 ELSE 0 END) as Iniciados,
            SUM(CASE WHEN cloud.[Estado] = 'Agendada' THEN 1 ELSE 0 END) as Agendadas,
            SUM(CASE WHEN cloud.[Estado] = 'No realizada' THEN 1 ELSE 0 END) as NoRealizada,
            SUM(CASE WHEN cloud.[Estado] NOT IN ('No realizada','Completado', 'Pendiente', 'Iniciado', 'Agendada') OR cloud.[Estado] IS NULL THEN 1 ELSE 0 END) as Otros_Estados,
            CASE 
                WHEN SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0 THEN 'Px0'
                ELSE 'OK'
            END as Categoria,
            CASE 
                WHEN SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0 
                     AND SUM(CASE WHEN cloud.[Estado] = 'Pendiente' THEN 1 ELSE 0 END) = 0 THEN 'Critico'
                WHEN SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0 
                     AND SUM(CASE WHEN cloud.[Estado] = 'Iniciado' THEN 1 ELSE 0 END) = 1 THEN 'Malo'
                WHEN SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0 THEN 'Muy Malo'
                ELSE NULL
            END as Subcategoria
        FROM TB_TOA_30DIAS_CLOUD cloud
        LEFT JOIN tb_user_tqw dotacion ON LTRIM(RTRIM(dotacion.rut)) = LTRIM(RTRIM(cloud.[Rut o Bucket]))
        WHERE cloud.[Técnico] LIKE '%TQW%' 
            AND cloud.[Fecha] = '{fecha_actual}'
            AND cloud.[Técnico] NOT LIKE '%SME%'
            AND cloud.[Técnico] != 'Virtual_TQW_Sergio Acevedo P'
            AND cloud.[Técnico] != 'Virtual TQW Negocios'
        GROUP BY cloud.[Técnico], ISNULL(dotacion.supervisor, 'Sin supervisor')
        HAVING SUM(CASE WHEN cloud.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0
        ORDER BY 
            COUNT(*) DESC,
            cloud.[Técnico]
        """
        
        logger.info("Obteniendo metadatos...")
        with engine.connect() as connection:
            # Obtener metadatos con manejo de errores
            try:
                logger.info("Ejecutando consulta de metadatos...")
                logger.info(f"Query de metadatos: {metadata_query}")
                metadata_result = connection.execute(text(metadata_query)).fetchone()
                if metadata_result:
                    ultima_carga = metadata_result.ultima_carga
                    ultima_actualizacion = metadata_result.ultima_actualizacion
                    logger.info(f"Metadatos obtenidos: ultima_carga={ultima_carga}, ultima_actualizacion={ultima_actualizacion}")
                else:
                    logger.warning("No se encontraron metadatos con la consulta. Usando valores por defecto.")
                    ultima_carga = datetime.now()
                    ultima_actualizacion = datetime.now()
            except Exception as e:
                logger.error(f"Error al obtener metadatos: {str(e)}")
                ultima_carga = datetime.now()
                ultima_actualizacion = datetime.now()
            
            # Función para formatear fechas que pueden ser strings o datetime
            def format_date(date_value):
                if not date_value:
                    return 'No disponible'
                try:
                    from datetime import datetime
                    if isinstance(date_value, str):
                        # Si es string, intentar convertir a datetime
                        try:
                            # Intentar con formato de fecha y hora
                            dt = datetime.strptime(date_value, '%Y-%m-%d %H:%M:%S')
                            return dt.strftime('%d/%m/%Y %H:%M:%S')
                        except ValueError:
                            # Si falla, intentar solo con fecha
                            try:
                                dt = datetime.strptime(date_value, '%Y-%m-%d')
                                return dt.strftime('%d/%m/%Y')
                            except ValueError:
                                return str(date_value)  # Devolver como está si no se puede formatear
                    elif hasattr(date_value, 'strftime'):
                        # Si ya es un objeto datetime
                        return date_value.strftime('%d/%m/%Y %H:%M:%S')
                    return str(date_value)
                except Exception as e:
                    logger.error(f"Error al formatear fecha {date_value}: {str(e)}")
                    return str(date_value)
            
            # Formatear fechas
            hora_descarga = format_date(ultima_carga)
            hora_actualizacion = format_date(ultima_actualizacion)
            
            logger.info(f"Hora de descarga: {hora_descarga}")
            logger.info(f"Hora de actualización: {hora_actualizacion}")
            
            logger.info("Ejecutando consulta de resumen por supervisor...")
            # Consulta para resumen por supervisor (solo supervisor y conteo de técnicos)
            supervisor_query = f"""
            SELECT  
                ISNULL(dotacion.supervisor, 'Sin supervisor') as Supervisor,
                COUNT(DISTINCT cloud.[Técnico]) as Tecnicos_Con_0_Completados
            FROM TB_TOA_30DIAS_CLOUD cloud
            LEFT JOIN tb_user_tqw dotacion ON LTRIM(RTRIM(dotacion.rut)) = LTRIM(RTRIM(cloud.[Rut o Bucket]))
            WHERE cloud.[Técnico] LIKE '%TQW%' 
                AND cloud.[Fecha] = '{fecha_actual}'
                AND cloud.[Técnico] NOT LIKE '%SME%'
                AND cloud.[Técnico] != 'Virtual_TQW_Sergio Acevedo P'
                AND cloud.[Técnico] IN (
                    SELECT cloud_sub.[Técnico]
                    FROM TB_TOA_30DIAS_CLOUD cloud_sub
                    WHERE cloud_sub.[Técnico] LIKE '%TQW%' 
                        AND cloud_sub.[Fecha] = '{fecha_actual}'
                        AND cloud_sub.[Técnico] NOT LIKE '%SME%'
                        AND cloud_sub.[Técnico] != 'Virtual_TQW_Sergio Acevedo P'
                    GROUP BY cloud_sub.[Técnico]
                    HAVING SUM(CASE WHEN cloud_sub.[Estado] = 'Completado' THEN 1 ELSE 0 END) = 0
                )
            GROUP BY ISNULL(dotacion.supervisor, 'Sin supervisor')
            ORDER BY Tecnicos_Con_0_Completados DESC, Supervisor
            """
            
            # Ejecutar consulta de resumen por supervisor
            supervisor_result = connection.execute(text(supervisor_query))
            df_supervisores = pd.DataFrame(supervisor_result.fetchall(), columns=supervisor_result.keys())
            
            logger.info("Ejecutando consulta de técnicos...")
            # Ejecutar consulta principal
            result = connection.execute(text(query))
            df = pd.DataFrame(result.fetchall(), columns=result.keys())
        
        if df.empty:
            logger.info("No hay técnicos con categoría 'Px0' para reportar")
            return
            
        logger.info(f"Encontrados {len(df)} técnicos con categoría 'Px0'")
        logger.info(f"Resumen por {len(df_supervisores)} supervisores")
        
        # Convertir DataFrames a tablas HTML con estilos personalizados
        # Tabla de resumen por supervisor
        if not df_supervisores.empty:
            supervisor_table = df_supervisores.to_html(
                index=False,
                classes='dataframe',
                border=0,
                justify='center',
                table_id='supervisorTable',
                na_rep='',
                float_format=lambda x: f"{x:,.0f}" if isinstance(x, (int, float)) else x
            )
        else:
            supervisor_table = "<p>No hay supervisores con técnicos Px0</p>"
            
        # Tabla principal de técnicos
        table_html = df.to_html(
            index=False,
            classes='dataframe',
            border=0,
            justify='center',
            table_id='tecnicoTable',
            na_rep='',
            float_format=lambda x: f"{x:,.0f}" if isinstance(x, (int, float)) else x
        )
        
        # Formatear el cuerpo del correo como HTML con CSS mejorado
        html_content = f"""
        <!DOCTYPE html>
        <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 20px;
                    }}
                    h2 {{
                        color: #2c3e50;
                        text-align: center;
                    }}
                    #tecnicoTable, #supervisorTable {{
                        width: 100%;
                        margin: 5px 0 !important;  /* Reducido el margen vertical */
                        font-size: 84%;
                        border-collapse: collapse;
                    }}
                    #tecnicoTable th, #tecnicoTable td, #supervisorTable th, #supervisorTable td {{
                        border: 1px solid #ddd;
                        padding: 2px 5px !important;  /* Reducido el padding vertical a 2px y horizontal a 5px */
                        text-align: center;
                        line-height: 1.2 !important;  /* Reducido el interlineado */
                        margin: 0 !important;  /* Elimina cualquier margen */
                    }}
                    #tecnicoTable, #supervisorTable {{
                        border-collapse: collapse;
                        border-spacing: 0;
                    }}
                    #tecnicoTable tr, #supervisorTable tr {{
                        margin: 0 !important;
                        padding: 0 !important;
                    }}
                    #tecnicoTable th {{
                        background-color: #3498db;
                        color: white;
                        position: sticky;
                        top: 0;
                    }}
                    #tecnicoTable tr:nth-child(even), #supervisorTable tr:nth-child(even) {{
                        background-color: #f8f9fa;
                    }}
                    #tecnicoTable tr:hover, #supervisorTable tr:hover {{
                        background-color: #e9ecef;
                    }}
                    .container {{
                        max-width: 95%;
                        margin: 0 auto;
                    }}
                    .footer {{
                        margin-top: 20px;
                        font-size: 90%;
                        color: #6c757d;
                        text-align: center;
                    }}
                    .metadata {{
                        background-color: #f8f9fa;
                        border-left: 4px solid #3498db;
                        padding: 10px 15px;
                        margin: 15px 0;
                        font-size: 90%;
                    }}
                    .metadata p {{
                        margin: 5px 0;
                    }}
                    h3 {{
                        color: #2c3e50;
                        margin-top: 25px;
                        margin-bottom: 10px;
                        padding-bottom: 5px;
                        border-bottom: 1px solid #e0e0e0;
                    }}
                    #supervisorTable {{
                        width: auto;
                        min-width: 300px;
                        max-width: 500px;
                        margin: 15px auto;
                        font-size: 76%;  /* Reducido de 84% a 76% */
                        border-collapse: collapse;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    }}
                    #supervisorTable th, #supervisorTable td {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }}
                    #supervisorTable th {{
                        background-color: #2ecc71;
                        color: white;
                        position: sticky;
                        top: 0;
                    }}
                    #supervisorTable tr:nth-child(even) {{
                        background-color: #f0f8f1;
                    }}
                    #supervisorTable tr:hover {{
                        background-color: #d4edda;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <h2>Reporte de Técnicos con Categoría Px0</h2>
                    <div class="metadata">
                        <p><strong>Hora de descarga del reporte:</strong> {hora_descarga}</p>
                        <p><strong>Hora de actualización de datos:</strong> {hora_actualizacion}</p>
                        <p>Total de técnicos Px0: {len(df)}</p>
                        <p>Total de supervisores con técnicos Px0: {len(df_supervisores)}</p>
                    </div>
                    <h3>Resumen por Supervisor</h3>
                    <div style="text-align: center; margin: 15px 0;">
                        <p style="margin-bottom: 10px;">Supervisores con técnicos que no han completado órdenes:</p>
                        {supervisor_table}
                    </div>
                    
                    <h3>Detalle por Técnico</h3>
                    <p>Técnicos que no han completado ninguna orden al día de hoy:</p>
                    {table_html}
                    <div class="footer">

                        <p>Este es un correo automático, por favor no responder.</p>
                    </div>
                </div>
            </body>
        </html>
        """
        
        # Importar la función de envío de correo mejorada
        try:
            from email_utils_v2 import enviar_correo
            logger.info("Usando email_utils_v2 (versión mejorada)")
        except ImportError:
            from email_utils import enviar_correo
            logger.info("Usando email_utils original")
        
        # Mostrar los primeros registros para depuración
        logger.info(f"Datos a enviar (primeros 3 registros):\n{df.head(3).to_string()}")
        
        logger.info("Preparando envío de correo...")
        # Lista de destinatarios
        destinatarios = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'  # Mantener el correo original como copia
        ]
        
        logger.info(f"Destinatarios: {', '.join(destinatarios)}")
        logger.info(f"Tamaño del contenido HTML: {len(html_content)} caracteres")
        
        # Enviar el correo a todos los destinatarios
        resultado = enviar_correo(
            destinatario=destinatarios,  # Pasar la lista directamente, no como string
            asunto=f"Reporte de Técnicos Px0 - {datetime.now().strftime('%d/%m/%Y')}",
            cuerpo=html_content,
            html=True,
            debug=True  # Activar modo debug si está disponible
        )
        
        if resultado['exito']:
            logger.info("[OK] Reporte enviado por correo exitosamente")
        else:
            logger.error(f"[ERROR] Error al enviar el correo: {resultado['mensaje']}")
            
    except Exception as e:
        logger.error(f"[ERROR] Error al generar/enviar el reporte: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    print("="*60)
    print("[CORREO] PRUEBA DE ENVIO DE REPORTE POR CORREO")
    print("="*60)
    send_report()
    print("="*60)
    print("Prueba completada. Revise el <NAME_EMAIL>")
    print("="*60)
