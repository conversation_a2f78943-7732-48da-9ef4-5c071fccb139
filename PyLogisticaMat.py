import pandas as pd
import datetime as date
import requests
import json
from datetime import datetime, timedelta
import mysql.connector
import pyodbc
import time

try:
    # Obtener datos del CSV
    url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRPxNYCkn5PSa_O8fSotbTlsXiRwBMfMogebdSN7IlVYO2f-0MiGMGd6qDrbJequYy85OBca_UeBvo9/pub?gid=0&single=true&output=csv'
    df = pd.read_csv(url)
    
    # Agregar columna de fecha_actualizacion con la fecha y hora actual
    fecha_actualizacion = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    df['fecha_actualizacion'] = fecha_actualizacion
    
    print(f"Datos obtenidos del CSV. Fecha de actualización: {fecha_actualizacion}")
    
    # Conexión a MySQL
    mysql_conn = mysql.connector.connect(
        host='**************',
        user='ncornejo',
        password='**********',
        database='operaciones_tqw'
    )
    mysql_cursor = mysql_conn.cursor()
    
    # Verificar si la columna fecha_actualizacion existe en MySQL, si no, crearla
    mysql_cursor.execute("SHOW COLUMNS FROM tp_logistica_mat_oracle LIKE 'fecha_actualizacion'")
    column_exists = mysql_cursor.fetchone()
    if not column_exists:
        mysql_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD COLUMN fecha_actualizacion DATETIME")
        mysql_conn.commit()
        print("Columna fecha_actualizacion agregada a la tabla MySQL")
    
    # Verificar si la columna 'Flag contrato' existe en MySQL, si no, crearla
    mysql_cursor.execute("SHOW COLUMNS FROM tp_logistica_mat_oracle LIKE 'Flag contrato'")
    column_exists = mysql_cursor.fetchone()
    if not column_exists:
        mysql_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD COLUMN `Flag contrato` VARCHAR(255)")
        mysql_conn.commit()
        print("Columna 'Flag contrato' agregada a la tabla MySQL")
    
    # Verificar si la columna 'cruce' existe en MySQL, si no, crearla
    mysql_cursor.execute("SHOW COLUMNS FROM tp_logistica_mat_oracle LIKE 'cruce'")
    column_exists = mysql_cursor.fetchone()
    if not column_exists:
        mysql_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD COLUMN `cruce` VARCHAR(255)")
        mysql_conn.commit()
        print("Columna 'cruce' agregada a la tabla MySQL")
    
    # Truncar tabla en MySQL antes de insertar
    mysql_cursor.execute("TRUNCATE TABLE tp_logistica_mat_oracle")
    
    # Conexión a SQL Server
    sql_server_conn = pyodbc.connect(
        'DRIVER={ODBC Driver 17 for SQL Server};SERVER=20.20.20.207;DATABASE=telqway;UID=ncornejo;PWD=**********'
    )
    sql_server_cursor = sql_server_conn.cursor()
    
    # Verificar si la columna fecha_actualizacion existe en SQL Server, si no, crearla
    try:
        sql_server_cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tp_logistica_mat_oracle' AND COLUMN_NAME = 'fecha_actualizacion'")
        column_exists = sql_server_cursor.fetchone()
        if not column_exists:
            sql_server_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD fecha_actualizacion DATETIME")
            sql_server_conn.commit()
            print("Columna fecha_actualizacion agregada a la tabla SQL Server")
    except pyodbc.Error as e:
        print(f"Error al verificar/crear columna en SQL Server: {str(e)}")
    
    # Verificar si la columna 'Flag contrato' existe en SQL Server, si no, crearla
    try:
        sql_server_cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tp_logistica_mat_oracle' AND COLUMN_NAME = 'Flag contrato'")
        column_exists = sql_server_cursor.fetchone()
        if not column_exists:
            sql_server_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD [Flag contrato] VARCHAR(255)")
            sql_server_conn.commit()
            print("Columna 'Flag contrato' agregada a la tabla SQL Server")
    except pyodbc.Error as e:
        print(f"Error al verificar/crear columna 'Flag contrato' en SQL Server: {str(e)}")
    
    # Verificar si la columna 'cruce' existe en SQL Server, si no, crearla
    try:
        sql_server_cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tp_logistica_mat_oracle' AND COLUMN_NAME = 'cruce'")
        column_exists = sql_server_cursor.fetchone()
        if not column_exists:
            sql_server_cursor.execute("ALTER TABLE tp_logistica_mat_oracle ADD [cruce] VARCHAR(255)")
            sql_server_conn.commit()
            print("Columna 'cruce' agregada a la tabla SQL Server")
    except pyodbc.Error as e:
        print(f"Error al verificar/crear columna 'cruce' en SQL Server: {str(e)}")
    
    # Truncar tabla en SQL Server antes de insertar
    sql_server_cursor.execute("TRUNCATE TABLE tp_logistica_mat_oracle")
    
    # Convertir DataFrame a una lista de tuplas para inserción
    # Reemplazar valores NaN con None para evitar errores
    # Convertir columnas numéricas a float donde sea necesario
    float_columns = ['Unidad por Kit', 'Valor VTR', 'Funcionalidad']
    
    # Convertir columnas float a tipo numérico y manejar valores no convertibles
    for col in float_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    data = [tuple(None if pd.isna(x) else x for x in row) for row in df.values]
    
    # Obtener nombres de columnas
    columns = list(df.columns)
    # Escapar nombres de columnas con comillas invertidas para MySQL y corchetes para SQL Server
    mysql_columns = [f'`{col}`' for col in columns]
    sqlserver_columns = [f'[{col}]' for col in columns]
    
    # Crear cadenas de columnas para las consultas
    mysql_columns_str = ", ".join(mysql_columns)
    sqlserver_columns_str = ", ".join(sqlserver_columns)
    
    # Crear placeholders
    placeholders = ", ".join(["%s"] * len(columns))  # Para MySQL
    placeholders_sql_server = ", ".join(["?"] * len(columns))  # Para SQL Server
    
    # Preparar consultas SQL
    mysql_query = f"INSERT INTO `tp_logistica_mat_oracle` ({mysql_columns_str}) VALUES ({placeholders})"
    sql_server_query = f"INSERT INTO [tp_logistica_mat_oracle] ({sqlserver_columns_str}) VALUES ({placeholders_sql_server})"
    
    # Insertar datos en lotes
    batch_size = 100
    total_rows = len(data)
    
    for i in range(0, total_rows, batch_size):
        batch = data[i:min(i+batch_size, total_rows)]
        
        # Insertar lote en MySQL
        mysql_cursor.executemany(mysql_query, batch)
        
        # Insertar lote en SQL Server
        sql_server_cursor.executemany(sql_server_query, batch)
        
        # Hacer commit después de cada lote
        mysql_conn.commit()
        sql_server_conn.commit()
        
        # Mostrar progreso
        print(f"Progreso: {min(i+batch_size, total_rows)}/{total_rows} registros procesados")

except requests.exceptions.RequestException as e:
    print(f"Error al obtener datos del CSV: {str(e)}")
except mysql.connector.Error as e:
    print(f"Error en MySQL: {str(e)}")
    # Imprimir muestra de datos problemáticos si es posible
    if 'data' in locals() and len(data) > 0:
        print(f"Muestra de datos problemáticos (MySQL): {data[0] if len(data) > 0 else 'No hay datos'}")
except pyodbc.Error as e:
    print(f"Error en SQL Server: {str(e)}")
    # Imprimir muestra de datos problemáticos si es posible
    if 'data' in locals() and len(data) > 0:
        print(f"Muestra de datos problemáticos (SQL Server): {data[0] if len(data) > 0 else 'No hay datos'}")
except Exception as e:
    print(f"Error inesperado: {str(e)}")
finally:
    # Cerramos explícitamente cursores y conexiones
    try:
        if 'mysql_cursor' in locals() and mysql_cursor:
            mysql_cursor.close()
        if 'mysql_conn' in locals() and mysql_conn:
            mysql_conn.close()
        if 'sql_server_cursor' in locals() and sql_server_cursor:
            sql_server_cursor.close()
        if 'sql_server_conn' in locals() and sql_server_conn:
            sql_server_conn.close()
        print("Conexiones cerradas correctamente")
    except Exception as e:
        print(f"Error al cerrar conexiones: {str(e)}")
