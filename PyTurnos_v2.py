#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para obtener datos de turnos desde Google Sheets y cargarlos en MySQL y SQL Server.
Incluye validación de datos, estandarización de fechas y manejo robusto de tipos de datos.
"""

import pandas as pd
import numpy as np
import requests
import mysql.connector
import pyodbc
from datetime import datetime, timedelta
import logging
import sys
from typing import Dict, List, Optional, Tuple, Any, Union

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('PyTurnos.log'),
        logging.StreamHandler()
    ]
)

# Configuración de bases de datos
MYSQL_CONFIG = {
    'host': "**************",
    'user': "ncornejo",
    'password': "N1c0l7as17",
    'database': "operaciones_tqw"
}

SQLSERVER_CONFIG = {
    'driver': "{ODBC Driver 17 for SQL Server}",
    'server': "************",
    'database': "telqway",
    'uid': "ncornejo",
    'pwd': "N1c0l7as17"
}

# URL del Google Sheets con los datos de turnos
TURNOS_URL = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vRyHAgqQ3Rh19AvtOm621ddIUNMXXF8dRjjLKXqu_7rrZ39bYnXOmWNqbUNveqPLkuK19_qQ2H9tBgY/pub?gid=1062603265&single=true&output=csv'


def fetch_data(url: str) -> pd.DataFrame:
    """
    Descarga datos CSV desde una URL.
    
    Args:
        url: URL del archivo CSV.
        
    Returns:
        DataFrame con los datos descargados.
        
    Raises:
        requests.exceptions.RequestException: Si hay un error al obtener los datos.
    """
    logging.info(f"Descargando datos CSV desde {url}...")
    try:
        df = pd.read_csv(url, low_memory=False)
        logging.info(f"Datos descargados correctamente. {len(df)} filas obtenidas.")
        logging.info(f"Columnas encontradas: {list(df.columns)}")
        return df
    except requests.exceptions.RequestException as e:
        logging.error(f"Error al obtener datos del CSV: {str(e)}")
        raise


def standardize_date_format(df: pd.DataFrame, date_column: str = 'FECHA') -> pd.DataFrame:
    """
    Estandariza el formato de fechas en el DataFrame.
    
    Args:
        df: DataFrame con los datos.
        date_column: Nombre de la columna de fechas.
        
    Returns:
        DataFrame con fechas estandarizadas.
    """
    if date_column not in df.columns:
        logging.warning(f"Columna {date_column} no encontrada en el DataFrame")
        return df
    
    logging.info(f"Estandarizando formato de fechas en columna: {date_column}")
    
    # Función para convertir fechas con múltiples formatos
    def parse_date(date_str: Any) -> Optional[str]:
        if pd.isna(date_str) or date_str is None:
            return None
            
        date_str = str(date_str).strip()
        
        # Lista de formatos posibles
        formats = [
            '%m/%d/%Y',    # 9/6/2025
            '%d/%m/%Y',    # 25/11/2024
            '%Y-%m-%d',    # 2025-06-09
            '%d-%m-%Y',    # 09-06-2025
            '%m-%d-%Y',    # 06-09-2025
        ]
        
        for fmt in formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                # Convertir a formato estándar MM/dd/yyyy
                return parsed_date.strftime('%m/%d/%Y')
            except ValueError:
                continue
        
        logging.warning(f"No se pudo parsear la fecha: {date_str}")
        return None
    
    # Aplicar la función de conversión
    original_count = len(df)
    df[date_column] = df[date_column].apply(parse_date)
    
    # Contar fechas válidas vs inválidas
    valid_dates = df[date_column].notna().sum()
    invalid_dates = original_count - valid_dates
    
    logging.info(f"Fechas procesadas: {valid_dates} válidas, {invalid_dates} inválidas")
    
    if invalid_dates > 0:
        logging.warning("Registros con fechas inválidas:")
        invalid_samples = df[df[date_column].isna()].head()
        for idx, row in invalid_samples.iterrows():
            logging.warning(f"  Fila {idx}: {row.get('RUT', 'N/A')} - {row.get('NOMBRE', 'N/A')}")
    
    return df


def validate_data_quality(df: pd.DataFrame) -> List[str]:
    """
    Valida la calidad de los datos.
    
    Args:
        df: DataFrame con los datos.
        
    Returns:
        Lista de problemas de calidad detectados.
    """
    logging.info("Validando calidad de datos...")
    
    issues = []
    
    # Verificar columnas críticas
    critical_columns = ['FECHA', 'RUT', 'NOMBRE', 'ZONA']
    for col in critical_columns:
        if col in df.columns:
            null_count = df[col].isna().sum()
            if null_count > 0:
                issues.append(f"Columna {col}: {null_count} valores nulos")
    
    # Verificar rango de fechas
    if 'FECHA' in df.columns:
        valid_dates = pd.to_datetime(df['FECHA'], format='%m/%d/%Y', errors='coerce')
        min_date = valid_dates.min()
        max_date = valid_dates.max()
        
        if min_date and max_date:
            logging.info(f"Rango de fechas: {min_date.date()} a {max_date.date()}")
            
            # Verificar si hay fechas muy antiguas o muy futuras
            today = datetime.now()
            if min_date < today - timedelta(days=365):
                issues.append(f"Hay fechas muy antiguas (anterior a {(today - timedelta(days=365)).date()})")
            if max_date > today + timedelta(days=365):
                issues.append(f"Hay fechas muy futuras (posterior a {(today + timedelta(days=365)).date()})")
    
    if issues:
        logging.warning("Problemas de calidad detectados:")
        for issue in issues:
            logging.warning(f"  - {issue}")
    else:
        logging.info("Validación de calidad: OK")
    
    return issues


def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Preprocesa los datos para su inserción en las bases de datos.
    
    Args:
        df: DataFrame con los datos originales.
        
    Returns:
        DataFrame preprocesado.
    """
    logging.info("Preprocesando datos...")
    
    # Estandarizar formato de fechas
    df = standardize_date_format(df, 'FECHA')
    
    # Validar calidad de datos
    validate_data_quality(df)
    
    # Reemplazar NaN con None para que SQL los maneje como NULL
    df = df.replace({np.nan: None})
    
    # Filtrar solo registros con fechas válidas
    original_count = len(df)
    df = df[df['FECHA'].notna()]
    filtered_count = len(df)
    
    if original_count != filtered_count:
        logging.warning(f"Se filtraron {original_count - filtered_count} registros con fechas inválidas")
    
    logging.info(f"Registros a procesar: {filtered_count}")
    
    return df


def create_column_mapping(df_columns: List[str], table_columns: List[str]) -> Dict[str, str]:
    """
    Crea un mapeo entre columnas del DataFrame y columnas de la tabla en la base de datos.
    
    Args:
        df_columns: Lista de columnas del DataFrame.
        table_columns: Lista de columnas de la tabla en la base de datos.
        
    Returns:
        Diccionario con el mapeo de columnas.
    """
    column_mapping = {}
    for db_col in table_columns:
        for csv_col in df_columns:
            # Comprobamos equivalencia ignorando mayúsculas/minúsculas y espacios por guiones bajos
            if (csv_col.lower().replace(' ', '_') == db_col.lower() or 
                csv_col.lower() == db_col.lower()):
                column_mapping[csv_col] = db_col
    
    return column_mapping


def prepare_dataframe_for_db(df: pd.DataFrame, column_mapping: Dict[str, str]) -> pd.DataFrame:
    """
    Prepara un DataFrame para inserción en una base de datos específica.
    
    Args:
        df: DataFrame original.
        column_mapping: Mapeo de columnas CSV a columnas DB.
        
    Returns:
        DataFrame preparado para la base de datos.
    """
    # Verificar si hay mapeo
    if not column_mapping:
        raise ValueError("No hay columnas coincidentes para insertar en la base de datos")
    
    # Renombrar las columnas en el DataFrame según el mapeo y seleccionar solo las que están en la tabla
    df_db = df[list(column_mapping.keys())].copy()
    df_db.columns = [column_mapping[col] for col in df_db.columns]
    
    return df_db


def convert_value_for_sqlserver(value: Any, column: str, sql_type: str) -> Any:
    """
    Convierte un valor al tipo adecuado para SQL Server.
    
    Args:
        value: Valor a convertir.
        column: Nombre de la columna.
        sql_type: Tipo SQL de la columna.
        
    Returns:
        Valor convertido.
    """
    # Manejar valores nulos o vacíos de forma más robusta
    if pd.isna(value) or value is None or value == '' or (isinstance(value, str) and value.strip() == ''):
        return None
    
    # Convertir explícitamente según el tipo de destino
    try:
        if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
            # Para columnas numéricas de punto flotante
            if isinstance(value, str):
                # Eliminar caracteres no numéricos excepto el punto decimal y signo negativo
                value = ''.join(c for c in value if c.isdigit() or c == '.' or c == '-')
                if not value or value == '-' or value == '.':
                    return None
            # Asegurarse de que el valor sea float
            return float(value)
        elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint'):
            # Para columnas numéricas enteras
            # Si es float, convertir a int
            if isinstance(value, float):
                return int(value)
            
            if isinstance(value, str):
                # Eliminar caracteres no numéricos excepto signo negativo
                value = ''.join(c for c in value if c.isdigit() or c == '-')
                if not value or value == '-':
                    return None
            # Asegurarse de que el valor sea int
            return int(value)
        else:
            # Para otros tipos, devolver el valor tal cual
            return value
    except (ValueError, TypeError):
        logging.warning(f"No se pudo convertir el valor '{value}' (tipo: {type(value).__name__}) para la columna {column} al tipo {sql_type}")
        return None


def prepare_sqlserver_dataframe(df: pd.DataFrame, df_sql: pd.DataFrame, column_types: Dict[str, str]) -> pd.DataFrame:
    """
    Prepara un DataFrame para inserción en SQL Server, con conversiones de tipo.
    
    Args:
        df: DataFrame original.
        df_sql: DataFrame preparado para SQL Server.
        column_types: Diccionario de tipos de columnas SQL Server.
        
    Returns:
        DataFrame preparado con conversiones de tipo.
    """
    # Aplicar conversiones a cada columna según su tipo en SQL Server
    for column in df_sql.columns:
        if column in column_types:
            sql_type = column_types[column]
            logging.info(f"Convirtiendo columna {column} al tipo {sql_type}")
            df_sql[column] = df_sql[column].apply(lambda x: convert_value_for_sqlserver(x, column, sql_type))
    
    # Verificación adicional para columnas numéricas
    logging.info("Realizando verificación adicional de tipos para columnas numéricas...")
    for column in df_sql.columns:
        if column in column_types:
            sql_type = column_types[column]
            try:
                if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
                    # Verificar que valores para columnas float sean realmente float o None
                    df_sql[column] = df_sql[column].apply(lambda x: float(x) if x is not None else None)
                    logging.info(f"Verificación adicional de tipo float para columna {column} completada")
                elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint'):
                    # Verificar que valores para columnas int sean realmente int o None
                    df_sql[column] = df_sql[column].apply(lambda x: int(x) if x is not None else None)
                    logging.info(f"Verificación adicional de tipo int para columna {column} completada")
            except Exception as e:
                logging.error(f"Error en verificación adicional para columna {column}: {str(e)}")
                # Mostrar valores problemáticos
                problematic = df_sql[~df_sql[column].isna()].head(5)
                logging.error(f"Ejemplos de valores en {column}: {problematic[column].tolist() if not problematic.empty else 'Ninguno'}")
                logging.error(f"Tipos de datos: {[type(x).__name__ for x in problematic[column].tolist()] if not problematic.empty else 'Ninguno'}")
                
                # Intentar corregir los valores problemáticos
                logging.info(f"Intentando corregir valores para columna {column}...")
                if sql_type.lower() in ('float', 'real', 'decimal', 'numeric'):
                    df_sql[column] = df_sql[column].apply(
                        lambda x: float(x) if x is not None and not pd.isna(x) and isinstance(x, (int, float, str)) else None
                    )
                elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint'):
                    df_sql[column] = df_sql[column].apply(
                        lambda x: int(float(x)) if x is not None and not pd.isna(x) and isinstance(x, (int, float, str)) else None
                    )
    
    return df_sql


def insert_into_mysql(df: pd.DataFrame, mysql_config: Dict[str, str], table_name: str) -> None:
    """
    Inserta datos en una tabla MySQL.
    
    Args:
        df: DataFrame con los datos a insertar.
        mysql_config: Configuración de conexión a MySQL.
        table_name: Nombre de la tabla.
        
    Raises:
        mysql.connector.Error: Si hay errores en la conexión o inserción.
    """
    logging.info(f"Conectando a MySQL: {mysql_config['host']}...")
    mysql_conn = None
    mysql_cursor = None
    
    try:
        # Conectar a MySQL
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # Obtener columnas de la tabla MySQL
        logging.info(f"Obteniendo esquema de la tabla MySQL {table_name}...")
        mysql_cursor.execute(f"SHOW COLUMNS FROM {table_name}")
        table_columns = [row[0] for row in mysql_cursor.fetchall()]
        logging.info(f"Columnas en MySQL: {table_columns}")
        
        # Crear mapeo de columnas
        column_mapping = create_column_mapping(df.columns, table_columns)
        logging.info(f"Mapeo de columnas MySQL: {column_mapping}")
        
        if not column_mapping:
            logging.error("¡No se encontraron columnas coincidentes entre el CSV y la tabla MySQL!")
            logging.error(f"Columnas en CSV: {list(df.columns)}")
            logging.error(f"Columnas en MySQL: {table_columns}")
            raise ValueError("No hay columnas coincidentes para insertar en MySQL")
        
        # Preparar DataFrame para MySQL
        df_mysql = prepare_dataframe_for_db(df, column_mapping)
        logging.info(f"Columnas que se insertarán en MySQL: {list(df_mysql.columns)}")
        
        # Truncar tabla
        logging.info(f"Truncando tabla MySQL {table_name}...")
        mysql_cursor.execute(f"TRUNCATE TABLE {table_name}")
        mysql_conn.commit()
        
        # Preparar consulta INSERT
        cols = df_mysql.columns.tolist()
        placeholders = ", ".join(["%s"] * len(cols))
        columns = ", ".join([f"`{col}`" for col in cols])
        
        mysql_insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Insertar por lotes
        batch_size = 1000
        total_records = len(df_mysql)
        
        for i in range(0, total_records, batch_size):
            batch_end = min(i + batch_size, total_records)
            # Convertir los valores a lista de tuplas, reemplazando NaN por None
            batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df_mysql.iloc[i:batch_end].values]
            mysql_cursor.executemany(mysql_insert_query, batch_data)
            mysql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records} registros en MySQL...")
        
        logging.info("Datos insertados correctamente en MySQL.")
    except mysql.connector.Error as e:
        logging.error(f"Error en MySQL: {str(e)}")
        raise
    finally:
        # Cerrar conexiones
        if mysql_cursor:
            mysql_cursor.close()
        if mysql_conn:
            mysql_conn.close()


def insert_into_sqlserver(df: pd.DataFrame, sqlserver_config: Dict[str, str], table_name: str, sp_name: Optional[str] = None) -> None:
    """
    Inserta datos en una tabla SQL Server y opcionalmente ejecuta un stored procedure.
    
    Args:
        df: DataFrame con los datos a insertar.
        sqlserver_config: Configuración de conexión a SQL Server.
        table_name: Nombre de la tabla.
        sp_name: Nombre del stored procedure a ejecutar después de la inserción.
        
    Raises:
        pyodbc.Error: Si hay errores en la conexión o inserción.
    """
    logging.info(f"Conectando a SQL Server: {sqlserver_config['server']}...")
    mssql_conn = None
    mssql_cursor = None
    
    try:
        # Conectar a SQL Server
        connection_string = (
            f"DRIVER={sqlserver_config['driver']};"
            f"SERVER={sqlserver_config['server']};"
            f"DATABASE={sqlserver_config['database']};"
            f"UID={sqlserver_config['uid']};"
            f"PWD={sqlserver_config['pwd']}"
        )
        mssql_conn = pyodbc.connect(connection_string)
        mssql_cursor = mssql_conn.cursor()
        
        # Truncar tabla
        logging.info(f"Truncando tabla SQL Server {table_name}...")
        mssql_cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL TRUNCATE TABLE {table_name}")
        mssql_conn.commit()
        
        # Obtener columnas de la tabla SQL Server
        logging.info(f"Obteniendo esquema de la tabla SQL Server {table_name}...")
        mssql_cursor.execute(f"SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}'")
        sql_table_columns = [row[0] for row in mssql_cursor.fetchall()]
        logging.info(f"Columnas en SQL Server: {sql_table_columns}")
        
        # Crear mapeo de columnas
        sql_column_mapping = create_column_mapping(df.columns, sql_table_columns)
        logging.info(f"Mapeo de columnas SQL Server: {sql_column_mapping}")
        
        if not sql_column_mapping:
            logging.error("¡No se encontraron columnas coincidentes entre el CSV y la tabla SQL Server!")
            logging.error(f"Columnas en CSV: {list(df.columns)}")
            logging.error(f"Columnas en SQL Server: {sql_table_columns}")
            raise ValueError("No hay columnas coincidentes para insertar en SQL Server")
        
        # Preparar DataFrame para SQL Server
        df_sql = prepare_dataframe_for_db(df, sql_column_mapping)
        logging.info(f"Columnas que se insertarán en SQL Server: {list(df_sql.columns)}")
        
        # Obtener tipos de datos de las columnas en SQL Server
        logging.info("Obteniendo tipos de datos de las columnas de SQL Server...")
        mssql_cursor.execute(f"""
            SELECT COLUMN_NAME, DATA_TYPE 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
        """)
        column_types = {row[0]: row[1] for row in mssql_cursor.fetchall()}
        logging.info(f"Tipos de datos en SQL Server: {column_types}")
        
        # Preparar DataFrame para SQL Server con conversiones de tipo
        df_sql = prepare_sqlserver_dataframe(df, df_sql, column_types)
        
        # Preparar consulta SQL Server
        cols = df_sql.columns.tolist()
        placeholders = ", ".join(["?"] * len(cols))
        columns = ", ".join([f"[{col}]" for col in cols])
        
        mssql_insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Insertar por lotes
        batch_size = 1000
        total_records_sql = len(df_sql)
        
        for i in range(0, total_records_sql, batch_size):
            batch_end = min(i + batch_size, total_records_sql)
            # Convertir los valores a lista de tuplas con verificación de tipos adicional
            batch_data = []
            for idx, row in df_sql.iloc[i:batch_end].iterrows():
                # Verificar cada valor y ajustar según el tipo de columna
                converted_row = []
                for j, (col, val) in enumerate(zip(cols, row)):
                    if pd.isna(val) or val is None:
                        converted_row.append(None)
                    elif col in column_types:
                        sql_type = column_types[col]
                        # Verificación final de tipo
                        try:
                            if sql_type.lower() in ('float', 'real', 'decimal', 'numeric') and not isinstance(val, float):
                                converted_row.append(float(val))
                            elif sql_type.lower() in ('int', 'bigint', 'smallint', 'tinyint') and not isinstance(val, int):
                                converted_row.append(int(val))
                            else:
                                converted_row.append(val)
                        except (ValueError, TypeError):
                            logging.warning(f"Conversión final fallida para {col} en fila {idx}. Estableciendo a NULL.")
                            converted_row.append(None)
                    else:
                        converted_row.append(val)
                batch_data.append(tuple(converted_row))
            
            mssql_cursor.executemany(mssql_insert_query, batch_data)
            mssql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records_sql} registros en SQL Server...")
        
        logging.info("Datos insertados correctamente en SQL Server.")
        
        # Ejecutar stored procedure si se especificó
        if sp_name:
            logging.info(f"Ejecutando stored procedure {sp_name}...")
            mssql_cursor.execute(f"exec {sp_name}")
            mssql_conn.commit()
            logging.info("Stored procedure ejecutado correctamente.")
    
    except pyodbc.Error as e:
        error_str = str(e)
        logging.error(f"Error al insertar en SQL Server: {error_str}")
        
        # Diagnóstico específico para error de conversión de tipos
        if "Error converting data type nvarchar to float" in error_str:
            logging.error("Error de conversión de tipos detectado. Analizando columnas numéricas...")
            numeric_columns = [col for col, type_name in column_types.items() 
                              if type_name.lower() in ('float', 'real', 'decimal', 'numeric', 'int', 'bigint', 'smallint', 'tinyint')]
            
            if numeric_columns:
                logging.error(f"Columnas numéricas encontradas: {numeric_columns}")
                # Examinar cada columna numérica para identificar valores problemáticos
                for col in numeric_columns:
                    if col in df_sql.columns:
                        try:
                            non_numeric_values = df_sql[~df_sql[col].isna()].apply(
                                lambda row: row[col] if not isinstance(row[col], (int, float)) and row[col] is not None else None, 
                                axis=1
                            )
                            non_numeric_values = non_numeric_values[non_numeric_values.notna()]
                            if not non_numeric_values.empty:
                                logging.error(f"Columna '{col}' tiene valores no numéricos: {non_numeric_values.head(5).tolist()}")
                                logging.error(f"Tipos de datos de estos valores: {[type(x).__name__ for x in non_numeric_values.head(5).tolist()]}")
                        except Exception as col_ex:
                            logging.error(f"Error al analizar columna {col}: {str(col_ex)}")
            else:
                logging.error("No se encontraron columnas numéricas en la información de tipos.")
        
        # Mostrar una muestra de los datos para diagnóstico
        logging.error("Muestra de datos que causaron el error:")
        sample = df_sql.iloc[i:i+5] if i < len(df_sql) else df_sql.iloc[-5:]
        logging.error(str(sample))
        logging.error(f"Columnas en df_sql: {list(df_sql.columns)}")
        logging.error(f"Columnas en consulta SQL: {columns}")
        
        raise
    finally:
        # Cerrar conexiones
        if mssql_cursor:
            mssql_cursor.close()
        if mssql_conn:
            mssql_conn.close()


def main() -> None:
    """
    Función principal que coordina todo el proceso.
    """
    try:
        logging.info("Iniciando proceso de obtención y carga de turnos...")
        
        # Obtener datos
        df = fetch_data(TURNOS_URL)
        
        # Preprocesar datos
        df = preprocess_data(df)
        
        # Insertar en MySQL
        insert_into_mysql(df, MYSQL_CONFIG, "tb_turnos_py")
        
        # Insertar en SQL Server y ejecutar stored procedure
        insert_into_sqlserver(df, SQLSERVER_CONFIG, "tb_paso_pyTurnos", "SP_INSERT_PY_TURNO")
        
        logging.info("Proceso completado con éxito.")
        
    except Exception as e:
        logging.error(f"Error inesperado: {str(e)}")
        sys.exit(1)
    finally:
        logging.info("Proceso finalizado.")


if __name__ == "__main__":
    main()