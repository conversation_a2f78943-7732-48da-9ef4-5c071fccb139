# Sistema de Flujo de Datos - NdcBot

## Solución al problema de WebDriver Manager

### Contexto del problema

Microsoft ha cambiado la forma de distribuir msedgedriver. El dominio antiguo `msedgedriver.azureedge.net` ya no funciona, y ahora hay dos alternativas oficiales:

1. **Portal web de descarga directa**: `https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/`
2. **Almacenamiento de archivos**: `https://msedgewebdriverstorage.z22.web.core.windows.net/`

Este cambio ha afectado a todas las herramientas que dependían del antiguo dominio, incluyendo webdriver-manager y otras utilidades de automatización.

### Soluciones implementadas

Los scripts han sido actualizados para abordar este problema con múltiples soluciones:

1. **Uso del portal web oficial**: Se modificaron los scripts para utilizar el portal web oficial de Microsoft para descargar el driver.

2. **Múl<PERSON>les rutas de descarga**: El script ahora busca el driver en varias ubicaciones comunes:
   - En la misma carpeta que el script
   - En la subcarpeta `drivers/`
   - En `Downloads/` del usuario
   - En `Documents/` del usuario

3. **Cache local**: Se configura un directorio de cache local para guardar los drivers descargados, evitando descargas repetidas.

4. **Descarga manual**: Se incluye un script dedicado `descargar_msedgedriver.py` para descargar manualmente el driver cuando se tenga conexión.

5. **Herramienta de diagnóstico**: El script `probar_conectividad.py` para diagnosticar problemas de red y verificar la conectividad con los servidores oficiales.

6. **Reemplazo de URLs en webdriver-manager**: Se modifica dinámicamente el comportamiento de webdriver-manager para usar las URLs correctas.

### Cómo solucionar el problema

Si encuentras errores con la descarga automática del webdriver, sigue estos pasos:

1. **Diagnóstico**:
   - Ejecuta `probar_conectividad.py` para verificar el problema específico
   - Verifica si el problema es de red, proxy o firewall

2. **Opciones de solución**:

   **Opción 1: Descargar el driver manualmente**
   - Ejecuta `descargar_msedgedriver.py`
   - Este script descargará e instalará el driver en la carpeta correcta

   **Opción 2: Configurar proxy (si aplica)**
   - Si estás detrás de un proxy corporativo, establece las variables de entorno:
     ```
     set HTTP_PROXY=http://tu-proxy:puerto
     set HTTPS_PROXY=http://tu-proxy:puerto
     ```

   **Opción 3: Descarga directa**
   - Visita: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
   - Descarga la versión del driver que coincida con tu versión de Edge
   - Descomprime el archivo y coloca `msedgedriver.exe` en la misma carpeta que NdcBot.py

### Verificación de la versión de Edge

Para verificar tu versión de Edge:
1. Abre Microsoft Edge
2. Haz clic en los tres puntos (...) en la esquina superior derecha
3. Ve a Configuración > Acerca de Microsoft Edge
4. La versión se mostrará en la página (ej. "Versión 115.0.1901.203")

### Problemas conocidos

- Algunas redes corporativas bloquean tanto el dominio antiguo como el nuevo
- Algunas versiones de webdriver-manager tienen el dominio antiguo codificado y no pueden actualizarse
- La descarga puede fallar si estás offline o tienes problemas de conectividad

## Contacto y soporte

Si encuentras problemas adicionales, por favor contacta al equipo de soporte.