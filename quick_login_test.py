"""
Script rápido para probar el login completo en Oracle Cloud
"""

from playwright.sync_api import sync_playwright
import time

def quick_oracle_login():
    """Ejecutar login completo sin pausas"""
    
    with sync_playwright() as p:
        # Configurar navegador
        browser = p.chromium.launch(headless=False, slow_mo=1000)
        page = browser.new_page()
        
        try:
            print("🚀 Iniciando proceso de login automatizado...")
            
            # Paso 1: Navegar a Oracle Cloud
            print("1️⃣ Navegando a Oracle Cloud...")
            page.goto("https://vtr.fs.ocs.oraclecloud.com/")
            
            # Paso 2: Hacer clic en "Conectarse con SSO"
            print("2️⃣ Haciendo clic en 'Conectarse con SSO'...")
            sso_button = page.locator("button:has-text('Conectarse con SSO')")
            sso_button.click()
            
            # Paso 3: Llenar usuario
            print("3️⃣ Llenando usuario 'ncornejoh'...")
            username_input = page.locator("input[type='text'], textbox").first
            username_input.fill("ncornejoh")
            
            # Paso 4: Continuar con SSO
            print("4️⃣ Haciendo clic en 'Continuar con SSO'...")
            continue_button = page.locator("button:has-text('Continuar con SSO')")
            continue_button.click()
            
            # Esperar a que cargue la página de email
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            # Paso 5: Llenar email
            print("5️⃣ Llenando email '<EMAIL>'...")
            email_input = page.locator("input").first
            email_input.fill("<EMAIL>")
            
            # Paso 6: Hacer clic en siguiente
            print("6️⃣ Haciendo clic en 'Siguiente'...")
            next_button = page.locator("button:has-text('Siguiente'), button:has-text('Continuar'), button[type='submit']").first
            next_button.click()
            
            # Esperar a que cargue la página de contraseña
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            # Paso 7: Llenar contraseña
            print("7️⃣ Llenando contraseña...")
            password_input = page.locator("input[type='password'], input").first
            password_input.fill("Telqway.202517")
            
            # Paso 8: Iniciar sesión
            print("8️⃣ Haciendo clic en 'Iniciar sesión'...")
            login_button = page.locator("button:has-text('Iniciar sesión'), button:has-text('Iniciar'), button[type='submit']").first
            login_button.click()
            
            # Esperar resultado
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Mostrar resultado
            print(f"✅ Login completado!")
            print(f"📄 URL final: {page.url}")
            print(f"📋 Título: {page.title()}")
            
            # Tomar captura de pantalla
            page.screenshot(path="quick_login_result.png")
            print("📸 Captura guardada: quick_login_result.png")
            
            # Esperar para ver el resultado
            input("Presiona Enter para cerrar...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            page.screenshot(path="quick_login_error.png")
            print("📸 Captura de error guardada: quick_login_error.png")
            
        finally:
            browser.close()

if __name__ == "__main__":
    quick_oracle_login()