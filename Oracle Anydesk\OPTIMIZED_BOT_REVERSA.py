import time
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pandas as pd
import sqlalchemy
import os
import glob
import pyautogui
import cv2
import numpy as np
import win32gui
import win32com.client
from aux_mov import DatabaseManager
import traceback

class ImageNotFoundError(Exception):
    pass

def cerrar_ventana_edge():
    def enum_windows_callback(hwnd, results):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            print(f"Ventana encontrada - Título: '{title}', Clase: '{class_name}'")
            if "Edge" in title or "Microsoft" in title:
                results.append((hwnd, title, class_name))

    edge_windows = []
    win32gui.EnumWindows(enum_windows_callback, edge_windows)

    if edge_windows:
        print("Ventanas de Edge encontradas:")
        for hwnd, title, class_name in edge_windows:
            print(f"  Handle: {hwnd}, <PERSON>í<PERSON><PERSON>: '{title}', Clase: '{class_name}'")
        
        # Intentar cerrar la primera ventana encontrada
        edge_hwnd, _, _ = edge_windows[0]
        shell = win32com.client.Dispatch("WScript.Shell")
        win32gui.SetForegroundWindow(edge_hwnd)
        shell.SendKeys("%{F4}")
        print(f"Intento de cerrar ventana con handle {edge_hwnd}")
    else:
        print("No se encontraron ventanas que parezcan ser Edge.")

def buscar_imagen_en_pantalla(imagen_a_buscar, max_intentos=3):
    """Busca una imagen en la pantalla con reintentos"""
    intento = 0
    while intento < max_intentos:
        try:
            print(f"Buscando la imagen: {imagen_a_buscar} (intento {intento + 1}/{max_intentos})")
            
            # Verificar que el archivo existe
            if not os.path.exists(imagen_a_buscar):
                print(f"ERROR: El archivo no existe: {imagen_a_buscar}")
                return "IMAGE_NOT_FOUND"
            
            # Toma una captura de pantalla
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

            # Lee la imagen a buscar y conviértela a escala de grises
            try:
                template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)
                if template is None:
                    print(f"ERROR: No se pudo leer la imagen: {imagen_a_buscar}")
                    return "IMAGE_NOT_FOUND"
            except Exception as e:
                print(f"ERROR al leer imagen {imagen_a_buscar}: {str(e)}")
                return "IMAGE_NOT_FOUND"
                
            w, h = template.shape[::-1]

            # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
            result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
            loc = np.where(result >= 0.90)

            # Si encontró la imagen, devuelve las coordenadas del centro
            for pt in zip(*loc[::-1]):
                centro_x = pt[0] + w // 2
                centro_y = pt[1] + h // 2
                print(f"Imagen encontrada en: ({centro_x}, {centro_y})")
                return (centro_x, centro_y)

            # Si no encontró la imagen, verifica si es una de las excepciones
            if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\D32Vina_reversa2.png",
                                   "C:\\Users\\<USER>\\Desktop\\D32Vina_reversa.png",
                                   "C:\\Users\\<USER>\\Desktop\\ContinueEnd.png"]:            
                print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
                return None
            
            print(f"Imagen no encontrada: {imagen_a_buscar}")
            intento += 1
            if intento < max_intentos:
                time.sleep(2)  # Esperar un poco antes de reintentar
            
        except Exception as e:
            print(f"ERROR durante la búsqueda de la imagen: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            intento += 1
            if intento < max_intentos:
                time.sleep(2)
    
    # Si llegamos aquí, no se encontró la imagen después de todos los intentos
    return "IMAGE_NOT_FOUND"

def procesar_bodega(nombre_bodega, imagenes_config, reintentar=True):
    """Procesa los pasos para una bodega específica con opción de reintento"""
    print(f"\n{'='*20} Procesando bodega: {nombre_bodega} {'='*20}")
    
    intentos = 2 if reintentar else 1
    for intento in range(intentos):
        try:
            if intento > 0:
                print(f"Reintentando procesamiento de {nombre_bodega} (intento {intento + 1})")
            
            # Click inicial en sombrero (agregando 4 segundos)
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(7)  # 3 + 4 segundos
            
            # Seleccionar bodega
            coordenadas = buscar_imagen_en_pantalla(imagenes_config['bodega'])
            if coordenadas == "IMAGE_NOT_FOUND":
                print(f"ERROR: No se pudo encontrar la imagen de bodega: {imagenes_config['bodega']}")
                continue
            if coordenadas:
                pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            time.sleep(7)  # 3 + 4 segundos
            
            # Navegar por consulta de transacción
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
            if coordenadas:
                pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            time.sleep(7)  # 3 + 4 segundos
            
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
            if coordenadas:
                pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            time.sleep(7)  # 3 + 4 segundos
            
            # Manejo especial para Concepción y Temuco (necesitan scroll)
            if nombre_bodega in ['Concepción', 'Temuco']:
                pyautogui.scroll(-1200)
                pyautogui.scroll(-1200)
                pyautogui.scroll(-1200)
                time.sleep(7)  # 3 + 4 segundos
            
            # Seleccionar transacción específica
            if 'transaccion1' in imagenes_config:
                coordenadas = buscar_imagen_en_pantalla(imagenes_config['transaccion1'])
                if coordenadas:
                    pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            
            time.sleep(7)  # 3 + 4 segundos
            
            if 'transaccion2' in imagenes_config:
                coordenadas = buscar_imagen_en_pantalla(imagenes_config['transaccion2'])
                if coordenadas:
                    pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            
            time.sleep(imagenes_config.get('wait_after_transaction', 5) + 4)
            
            # Buscar
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(imagenes_config.get('wait_after_find', 14) + 4)
            
            # Exportar
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(imagenes_config.get('wait_after_file', 2) + 4)
            
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(imagenes_config.get('wait_after_export', 6) + 4)
            
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(imagenes_config.get('wait_after_continue', 50) + 4)
            
            return True
            
        except Exception as e:
            print(f"Error procesando bodega {nombre_bodega}: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            if intento < intentos - 1:
                print(f"Esperando 10 segundos antes de reintentar...")
                time.sleep(10)
        finally:
            # Cerrar ventana actual
            coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
            if coordenadas:
                pyautogui.click(coordenadas[0], coordenadas[1])
            time.sleep(7)  # 3 + 4 segundos
    
    return False

def leer_archivo_exportado(engine, custom_dtypes):
    """Lee el archivo CSV más reciente exportado y lo carga a la base de datos"""
    try:
        list_of_files = glob.glob('C:/Users/<USER>/Downloads/*')
        if not list_of_files:
            print("ERROR: No se encontraron archivos en Downloads")
            return 0
            
        latest_file = max(list_of_files, key=os.path.getctime)
        
        print(f"Procesando archivo: {latest_file}")
        
        df = pd.read_csv(latest_file, sep='\t')
        df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False, dtype=custom_dtypes)
        print(f"Total de registros en el DataFrame: {len(df)}")
        
        return len(df)
    except Exception as e:
        print(f"Error leyendo archivo exportado: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return 0

def main():
    try:
        # Configuración de conexión a base de datos
        engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server')
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Truncar tabla inicial
        session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_REVERSAX"))
        session.commit()
        
        # Definir tipos de datos personalizados
        custom_dtypes = {
            'Serial': sqlalchemy.types.String(50),
            'Item': sqlalchemy.types.String(50),
            'Org': sqlalchemy.types.String(50),
            'Revision': sqlalchemy.types.String(50),
            'Subinventory': sqlalchemy.types.String(50),
            'Locator': sqlalchemy.types.String(50),
            'Operation': sqlalchemy.types.String(50),
            'Job': sqlalchemy.types.String(50),
            'Step': sqlalchemy.types.String(50),
            'Lot': sqlalchemy.types.String(50),
            'State': sqlalchemy.types.String(50),
            'Status': sqlalchemy.types.String(50),
            'Receipt Date': sqlalchemy.types.String(50),
            'Ship Date': sqlalchemy.types.String(50),
            'Supplier Name': sqlalchemy.types.String(50),
            'Supplier Lot': sqlalchemy.types.String(50),
            'Supplier Serial': sqlalchemy.types.String(50),
            'Unit Number': sqlalchemy.types.String(50),
            'Attributes': sqlalchemy.types.String(50),
            '[  ]': sqlalchemy.types.String(50),
            'Unnamed: 20': sqlalchemy.types.String(50)
        }
        
        # Configuración de bodegas (tiempos ya incluyen los 4 segundos adicionales)
        bodegas_config = {
            'Santiago': {
                'bodega': 'C:\\Users\\<USER>\\Desktop\\STGO_reversa.png',
                'transaccion1': 'C:\\Users\\<USER>\\Desktop\\G41Stgo_reversa.png',
                'wait_after_find': 14,
                'wait_after_continue': 80
            },
            'Viña': {
                'bodega': 'C:\\Users\\<USER>\\Desktop\\VinaReversa.png',
                'transaccion1': 'C:\\Users\\<USER>\\Desktop\\D32Vina_reversa.png',
                'transaccion2': 'C:\\Users\\<USER>\\Desktop\\D32Vina_reversa2.png',
                'wait_after_find': 60,
                'wait_after_continue': 120
            },
            'Concepción': {
                'bodega': 'C:\\Users\\<USER>\\Desktop\\Conce.png',
                'transaccion1': 'C:\\Users\\<USER>\\Desktop\\J64Conce_reversa.png',
                'wait_after_find': 12,
                'wait_after_continue': 50
            },
            'Antofagasta': {
                'bodega': 'C:\\Users\\<USER>\\Desktop\\AntoDirecta.png',
                'transaccion1': 'C:\\Users\\<USER>\\Desktop\\A91Anto_reversa.png',
                'wait_after_find': 12,
                'wait_after_continue': 60
            },
            'Temuco': {
                'bodega': 'C:\\Users\\<USER>\\Desktop\\TemucoDirecta.png',
                'transaccion1': 'C:\\Users\\<USER>\\Desktop\\J68_TemucoReversa.png',
                'wait_after_find': 12,
                'wait_after_continue': 60
            }
        }
        
        # Click inicial en sombrero (con 4 segundos adicionales)
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])
        time.sleep(8)  # 4 + 4 segundos
        
        # Procesar cada bodega
        bodegas_procesadas = 0
        bodegas_exitosas = 0
        
        for nombre_bodega, config in bodegas_config.items():
            bodegas_procesadas += 1
            if procesar_bodega(nombre_bodega, config, reintentar=True):
                registros = leer_archivo_exportado(engine, custom_dtypes)
                if registros > 0:
                    bodegas_exitosas += 1
                time.sleep(6)  # 2 + 4 segundos
            else:
                print(f"ERROR: Falló el procesamiento de {nombre_bodega} después de reintentos")
        
        print(f"\nResumen: {bodegas_exitosas}/{bodegas_procesadas} bodegas procesadas exitosamente")
        
        # Solo continuar si al menos una bodega fue exitosa
        if bodegas_exitosas > 0:
            # Ejecutar stored procedures y sincronización
            session.execute(text("EXEC SP_INSERT_ORACLE_REVERSA"))
            session.commit()
            
            # Leer y transferir datos a MySQL
            Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_REVERSAX", engine)
            
            engineMYsql_new = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@**************:3306/operaciones_tqw', echo=False)
            
            # Transferir tabla de series reversa
            series_reversa = pd.read_sql_query("SELECT * FROM tb_logis_tecnico_serie_reversa", engineMYsql_new)
            series_reversa.to_sql('tb_logis_tecnico_serie_reversa', engine, if_exists='replace', index=False)
            
            # Transferir datos principales
            Data.to_sql('tb_logist_bdreversa', engineMYsql_new, if_exists='replace', index=False)
            
            # Ejecutar actualización de logística reversa
            sql2 = text("CALL UpdateLogisticaReversa();")
            
            try:
                with engineMYsql_new.connect() as connection:
                    connection.execute(sql2)
                    print("Stored Procedure executed successfully!")
            except Exception as e:
                print(f"Error executing Stored Procedure: {str(e)}")
            finally:
                session.close()
                engineMYsql_new.dispose()
        else:
            print("ERROR: No se procesó ninguna bodega exitosamente. Saltando sincronización con MySQL.")
        
        # Cerrar Oracle
        time.sleep(7)  # 3 + 4 segundos
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreOracle.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])
        time.sleep(7)  # 3 + 4 segundos
        
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OkOracleCierre.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])
        time.sleep(7)  # 3 + 4 segundos
        
        # Cerrar Edge
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge.png')
        if coordenadas:
            pyautogui.rightClick(coordenadas[0], coordenadas[1])
        time.sleep(7)  # 3 + 4 segundos
        
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CloseWindows.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])
        
        # Llamar a cerrar_ventana_edge() solo si todo funcionó correctamente
        if bodegas_exitosas == bodegas_procesadas and bodegas_exitosas > 0:
            print("Proceso completado exitosamente. Cerrando Edge...")
            cerrar_ventana_edge()
            
    except Exception as e:
        print(f"ERROR GENERAL en main(): {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()