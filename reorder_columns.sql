-- Script para reordenar columnas: colocar "Técnico" en primera posición
-- Ejecutar este script en SQL Server Management Studio o similar

USE telqway;
GO

-- Paso 1: Crear nueva tabla con "Técnico" en primera posición
CREATE TABLE tb_toa_reporte_diario_new (
    [Técnico] NVARCHAR(MAX),
    [Orden de Trabajo] NVARCHAR(MAX),
    [Tipo de Actividad] NVARCHAR(MAX),
    [Subtipo] NVARCHAR(MAX),
    [Tipo de Orden] NVARCHAR(MAX),
    [Franja] NVARCHAR(MAX),
    [Inicio] NVARCHAR(MAX),
    [Fin] NVARCHAR(MAX),
    [Cliente] NVARCHAR(MAX),
    [Dirección] NVARCHAR(MAX),
    [Ciudad] NVARCHAR(MAX),
    [Número Cliente] NVARCHAR(MAX),
    [Celular] NVARCHAR(MAX),
    [Clase de Vivienda] NVARCHAR(MAX),
    [Código Ciudad] NVARCHAR(MAX),
    [Código Localidad] NVARCHAR(MAX),
    [Código Territorio] NVARCHAR(MAX),
    [Código Zona] NVARCHAR(MAX),
    [Comentarios de la actividad] NVARCHAR(MAX),
    [Coord X] NVARCHAR(MAX),
    [Coord Y] NVARCHAR(MAX),
    [Fecha] NVARCHAR(MAX),
    [Ventana de Entrega] NVARCHAR(MAX),
    [Descripción de la actividad] NVARCHAR(MAX),
    [Duración] NVARCHAR(MAX),
    [email] NVARCHAR(MAX),
    [Estado de la actividad] NVARCHAR(MAX),
    [Fecha Certificada] NVARCHAR(MAX),
    [Flag Estado Aprovisión] NVARCHAR(MAX),
    [Flag Fallas Masivas] NVARCHAR(MAX),
    [Flag Materiales] NVARCHAR(MAX),
    [Notas Materiales] NVARCHAR(MAX),
    [Flag Niveles] NVARCHAR(MAX),
    [Grupo Socioeconomico] NVARCHAR(MAX),
    [Indicador Capacidad] NVARCHAR(MAX),
    [Area derivación] NVARCHAR(MAX),
    [Nodo] NVARCHAR(MAX),
    [Nombre Completo Persona] NVARCHAR(MAX),
    [Nro Orden] NVARCHAR(MAX),
    [Nro Solicitud de Servicio] NVARCHAR(MAX),
    [Teléfono] NVARCHAR(MAX),
    [Prioridad] NVARCHAR(MAX),
    [Razón de Cancelación] NVARCHAR(MAX),
    [Inicio - Fin] NVARCHAR(MAX),
    [Estado] NVARCHAR(MAX),
    [Provincia] NVARCHAR(MAX),
    [Subnodo] NVARCHAR(MAX),
    [Territorio] NVARCHAR(MAX),
    [Tipo de Vivienda] NVARCHAR(MAX),
    [Tiempo de viaje] NVARCHAR(MAX),
    [Usuario Creador Actividad] NVARCHAR(MAX),
    [Zona de trabajo] NVARCHAR(MAX),
    [Código postal] NVARCHAR(MAX),
    [Zona] NVARCHAR(MAX),
    [Ventana de Servicio] NVARCHAR(MAX),
    [Código de Cierre] NVARCHAR(MAX),
    [Notas de Cierre (máx 500 caract. , sin caract especial "&")] NVARCHAR(MAX),
    [ID de actividad] NVARCHAR(MAX),
    [Rut o Bucket] NVARCHAR(MAX),
    [Pasos] NVARCHAR(MAX),
    [Hora de reserva de actividad] NVARCHAR(MAX),
    [Flag Corte de Acometida] NVARCHAR(MAX),
    [Flag Retiro de Materiales] NVARCHAR(MAX),
    [Flag Televisión Análoga] NVARCHAR(MAX),
    [Flag que indica si hay Internet] NVARCHAR(MAX),
    [Flag que indica si hay Telefonía] NVARCHAR(MAX),
    [Flag que indica si hay Televisión] NVARCHAR(MAX),
    [Equipos sin retirar] NVARCHAR(MAX),
    [Inicio SLA] NVARCHAR(MAX),
    [Activity status change by] NVARCHAR(MAX),
    [MAC del MTA] NVARCHAR(MAX),
    [Tipo Red] NVARCHAR(MAX),
    [Código GIS] NVARCHAR(MAX),
    [Categorización del número de derivaciones] NVARCHAR(MAX),
    [Agenda confirmada] NVARCHAR(MAX),
    [Criterios de priorización] NVARCHAR(MAX),
    [Cantidad de derivaciones NO terreno] NVARCHAR(MAX),
    [Cantidad de derivaciones terreno] NVARCHAR(MAX),
    [Bucket Original] NVARCHAR(MAX),
    [Tipo de work skill Siebel] NVARCHAR(MAX),
    [Items Orden] NTEXT,
    [Notas de Suspensión (máx 500 caract. , sin caract especial "&")] NVARCHAR(MAX),
    [Usuario que suspende] NVARCHAR(MAX),
    [Hora de asignación de actividad] NVARCHAR(MAX),
    [Cierre Suspender] NVARCHAR(MAX),
    [Marca] NVARCHAR(MAX),
    [Tipo red producto] NVARCHAR(MAX),
    [Access ID] NVARCHAR(MAX),
    [Aptitud laboral] NVARCHAR(MAX),
    [Flag Resultado DROP] NVARCHAR(MAX),
    [Flag Resultado NAP] NVARCHAR(MAX),
    [Tipo NAP] NVARCHAR(MAX),
    [QR DROP] NVARCHAR(MAX),
    [Nap ID] NVARCHAR(MAX),
    [Nap Ubicación] NVARCHAR(MAX),
    [Puerto NAP] NVARCHAR(MAX),
    [EOS asociado] NVARCHAR(MAX),
    [Flag Cambio Pelo] NVARCHAR(MAX),
    [Respuesta Intervencion Cambio Pelo] NVARCHAR(MAX),
    [Flag Consulta Vecino] NVARCHAR(MAX),
    [Notas Consulta Vecino] NVARCHAR(MAX),
    [Uso interno plugin cambio de pelo (no modificar)] NVARCHAR(MAX),
    [Intervención neutra] NVARCHAR(MAX),
    [fecha_integracion] DATETIME
);
GO

-- Paso 2: Copiar datos de la tabla original a la nueva tabla
INSERT INTO tb_toa_reporte_diario_new (
    [Técnico],
    [Orden de Trabajo],
    [Tipo de Actividad],
    [Subtipo],
    [Tipo de Orden],
    [Franja],
    [Inicio],
    [Fin],
    [Cliente],
    [Dirección],
    [Ciudad],
    [Número Cliente],
    [Celular],
    [Clase de Vivienda],
    [Código Ciudad],
    [Código Localidad],
    [Código Territorio],
    [Código Zona],
    [Comentarios de la actividad],
    [Coord X],
    [Coord Y],
    [Fecha],
    [Ventana de Entrega],
    [Descripción de la actividad],
    [Duración],
    [email],
    [Estado de la actividad],
    [Fecha Certificada],
    [Flag Estado Aprovisión],
    [Flag Fallas Masivas],
    [Flag Materiales],
    [Notas Materiales],
    [Flag Niveles],
    [Grupo Socioeconomico],
    [Indicador Capacidad],
    [Area derivación],
    [Nodo],
    [Nombre Completo Persona],
    [Nro Orden],
    [Nro Solicitud de Servicio],
    [Teléfono],
    [Prioridad],
    [Razón de Cancelación],
    [Inicio - Fin],
    [Estado],
    [Provincia],
    [Subnodo],
    [Territorio],
    [Tipo de Vivienda],
    [Tiempo de viaje],
    [Usuario Creador Actividad],
    [Zona de trabajo],
    [Código postal],
    [Zona],
    [Ventana de Servicio],
    [Código de Cierre],
    [Notas de Cierre (máx 500 caract. , sin caract especial "&")],
    [ID de actividad],
    [Rut o Bucket],
    [Pasos],
    [Hora de reserva de actividad],
    [Flag Corte de Acometida],
    [Flag Retiro de Materiales],
    [Flag Televisión Análoga],
    [Flag que indica si hay Internet],
    [Flag que indica si hay Telefonía],
    [Flag que indica si hay Televisión],
    [Equipos sin retirar],
    [Inicio SLA],
    [Activity status change by],
    [MAC del MTA],
    [Tipo Red],
    [Código GIS],
    [Categorización del número de derivaciones],
    [Agenda confirmada],
    [Criterios de priorización],
    [Cantidad de derivaciones NO terreno],
    [Cantidad de derivaciones terreno],
    [Bucket Original],
    [Tipo de work skill Siebel],
    [Items Orden],
    [Notas de Suspensión (máx 500 caract. , sin caract especial "&")],
    [Usuario que suspende],
    [Hora de asignación de actividad],
    [Cierre Suspender],
    [Marca],
    [Tipo red producto],
    [Access ID],
    [Aptitud laboral],
    [Flag Resultado DROP],
    [Flag Resultado NAP],
    [Tipo NAP],
    [QR DROP],
    [Nap ID],
    [Nap Ubicación],
    [Puerto NAP],
    [EOS asociado],
    [Flag Cambio Pelo],
    [Respuesta Intervencion Cambio Pelo],
    [Flag Consulta Vecino],
    [Notas Consulta Vecino],
    [Uso interno plugin cambio de pelo (no modificar)],
    [Intervención neutra],
    [fecha_integracion]
)
SELECT
    NULL AS [Técnico],  -- Nueva columna sin datos
    [Orden de Trabajo],
    [Tipo de Actividad],
    [Subtipo],
    [Tipo de Orden],
    [Franja],
    [Inicio],
    [Fin],
    [Cliente],
    [Dirección],
    [Ciudad],
    [Número Cliente],
    [Celular],
    [Clase de Vivienda],
    [Código Ciudad],
    [Código Localidad],
    [Código Territorio],
    [Código Zona],
    [Comentarios de la actividad],
    [Coord X],
    [Coord Y],
    [Fecha],
    [Ventana de Entrega],
    [Descripción de la actividad],
    [Duración],
    [email],
    [Estado de la actividad],
    [Fecha Certificada],
    [Flag Estado Aprovisión],
    [Flag Fallas Masivas],
    [Flag Materiales],
    [Notas Materiales],
    [Flag Niveles],
    [Grupo Socioeconomico],
    [Indicador Capacidad],
    [Area derivación],
    [Nodo],
    [Nombre Completo Persona],
    [Nro Orden],
    [Nro Solicitud de Servicio],
    [Teléfono],
    [Prioridad],
    [Razón de Cancelación],
    [Inicio - Fin],
    [Estado],
    [Provincia],
    [Subnodo],
    [Territorio],
    [Tipo de Vivienda],
    [Tiempo de viaje],
    [Usuario Creador Actividad],
    [Zona de trabajo],
    [Código postal],
    [Zona],
    [Ventana de Servicio],
    [Código de Cierre],
    [Notas de Cierre (máx 500 caract. , sin caract especial "&")],
    [ID de actividad],
    [Rut o Bucket],
    [Pasos],
    [Hora de reserva de actividad],
    [Flag Corte de Acometida],
    [Flag Retiro de Materiales],
    [Flag Televisión Análoga],
    [Flag que indica si hay Internet],
    [Flag que indica si hay Telefonía],
    [Flag que indica si hay Televisión],
    [Equipos sin retirar],
    [Inicio SLA],
    [Activity status change by],
    [MAC del MTA],
    [Tipo Red],
    [Código GIS],
    [Categorización del número de derivaciones],
    [Agenda confirmada],
    [Criterios de priorización],
    [Cantidad de derivaciones NO terreno],
    [Cantidad de derivaciones terreno],
    [Bucket Original],
    [Tipo de work skill Siebel],
    [Items Orden],
    [Notas de Suspensión (máx 500 caract. , sin caract especial "&")],
    [Usuario que suspende],
    [Hora de asignación de actividad],
    [Cierre Suspender],
    [Marca],
    [Tipo red producto],
    [Access ID],
    [Aptitud laboral],
    [Flag Resultado DROP],
    [Flag Resultado NAP],
    [Tipo NAP],
    [QR DROP],
    [Nap ID],
    [Nap Ubicación],
    [Puerto NAP],
    [EOS asociado],
    [Flag Cambio Pelo],
    [Respuesta Intervencion Cambio Pelo],
    [Flag Consulta Vecino],
    [Notas Consulta Vecino],
    [Uso interno plugin cambio de pelo (no modificar)],
    [Intervención neutra],
    [fecha_integracion]
FROM tb_toa_reporte_diario;
GO

-- Paso 3: Verificar que los datos se copiaron correctamente
SELECT COUNT(*) as registros_copiados FROM tb_toa_reporte_diario_new;
GO

-- Paso 4: Eliminar la tabla original
DROP TABLE tb_toa_reporte_diario;
GO

-- Paso 5: Renombrar la nueva tabla
EXEC sp_rename 'tb_toa_reporte_diario_new', 'tb_toa_reporte_diario';
GO

-- Paso 6: Verificar el resultado final
SELECT TOP 5 COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'tb_toa_reporte_diario' AND TABLE_SCHEMA = 'dbo'
ORDER BY ORDINAL_POSITION;
GO

-- Paso 7: Verificar que los datos están intactos
SELECT COUNT(*) as total_registros FROM tb_toa_reporte_diario;
GO

PRINT '✅ PROCESO COMPLETADO: Columna "Técnico" movida a primera posición';
GO