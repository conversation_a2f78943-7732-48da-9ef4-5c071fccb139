import pandas as pd
import datetime as date
import requests
import json
from datetime import datetime, timedelta
import mysql.connector


url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQzHvElCat-9IBE6PhdJm8xXZcz9d3ar9SCPt9ZBxhFQwp_HIbpDn34QJiM92jehIvWvBOCOKaV1VK9/pub?gid=0&single=true&output=csv'

df = pd.read_csv(url)


# Conexión directa a MySQL
try:
    conn = mysql.connector.connect(
        host='**************',
        user='ncornejo',
        password='N1c0l7as17',
        database='operaciones_tqw'
    )
    cursor = conn.cursor()
    
    # Truncate table para evitar duplicados
    cursor.execute("TRUNCATE TABLE tb_anexo_liquidacion")
    
    # Preparar datos para inserción
    records = []
    for _, row in df.iterrows():
        record = [None if pd.isna(x) else x for x in row]
        records.append(tuple(record))
    
    # Preparar la consulta de inserción
    columns = ", ".join(df.columns)
    placeholders = ", ".join(["%s"] * len(df.columns))
    insert_query = f"INSERT INTO tb_anexo_liquidacion ({columns}) VALUES ({placeholders})"
    
    # Insertar por lotes
    batch_size = 1000
    for i in range(0, len(records), batch_size):
        batch = records[i:i + batch_size]
        cursor.executemany(insert_query, batch)
        conn.commit()
        print(f"Insertados {i + len(batch)} de {len(records)} registros")
    
    print("Datos insertados con éxito en tb_anexo_liquidacion")
    
except mysql.connector.Error as err:
    print(f"Error de MySQL: {err}")
    # Imprimir muestra de datos problemáticos si hay error
    if 'records' in locals() and len(records) > 0:
        print("Muestra de datos con problemas:")
        print(records[0] if len(records) > 0 else "No hay registros")
finally:
    if 'cursor' in locals() and cursor is not None:
        cursor.close()
    if 'conn' in locals() and conn is not None:
        conn.close()



url2 = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQzHvElCat-9IBE6PhdJm8xXZcz9d3ar9SCPt9ZBxhFQwp_HIbpDn34QJiM92jehIvWvBOCOKaV1VK9/pub?gid=991786719&single=true&output=csv'

df2 = pd.read_csv(url2)

# Conexión directa a MySQL para la segunda tabla
try:
    conn = mysql.connector.connect(
        host='**************',
        user='ncornejo',
        password='N1c0l7as17',
        database='operaciones_tqw'
    )
    cursor = conn.cursor()
    
    # Truncate table para evitar duplicados
    cursor.execute("TRUNCATE TABLE tb_anexo_liquidacion_bonos")
    
    # Preparar datos para inserción
    records = []
    for _, row in df2.iterrows():
        record = [None if pd.isna(x) else x for x in row]
        records.append(tuple(record))
    
    # Preparar la consulta de inserción
    columns = ", ".join(df2.columns)
    placeholders = ", ".join(["%s"] * len(df2.columns))
    insert_query = f"INSERT INTO tb_anexo_liquidacion_bonos ({columns}) VALUES ({placeholders})"
    
    # Insertar por lotes
    batch_size = 1000
    for i in range(0, len(records), batch_size):
        batch = records[i:i + batch_size]
        cursor.executemany(insert_query, batch)
        conn.commit()
        print(f"Insertados {i + len(batch)} de {len(records)} registros")
    
    print("Datos insertados con éxito en tb_anexo_liquidacion_bonos")
    
except mysql.connector.Error as err:
    print(f"Error de MySQL: {err}")
    # Imprimir muestra de datos problemáticos si hay error
    if 'records' in locals() and len(records) > 0:
        print("Muestra de datos con problemas:")
        print(records[0] if len(records) > 0 else "No hay registros")
finally:
    if 'cursor' in locals() and cursor is not None:
        cursor.close()
    if 'conn' in locals() and conn is not None:
        conn.close()








