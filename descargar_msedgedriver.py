#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import zipfile
import requests
import platform
import winreg
import logging
from tqdm import tqdm

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('msedgedriver_install.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_edge_version():
    """Obtener la versión de Microsoft Edge instalada en Windows"""
    try:
        # Abrir la clave de registro de Edge
        key_path = r"SOFTWARE\Microsoft\Edge\BLBeacon"
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path)
        
        # Leer la versión
        version = winreg.QueryValueEx(key, "version")[0]
        winreg.<PERSON><PERSON>ey(key)
        
        # Obtener solo la versión principal (por ejemplo, 115.0.1901.203 -> 115)
        major_version = version.split('.')[0]
        
        logger.info(f"Versión de Edge detectada: {version} (versión principal: {major_version})")
        return version, major_version
        
    except Exception as e:
        logger.error(f"Error obteniendo la versión de Edge: {e}")
        logger.info("No se pudo detectar la versión de Edge. Se utilizará la última versión disponible.")
        return None, None

def get_latest_stable_version():
    """Obtiene la última versión estable de Edge driver desde la página web oficial"""
    try:
        url = "https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            # Buscar la URL de descarga para Stable Channel en el contenido HTML
            import re
            # Buscar patrones como https://msedgedriver.microsoft.com/XX.X.XXXX.XX/edgedriver_win64.zip
            pattern = r'https://msedgedriver\.microsoft\.com/(\d+\.\d+\.\d+\.\d+)/edgedriver_win64\.zip'
            matches = re.findall(pattern, response.text)
            
            if matches and len(matches) > 0:
                # Tomar la primera versión encontrada (generalmente la Stable)
                version = matches[0]
                logger.info(f"Versión estable encontrada en la web: {version}")
                return version
        
        logger.warning("No se pudo obtener la última versión estable desde la web oficial")
        return None
    except Exception as e:
        logger.error(f"Error obteniendo la versión estable: {e}")
        return None

def download_msedgedriver(version=None):
    """Descargar el msedgedriver para la versión específica de Edge"""
    
    # URL base para la descarga directa
    base_url = "https://msedgedriver.microsoft.com"
    
    # Determinar la arquitectura del sistema
    if platform.machine().endswith('64'):
        arch = "x64"
    else:
        arch = "x86"
    
    # Determinar el sistema operativo
    if platform.system() == "Windows":
        os_name = "win"
    elif platform.system() == "Darwin":
        os_name = "mac"
        if platform.machine() == "arm64":
            arch = "arm64"
    else:
        os_name = "linux"
    
    # Construir el nombre de archivo según la plataforma
    if os_name == "win":
        filename = f"edgedriver_{os_name}{arch}.zip"
    elif os_name == "mac":
        if arch == "arm64":
            filename = "edgedriver_mac64_m1.zip"
        else:
            filename = f"edgedriver_{os_name}{arch}.zip"
    else:  # linux
        filename = f"edgedriver_{os_name}{arch}.zip"
    
    # Si no se proporciona versión, intentar obtener la última estable
    if not version:
        version = get_latest_stable_version()
        if not version:
            # Si no se puede obtener la versión desde la web, usar una versión conocida
            # Esta es una versión de respaldo conocida que suele estar disponible
            version = "138.0.3375.0"  # Versión conocida disponible
            logger.info(f"Usando versión de respaldo: {version}")
    
    # Construir la URL de descarga directa
    download_url = f"{base_url}/{version}/{filename}"
    logger.info(f"URL de descarga: {download_url}")
    
    # Directorio de descarga (en la misma ubicación que este script)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    download_path = os.path.join(script_dir, "edgedriver.zip")
    extract_path = script_dir
    
    logger.info(f"Descargando msedgedriver desde: {download_url}")
    
    try:
        # Descargar el archivo con manejo especial para redirecciones
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203'
        }
        
        response = requests.get(download_url, stream=True, headers=headers, allow_redirects=True)
        
        # Si es URL de Microsoft Developer, seguir la redirección y obtener el nombre del archivo
        if "microsoft-edge/tools/webdriver/download" in download_url and response.status_code == 200:
            # La descarga comenzó automáticamente, extraer nombre de archivo de las cabeceras
            content_disposition = response.headers.get('Content-Disposition', '')
            if 'filename=' in content_disposition:
                suggested_filename = content_disposition.split('filename=')[1].strip('"')
                logger.info(f"Nombre de archivo sugerido: {suggested_filename}")
        
        response.raise_for_status()  # Verificar si hay errores HTTP
        
        # Tamaño total del archivo
        total_size = int(response.headers.get('content-length', 0))
        
        # Escribir el archivo con barra de progreso
        with open(download_path, 'wb') as file, tqdm(
            desc="Descargando",
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as bar:
            for data in response.iter_content(chunk_size=1024):
                size = file.write(data)
                bar.update(size)
        
        logger.info(f"Archivo descargado en: {download_path}")
        
        # Descomprimir el archivo
        logger.info(f"Descomprimiendo en: {extract_path}")
        with zipfile.ZipFile(download_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        
        # Verificar que el archivo msedgedriver.exe exista
        msedgedriver_path = os.path.join(extract_path, "msedgedriver.exe")
        if os.path.exists(msedgedriver_path):
            logger.info(f"msedgedriver.exe extraído exitosamente en: {msedgedriver_path}")
            
            # Eliminar el archivo zip
            os.remove(download_path)
            logger.info("Archivo zip eliminado")
            
            return msedgedriver_path
        else:
            logger.error("No se pudo encontrar msedgedriver.exe después de descomprimir")
            return None
            
    except requests.exceptions.RequestException as e:
        logger.error(f"Error durante la descarga: {e}")
        
        if response.status_code == 404:
            logger.error(f"No se encontró la versión {version} del driver. Intentando con la última versión estable.")
            # Si falló con la versión específica, intentar con la última versión estable
            if version:
                return download_msedgedriver(None)
        return None
    except Exception as e:
        logger.error(f"Error al descomprimir o procesar el archivo: {e}")
        return None

def main():
    """Función principal para descargar e instalar msedgedriver"""
    logger.info("=== INICIO DE DESCARGA DE MSEDGEDRIVER ===")
    
    # Obtener la versión de Edge
    edge_version, major_version = get_edge_version()
    latest_version = get_latest_stable_version()
    
    print("\nOpciones disponibles:")
    print("1. Descargar la última versión estable disponible en la web oficial")
    
    if edge_version:
        print(f"2. Descargar la versión para su Microsoft Edge ({edge_version})")
    else:
        print("2. No disponible - No se pudo detectar su versión de Edge")
    
    if latest_version:
        print(f"\nNota: La última versión estable encontrada es: {latest_version}")
    
    choice = input("\nSeleccione una opción (1/2): ").strip()
    
    if choice == "2" and edge_version:
        logger.info(f"Descargando driver para versión específica: {edge_version}")
        msedgedriver_path = download_msedgedriver(edge_version)
    else:
        if latest_version:
            logger.info(f"Descargando la última versión estable del driver: {latest_version}")
            msedgedriver_path = download_msedgedriver(latest_version)
        else:
            logger.info("Descargando usando versión de respaldo")
            msedgedriver_path = download_msedgedriver()
    
    if msedgedriver_path:
        logger.info("\n=== INSTALACIÓN COMPLETADA ===")
        logger.info(f"msedgedriver ha sido instalado en: {msedgedriver_path}")
        logger.info("Ahora puede ejecutar el script NdcBot.py")
    else:
        logger.error("\n=== ERROR EN LA INSTALACIÓN ===")
        logger.error("No se pudo descargar e instalar msedgedriver")
        logger.error("Por favor, descárguelo manualmente desde:")
        logger.error("https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")

if __name__ == "__main__":
    main()