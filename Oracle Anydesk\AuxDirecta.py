# from selenium import webdriver
# from selenium.webdriver.common.by import By  # Importa la clase By
# from selenium.webdriver.common.keys import Keys
import time
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriveFr.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

import psutil
from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()
session.execute(text("EXEC SP_INSERT_ORACLE_DIRECTA"))

session.commit()


# result = engine.execute(text("EXEC SP_INSERT_ORACLE_DIRECTA")).fetchone()
# return_value = result[0]

#     # Verificar el valor de retorno
# if return_value == 0:
#         cerrar_proceso("jp2launcher.exe")

# else:
    # session.execute(text("EXEC "))
    # session.commit()

Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_DIRECTAX", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('TB_FERRET_DIRECTA1', engineMYsql, if_exists='replace',index=False)

# Define l  a sentencia SQL para crear el índice
sql = text("CREATE INDEX idx_serial ON TB_FERRET_DIRECTA1 (Serial(255));")



session.close()

# Ejecuta la sentencia SQL
with engineMYsql.connect() as connection:
            connection.execute(sql)

# Cierra la conexión
engineMYsql.dispose()




# coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreOracle.png')
# if coordenadas:
#     pyautogui.click(coordenadas[0], coordenadas[1])

# time.sleep(3)


# coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreOracleFinal.png')
# if coordenadas:
#     pyautogui.click(coordenadas[0], coordenadas[1])

