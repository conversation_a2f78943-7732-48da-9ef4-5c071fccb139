"""
Database operations module for SQL Server interactions.
Handles Excel file uploads, table creation, and data insertion.
"""

import pandas as pd
import pyodbc
import logging
from typing import Optional, Dict, List, Tuple
from config import SQL_SERVER_CONFIG, DATABASE_CONFIG, ERROR_MESSAGES

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages SQL Server database operations for Excel file uploads."""

    def __init__(self):
        self.config = SQL_SERVER_CONFIG
        self.db_config = DATABASE_CONFIG
        self.connection = None
        self.cursor = None

    def connect(self) -> bool:
        """
        Establish connection to SQL Server.

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            conn_str = (
                f"DRIVER={{{self.config['driver']}}};"
                f"SERVER={self.config['server']};"
                f"DATABASE={self.config['database']};"
                f"UID={self.config['uid']};"
                f"PWD={self.config['pwd']};"
                f"CONNECTION TIMEOUT={self.db_config['connection_timeout']}"
            )

            self.connection = pyodbc.connect(conn_str)
            self.cursor = self.connection.cursor()
            logger.info("✅ Connected to SQL Server successfully")
            return True

        except Exception as e:
            logger.error(f"❌ {ERROR_MESSAGES['connection_failed']}: {str(e)}")
            return False

    def disconnect(self):
        """Close database connection and cursor."""
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            bool: True if table exists, False otherwise
        """
        try:
            self.cursor.execute(
                "SELECT OBJECT_ID(?, 'U')",
                (table_name,)
            )
            result = self.cursor.fetchone()
            return result[0] is not None
        except Exception as e:
            logger.error(f"Error checking if table exists: {str(e)}")
            return False

    def create_table(self, table_name: str, column_types: Dict[str, str]) -> bool:
        """
        Create a new table with specified column types.

        Args:
            table_name: Name of the table to create
            column_types: Dictionary mapping column names to SQL data types

        Returns:
            bool: True if table created successfully, False otherwise
        """
        try:
            columns_def = [f"[{col}] {col_type}" for col, col_type in column_types.items()]
            create_sql = f"CREATE TABLE {table_name} ({', '.join(columns_def)})"

            logger.info(f"Creating table: {create_sql}")
            self.cursor.execute(create_sql)
            self.connection.commit()

            logger.info(f"✅ Table {table_name} created successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error creating table {table_name}: {str(e)}")
            self.connection.rollback()
            return False

    def drop_table(self, table_name: str) -> bool:
        """
        Drop an existing table.

        Args:
            table_name: Name of the table to drop

        Returns:
            bool: True if table dropped successfully, False otherwise
        """
        try:
            self.cursor.execute(f"DROP TABLE {table_name}")
            self.connection.commit()
            logger.info(f"✅ Table {table_name} dropped successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Error dropping table {table_name}: {str(e)}")
            self.connection.rollback()
            return False

    def get_table_columns(self, table_name: str) -> List[str]:
        """
        Get column names from an existing table.

        Args:
            table_name: Name of the table

        Returns:
            List of column names
        """
        try:
            self.cursor.execute(f"""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
            """)
            columns = [row[0] for row in self.cursor.fetchall()]
            return columns
        except Exception as e:
            logger.error(f"Error getting table columns: {str(e)}")
            return []

    def get_column_types(self, table_name: str) -> Dict[str, str]:
        """
        Get column names and their data types from an existing table.

        Args:
            table_name: Name of the table

        Returns:
            Dictionary mapping column names to their data types
        """
        try:
            self.cursor.execute(f"""
                SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
            """)
            columns_info = {}
            for row in self.cursor.fetchall():
                col_name, data_type, max_length = row
                if max_length == -1:  # NVARCHAR(MAX)
                    columns_info[col_name] = f"{data_type}(MAX)"
                elif max_length:
                    columns_info[col_name] = f"{data_type}({max_length})"
                else:
                    columns_info[col_name] = data_type
            return columns_info
        except Exception as e:
            logger.error(f"Error getting column types: {str(e)}")
            return {}

    def alter_column_to_nvarchar_max(self, table_name: str, column_name: str) -> bool:
        """
        Alter a column to NVARCHAR(MAX) to handle unlimited text length.

        Args:
            table_name: Name of the table
            column_name: Name of the column to alter

        Returns:
            bool: True if alteration successful, False otherwise
        """
        try:
            alter_sql = f"ALTER TABLE {table_name} ALTER COLUMN [{column_name}] NVARCHAR(MAX)"
            self.cursor.execute(alter_sql)
            self.connection.commit()
            logger.info(f"✅ Column {column_name} altered to NVARCHAR(MAX)")
            return True
        except Exception as e:
            logger.error(f"❌ Error altering column {column_name}: {str(e)}")
            return False

    def add_column_to_table(self, table_name: str, column_name: str, data_type: str = 'NVARCHAR(MAX)') -> bool:
        """
        Add a new column to an existing table.

        Args:
            table_name: Name of the table
            column_name: Name of the new column
            data_type: SQL data type for the new column

        Returns:
            bool: True if column added successfully, False otherwise
        """
        try:
            alter_sql = f"ALTER TABLE {table_name} ADD [{column_name}] {data_type}"
            self.cursor.execute(alter_sql)
            self.connection.commit()
            logger.info(f"✅ Column '{column_name}' added to table {table_name} with type {data_type}")
            return True
        except Exception as e:
            logger.error(f"❌ Error adding column {column_name}: {str(e)}")
            return False

    def ensure_table_columns_are_nvarchar_max(self, table_name: str, expected_columns: List[str]) -> bool:
        """
        Ensure all text columns in the table are NVARCHAR(MAX) for unlimited text handling.

        Args:
            table_name: Name of the table
            expected_columns: List of columns that should be NVARCHAR(MAX)

        Returns:
            bool: True if all columns are properly configured, False otherwise
        """
        try:
            logger.info("🔧 Checking and updating table column types for unlimited text support...")

            # Get current column types
            current_types = self.get_column_types(table_name)

            if not current_types:
                logger.warning("⚠️ Could not retrieve column types, skipping alteration")
                return False

            # Check each expected column
            alterations_needed = []
            for col in expected_columns:
                if col in current_types:
                    current_type = current_types[col].upper()
                    # Check if it's not already NVARCHAR(MAX)
                    if 'NVARCHAR(MAX)' not in current_type and 'NTEXT' not in current_type:
                        alterations_needed.append(col)
                        logger.info(f"📝 Column {col} needs alteration: {current_type} → NVARCHAR(MAX)")

            # Perform alterations
            if alterations_needed:
                logger.info(f"🔄 Altering {len(alterations_needed)} columns to NVARCHAR(MAX)...")
                for col in alterations_needed:
                    if not self.alter_column_to_nvarchar_max(table_name, col):
                        logger.error(f"❌ Failed to alter column {col}")
                        return False

                logger.info("✅ All table columns updated to NVARCHAR(MAX)")
                return True
            else:
                logger.info("✅ All table columns are already NVARCHAR(MAX)")
                return True

        except Exception as e:
            logger.error(f"❌ Error ensuring NVARCHAR(MAX) columns: {str(e)}")
            return False

    def create_view_latest_data(self, table_name: str, view_name: str = None) -> bool:
        """
        Create a view that shows only the latest data based on fecha_integracion.

        Args:
            table_name: Source table name
            view_name: Name for the view (defaults to table_name + '_latest')

        Returns:
            bool: True if view created successfully, False otherwise
        """
        if view_name is None:
            view_name = f"{table_name}_latest"

        try:
            logger.info(f"📊 Creating view {view_name} for latest data from {table_name}...")

            # Check if view already exists and drop it
            self.cursor.execute(f"""
                IF OBJECT_ID('{view_name}', 'V') IS NOT NULL
                DROP VIEW {view_name}
            """)
            self.connection.commit()

            # Create the view with latest data
            create_view_sql = f"""
                CREATE VIEW {view_name} AS
                SELECT *, fecha_integracion AS fecha_intv2
                FROM {table_name}
                WHERE fecha_integracion = (
                    SELECT MAX(fecha_integracion)
                    FROM {table_name}
                )
            """

            self.cursor.execute(create_view_sql)
            self.connection.commit()

            logger.info(f"✅ View {view_name} created successfully")
            logger.info(f"📋 View shows only records with the latest fecha_integracion")
            return True

        except Exception as e:
            logger.error(f"❌ Error creating view {view_name}: {str(e)}")
            return False

    def get_latest_fecha_integracion(self, table_name: str) -> str:
        """
        Get the latest fecha_integracion value from the table.

        Args:
            table_name: Name of the table

        Returns:
            str: Latest fecha_integracion as string, or None if error
        """
        try:
            self.cursor.execute(f"""
                SELECT MAX(fecha_integracion) as latest_fecha
                FROM {table_name}
            """)

            result = self.cursor.fetchone()
            if result and result[0]:
                latest_fecha = str(result[0])
                logger.info(f"📅 Latest fecha_integracion: {latest_fecha}")
                return latest_fecha
            else:
                logger.warning("⚠️ No fecha_integracion found in table")
                return None

        except Exception as e:
            logger.error(f"❌ Error getting latest fecha_integracion: {str(e)}")
            return None

    def query_view(self, view_name: str, limit: int = None) -> List[Tuple]:
        """
        Query a view and return results.

        Args:
            view_name: Name of the view to query
            limit: Optional limit for number of rows

        Returns:
            List of tuples containing the view data
        """
        try:
            limit_clause = f"TOP {limit}" if limit else ""

            self.cursor.execute(f"""
                SELECT {limit_clause} * FROM {view_name}
                ORDER BY fecha_integracion DESC
            """)

            results = self.cursor.fetchall()
            logger.info(f"📊 Retrieved {len(results)} rows from view {view_name}")
            return results

        except Exception as e:
            logger.error(f"❌ Error querying view {view_name}: {str(e)}")
            return []

    def create_staging_table(self, table_name: str, column_types: Dict[str, str]) -> str:
        """
        Create or truncate a staging table with the same structure as the main table.
        Uses TRUNCATE instead of DROP to preserve Power BI connections.

        Args:
            table_name: Name of the base table (staging table will be created with _staging suffix)
            column_types: Dictionary mapping column names to SQL data types

        Returns:
            str: Name of the created staging table, or False if error
        """
        try:
            # Add staging suffix if not present
            if not table_name.endswith('_staging'):
                staging_table = f"{table_name}_staging"
            else:
                staging_table = table_name

            logger.info(f"🏗️ Setting up staging table: {staging_table}")

            # Check if table exists
            table_exists = self.table_exists(staging_table)

            if table_exists:
                # Table exists - TRUNCATE to preserve structure and Power BI connection
                logger.info(f"🔄 Table {staging_table} exists, truncating to preserve Power BI connection...")
                self.cursor.execute(f"TRUNCATE TABLE {staging_table}")
                self.connection.commit()
                logger.info(f"✅ Staging table {staging_table} truncated successfully")
                return staging_table
            else:
                # Table doesn't exist - create it
                logger.info(f"📝 Table {staging_table} doesn't exist, creating...")

                # Create staging table with same structure
                columns_def = [f"[{col}] {col_type}" for col, col_type in column_types.items()]
                create_sql = f"CREATE TABLE {staging_table} ({', '.join(columns_def)})"

                logger.info(f"Creating staging table: {create_sql}")
                self.cursor.execute(create_sql)
                self.connection.commit()

                logger.info(f"✅ Staging table {staging_table} created successfully")
                return staging_table

        except Exception as e:
            logger.error(f"❌ Error setting up staging table {staging_table}: {str(e)}")
            self.connection.rollback()
            return False

    def execute_stored_procedure(self, sp_name: str, parameters: Dict[str, any] = None) -> bool:
        """
        Execute a stored procedure with optional parameters.

        Args:
            sp_name: Name of the stored procedure
            parameters: Dictionary of parameter names and values

        Returns:
            bool: True if SP executed successfully, False otherwise
        """
        try:
            logger.info(f"⚙️ Executing stored procedure: {sp_name}")

            if parameters:
                # Build parameter string
                param_list = []
                param_values = []
                for param_name, param_value in parameters.items():
                    param_list.append(f"@{param_name} = ?")
                    param_values.append(param_value)

                param_str = ", ".join(param_list)
                exec_sql = f"EXEC {sp_name} {param_str}"

                logger.info(f"Executing: {exec_sql}")
                self.cursor.execute(exec_sql, param_values)
            else:
                exec_sql = f"EXEC {sp_name}"
                logger.info(f"Executing: {exec_sql}")
                self.cursor.execute(exec_sql)

            self.connection.commit()
            logger.info(f"✅ Stored procedure {sp_name} executed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error executing stored procedure {sp_name}: {str(e)}")
            self.connection.rollback()
            return False

    def insert_data_batch(self, table_name: str, data: List[Tuple], columns: List[str]) -> bool:
        """
        Insert data in batches to improve performance.
        Handles column mapping for existing tables.

        Args:
            table_name: Target table name
            data: List of tuples containing row data
            columns: List of column names from Excel

        Returns:
            bool: True if all data inserted successfully, False otherwise
        """
        try:
            # Get existing table columns if table exists
            existing_columns = self.get_table_columns(table_name)
            if existing_columns:
                logger.info(f"📋 Existing table columns: {existing_columns}")
                logger.info(f"📋 Excel columns: {columns}")

                # Filter to only use columns that exist in the table
                valid_columns = [col for col in columns if col in existing_columns]
                if not valid_columns:
                    logger.error("❌ No matching columns found between Excel and table")
                    return False

                if len(valid_columns) != len(columns):
                    logger.warning(f"⚠️ Only {len(valid_columns)}/{len(columns)} columns match. Using: {valid_columns}")

                # Filter data to only include valid columns
                column_indices = [columns.index(col) for col in valid_columns]
                filtered_data = []
                for row in data:
                    filtered_row = tuple(row[i] for i in column_indices)
                    filtered_data.append(filtered_row)

                data = filtered_data
                columns = valid_columns

            batch_size = self.db_config['batch_size']
            total_rows = len(data)
            total_batches = (total_rows + batch_size - 1) // batch_size

            # Prepare SQL query
            columns_str = ", ".join([f"[{col}]" for col in columns])
            placeholders = ", ".join(["?" for _ in columns])
            insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

            logger.info(f"Inserting {total_rows} records in {total_batches} batches...")
            logger.info(f"Using columns: {columns}")

            for i in range(total_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, total_rows)
                batch_data = data[start_idx:end_idx]

                try:
                    self.cursor.executemany(insert_query, batch_data)
                    self.connection.commit()
                    logger.info(f"✅ Batch {i+1}/{total_batches} completed ({len(batch_data)} records)")
                except Exception as batch_error:
                    logger.error(f"❌ Error in batch {i+1}: {str(batch_error)}")
                    # Try inserting row by row for problematic batch
                    self._insert_row_by_row(insert_query, batch_data, columns)
                    continue

            return True

        except Exception as e:
            logger.error(f"❌ Error in batch insert: {str(e)}")
            self.connection.rollback()
            return False

    def _insert_row_by_row(self, query: str, data: List[Tuple], columns: List[str] = None):
        """
        Insert data row by row when batch insert fails.
        Handles data truncation and provides detailed error reporting.

        Args:
            query: SQL insert query
            data: List of tuples containing row data
            columns: List of column names for better error reporting
        """
        successful_inserts = 0
        truncation_errors = 0

        for j, row_data in enumerate(data):
            try:
                self.cursor.execute(query, row_data)
                self.connection.commit()
                successful_inserts += 1
            except Exception as row_error:
                error_msg = str(row_error).lower()

                # Handle string truncation errors
                if "truncated" in error_msg or "8152" in error_msg:
                    truncation_errors += 1
                    logger.warning(f"⚠️ Data truncation in row {j+1}, attempting to truncate data...")

                    # Try to insert with truncated data
                    if self._insert_with_truncated_data(query, row_data, columns, j):
                        successful_inserts += 1
                        logger.info(f"✅ Row {j+1} inserted with truncated data")
                    else:
                        logger.error(f"❌ Failed to insert row {j+1} even with truncation: {str(row_error)}")
                else:
                    logger.error(f"❌ Error in row {j+1}: {str(row_error)}")
                continue

        logger.info(f"✅ Inserted {successful_inserts}/{len(data)} rows from problematic batch")
        if truncation_errors > 0:
            logger.warning(f"⚠️ {truncation_errors} rows had data truncation issues")

    def _insert_with_truncated_data(self, query: str, row_data: Tuple, columns: List[str] = None, row_index: int = 0) -> bool:
        """
        Attempt to insert data with truncated values when truncation errors occur.

        Args:
            query: SQL insert query
            row_data: Tuple containing row data
            columns: List of column names
            row_index: Index of the row for logging

        Returns:
            bool: True if insert successful with truncated data, False otherwise
        """
        try:
            # Create a copy of the row data to modify
            truncated_data = list(row_data)

            # Truncate string values that might be too long
            # SQL Server NVARCHAR(MAX) can handle up to 2GB, so this is a fallback
            for i, value in enumerate(truncated_data):
                if isinstance(value, str) and len(value) > 8000:  # Conservative limit
                    truncated_data[i] = value[:8000]  # Truncate to 8000 chars
                    logger.warning(f"⚠️ Truncated column {i} in row {row_index} from {len(value)} to 8000 chars")

            # Try to insert with truncated data
            self.cursor.execute(query, tuple(truncated_data))
            self.connection.commit()
            return True

        except Exception as e:
            logger.error(f"❌ Failed to insert even with truncated data: {str(e)}")
            return False

    def analyze_column_types(self, df: pd.DataFrame) -> Tuple[Dict[str, str], Dict[str, str]]:
        """
        Analyze DataFrame columns to determine appropriate SQL data types.
        Uses flexible types to handle variable data lengths.

        Args:
            df: Pandas DataFrame to analyze

        Returns:
            Tuple of (column_types, column_mapping) dictionaries
        """
        column_types = {}
        column_mapping = {}

        for col in df.columns:
            # Handle long column names
            if len(col) > 128:
                short_name = f"col_{len(column_mapping) + 1}"
                column_mapping[col] = short_name
                sql_col_name = short_name
                logger.warning(f"⚠️ Column name too long: '{col[:50]}...' → '{sql_col_name}'")
            else:
                sql_col_name = col
                column_mapping[col] = col

            # Use flexible NVARCHAR(MAX) for all text columns to handle variable lengths
            column_types[sql_col_name] = 'NVARCHAR(MAX)'

            # Log column information for debugging
            if len(df[col].dropna()) > 0:
                max_length = df[col].dropna().astype(str).str.len().max()
                logger.info(f"📊 Column '{sql_col_name}': {column_types[sql_col_name]} (max {max_length} chars)")
            else:
                logger.info(f"📊 Column '{sql_col_name}': {column_types[sql_col_name]} (empty column)")

        # Add timestamp column
        timestamp_col = 'fecha_integracion'
        column_types[timestamp_col] = 'DATETIME'
        column_mapping[timestamp_col] = timestamp_col

        return column_types, column_mapping

def upload_excel_to_sql_server(
    excel_file_path: str,
    table_name: str = 'tb_toa_reporte_diario',
    mode: str = 'replace',
    create_latest_view: bool = True,
    use_staging_approach: bool = False
) -> bool:
    """
    Upload Excel file to SQL Server with comprehensive error handling.
    Now supports staging table approach for better data management.

    Args:
        excel_file_path: Path to the Excel file
        table_name: Target table name (historical table)
        mode: 'replace' to recreate table, 'append' to add to existing
        use_staging_approach: If True, use staging table + SP approach

    Returns:
        bool: True if upload successful, False otherwise
    """
    db_manager = DatabaseManager()

    try:
        logger.info(f"📊 Starting Excel upload to SQL Server...")
        logger.info(f"   📄 File: {excel_file_path}")
        logger.info(f"   🗄️  Table: {table_name}")
        logger.info(f"   🔄 Mode: {mode}")
        logger.info(f"   🏗️  Staging approach: {use_staging_approach}")

        # Validate file exists
        if not pd.io.common.file_exists(excel_file_path):
            logger.error(f"❌ {ERROR_MESSAGES['file_not_found']}: {excel_file_path}")
            return False

        # Read Excel file
        logger.info("📖 Reading Excel file...")
        df = pd.read_excel(excel_file_path, engine='openpyxl')

        if df.empty:
            logger.error(f"❌ {ERROR_MESSAGES['empty_file']}")
            return False

        logger.info(f"✅ File loaded: {len(df)} rows, {len(df.columns)} columns")

        # Add integration timestamp
        current_timestamp = pd.Timestamp.now()
        df['fecha_integracion'] = current_timestamp
        logger.info(f"🕒 Integration timestamp: {current_timestamp}")

        # Connect to database
        if not db_manager.connect():
            return False

        # Analyze column types
        logger.info("🔍 Analyzing data structure...")
        column_types, column_mapping = db_manager.analyze_column_types(df)

        # Prepare data for insertion
        sql_columns = [column_mapping[col] for col in df.columns]

        if use_staging_approach:
            # STAGING APPROACH: Use staging table + SP
            logger.info("🏗️ Using staging table approach...")

            # Step 1: Apply filter on Técnico field before processing
            logger.info("🔍 Applying filter on Técnico field (LIKE '%TQW%')...")
            original_count = len(df)

            # Check if 'Técnico' column exists
            tecnico_col = None
            for col in df.columns:
                if 'técnico' in col.lower() or 'tecnico' in col.lower():
                    tecnico_col = col
                    break

            if tecnico_col:
                # Apply filter: Técnico LIKE '%TQW%'
                df_filtered = df[df[tecnico_col].astype(str).str.contains('TQW', case=False, na=False)]
                filtered_count = len(df_filtered)
                logger.info(f"✅ Filter applied: {original_count} → {filtered_count} records")
                logger.info(f"📊 Filtered out {original_count - filtered_count} records that don't match 'TQW' pattern")
                df = df_filtered
            else:
                logger.warning("⚠️ 'Técnico' column not found in Excel file. Available columns: " + ", ".join(df.columns.tolist()))
                logger.warning("⚠️ Continuing without filter...")

            # Step 2: Create/replace staging table
            staging_table = db_manager.create_staging_table(table_name, column_types)
            if not staging_table:
                return False

            # Step 3: Insert filtered data into staging table
            values = []
            for _, row in df.iterrows():
                row_values = []
                for col_name, val in row.items():
                    if pd.isna(val):
                        row_values.append(None)
                    elif col_name == 'fecha_integracion':
                        row_values.append(val if isinstance(val, pd.Timestamp) else pd.Timestamp(val))
                    else:
                        row_values.append(str(val))
                values.append(tuple(row_values))

            if not db_manager.insert_data_batch(staging_table, values, sql_columns):
                return False

            logger.info(f"✅ Data loaded into staging table: {staging_table}")

            # Step 3: Execute SP to move data from staging to historical table
            sp_name = "sp_insert_from_staging_to_tb_toa_reporte_diario"
            sp_params = {
                'staging_table': staging_table,
                'target_table': table_name
            }

            if not db_manager.execute_stored_procedure(sp_name, sp_params):
                logger.error("❌ Failed to execute stored procedure for data migration")
                return False

            logger.info("✅ Data migrated from staging to historical table via SP")

        else:
            # LEGACY APPROACH: Direct insert (original logic)
            logger.info("📋 Using legacy direct insert approach...")

            # Handle table creation/replacement
            table_exists = db_manager.table_exists(table_name)

            if mode == 'replace':
                logger.info(f"🔄 REPLACE mode: Recreating table {table_name}...")
                if table_exists and not db_manager.drop_table(table_name):
                    return False
                table_exists = False
            else:
                logger.info(f"➕ APPEND mode: {'Table exists' if table_exists else 'Table will be created'}")

            # Create table if needed
            if not table_exists:
                if not db_manager.create_table(table_name, column_types):
                    return False
            else:
                # For existing tables, ensure columns are NVARCHAR(MAX) for unlimited text support
                logger.info("🔧 Ensuring existing table columns support unlimited text...")
                text_columns = [col for col in sql_columns if col != 'fecha_integracion']
                if not db_manager.ensure_table_columns_are_nvarchar_max(table_name, text_columns):
                    logger.warning("⚠️ Could not update all columns to NVARCHAR(MAX), but continuing...")

            values = []
            for _, row in df.iterrows():
                row_values = []
                for col_name, val in row.items():
                    if pd.isna(val):
                        row_values.append(None)
                    elif col_name == 'fecha_integracion':
                        row_values.append(val if isinstance(val, pd.Timestamp) else pd.Timestamp(val))
                    else:
                        row_values.append(str(val))
                values.append(tuple(row_values))

            # Insert data
            if not db_manager.insert_data_batch(table_name, values, sql_columns):
                return False

        logger.info("✅ Excel upload completed successfully!")
        logger.info(f"📊 Total records processed: {len(df)}")
        logger.info(f"🕒 Integration timestamp: {current_timestamp}")

        # Create view for latest data if requested
        if create_latest_view:
            logger.info("📊 Creating view for latest data...")
            if db_manager.create_view_latest_data(table_name):
                logger.info("✅ Latest data view created successfully")
            else:
                logger.warning("⚠️ Could not create latest data view, but upload was successful")

        return True

    except Exception as e:
        logger.error(f"❌ {ERROR_MESSAGES['upload_failed']}: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

    finally:
        db_manager.disconnect()

def create_and_query_latest_data_view(table_name: str = 'tb_toa_reporte_diario', view_name: str = None) -> List[Tuple]:
    """
    Create a view for latest data and return the results.

    Args:
        table_name: Source table name
        view_name: Name for the view (optional)

    Returns:
        List of tuples with the latest data
    """
    db_manager = DatabaseManager()

    try:
        if not db_manager.connect():
            return []

        # Create the view
        if not db_manager.create_view_latest_data(table_name, view_name):
            return []

        # Query the view
        if view_name is None:
            view_name = f"{table_name}_latest"

        results = db_manager.query_view(view_name)
        logger.info(f"📊 Retrieved {len(results)} records from latest data view")

        return results

    except Exception as e:
        logger.error(f"❌ Error in create_and_query_latest_data_view: {str(e)}")
        return []

    finally:
        db_manager.disconnect()

def diagnose_column_mismatch(excel_file_path: str, table_name: str = 'tb_toa_reporte_diario') -> Dict[str, List[str]]:
    """
    Diagnose column mismatches between Excel file and SQL Server table.

    Args:
        excel_file_path: Path to the Excel file
        table_name: SQL Server table name

    Returns:
        Dictionary with column analysis results
    """
    db_manager = DatabaseManager()
    results = {
        'excel_columns': [],
        'table_columns': [],
        'missing_in_table': [],
        'extra_in_excel': [],
        'matched_columns': []
    }

    try:
        # Read Excel columns
        logger.info(f"📖 Reading Excel file: {excel_file_path}")
        df = pd.read_excel(excel_file_path, engine='openpyxl')
        excel_columns = list(df.columns)
        results['excel_columns'] = excel_columns
        logger.info(f"📊 Excel has {len(excel_columns)} columns")
        logger.info(f"📋 First 5 Excel columns: {excel_columns[:5]}")

        # Get table columns
        if db_manager.connect():
            table_columns = db_manager.get_table_columns(table_name)
            results['table_columns'] = table_columns
            logger.info(f"🗄️ Table has {len(table_columns)} columns")
            logger.info(f"📋 First 5 table columns: {table_columns[:5]}")

            # Find mismatches
            excel_set = set(excel_columns)
            table_set = set(table_columns)

            results['missing_in_table'] = list(excel_set - table_set)
            results['extra_in_excel'] = list(table_set - excel_set)
            results['matched_columns'] = list(excel_set & table_set)

            logger.info(f"✅ Matched columns: {len(results['matched_columns'])}")
            logger.info(f"❌ Missing in table: {len(results['missing_in_table'])}")
            logger.info(f"⚠️ Extra in Excel: {len(results['extra_in_excel'])}")

            if results['missing_in_table']:
                logger.warning(f"🚨 Columns in Excel but not in table: {results['missing_in_table']}")

            if results['extra_in_excel']:
                logger.warning(f"🚨 Columns in table but not in Excel: {results['extra_in_excel']}")

        return results

    except Exception as e:
        logger.error(f"❌ Error in column diagnosis: {str(e)}")
        return results

    finally:
        db_manager.disconnect()
