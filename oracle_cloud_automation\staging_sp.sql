-- Stored Procedure for migrating data from staging table to historical table
-- This <PERSON> handles the INSERT INTO operation with error handling and logging

USE telqway;
GO

IF OBJECT_ID('sp_insert_from_staging_to_tb_toa_reporte_diario', 'P') IS NOT NULL
    DROP PROCEDURE sp_insert_from_staging_to_tb_toa_reporte_diario;
GO

CREATE PROCEDURE sp_insert_from_staging_to_tb_toa_reporte_diario
    @staging_table NVARCHAR(255),
    @target_table NVARCHAR(255) = 'tb_toa_reporte_diario'
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @sql NVARCHAR(MAX);
    DECLARE @error_message NVARCHAR(4000);
    DECLARE @records_inserted INT = 0;
    DECLARE @start_time DATETIME = GETDATE();

    BEGIN TRY
        -- Log start of operation
        INSERT INTO audit_log (operation, table_name, start_time, status)
        VALUES ('STAGING_TO_HISTORICAL_MIGRATION', @target_table, @start_time, 'STARTED');

        -- Check if staging table exists
        IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @staging_table)
        BEGIN
            SET @error_message = 'Staging table ' + @staging_table + ' does not exist';
            RAISERROR(@error_message, 16, 1);
            RETURN;
        END

        -- Check if target table exists, create if not
        IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @target_table)
        BEGIN
            -- Get structure from staging table
            SET @sql = '
                SELECT TOP 0 * INTO ' + @target_table + ' FROM ' + @staging_table + ';
                ALTER TABLE ' + @target_table + ' ADD CONSTRAINT PK_' + @target_table + '_id PRIMARY KEY (fecha_integracion);
            ';
            EXEC sp_executesql @sql;
            PRINT 'Created target table: ' + @target_table;
        END

        -- Insert data from staging to target table
        -- Using explicit column list to handle special characters in column names
        -- Avoid duplicates based on ID de actividad + fecha_integracion
        SET @sql = '
            INSERT INTO ' + @target_table + ' (
                [Técnico], [Orden de Trabajo], [Tipo de Actividad], [Subtipo],
                [Tipo de Orden], [Franja], [Inicio], [Fin], [Cliente],
                [Dirección], [Ciudad], [Número Cliente], [Celular],
                [Clase de Vivienda], [Código Ciudad], [Código Localidad],
                [Código Territorio], [Código Zona], [Comentarios de la actividad],
                [Coord X], [Coord Y], [Fecha], [Ventana de Entrega],
                [Descripción de la actividad], [Duración], [email],
                [Estado de la actividad], [Fecha Certificada], [Flag Estado Aprovisión],
                [Flag Fallas Masivas], [Flag Materiales], [Notas Materiales],
                [Flag Niveles], [Grupo Socioeconomico], [Indicador Capacidad],
                [Area derivación], [Nodo], [Nombre Completo Persona],
                [Nro Orden], [Nro Solicitud de Servicio], [Teléfono],
                [Prioridad], [Razón de Cancelación], [Inicio - Fin],
                [Estado], [Provincia], [Subnodo], [Territorio],
                [Tipo de Vivienda], [Tiempo de viaje], [Usuario Creador Actividad],
                [Zona de trabajo], [Código postal], [Zona], [Ventana de Servicio],
                [Código de Cierre], [Notas de Cierre (máx 500 caract. , sin caract especial "&")],
                [ID de actividad], [Rut o Bucket], [Pasos],
                [Hora de reserva de actividad], [Flag Corte de Acometida],
                [Flag Retiro de Materiales], [Flag Televisión Análoga],
                [Flag que indica si hay Internet], [Flag que indica si hay Telefonía],
                [Flag que indica si hay Televisión], [Equipos sin retirar],
                [Inicio SLA], [Activity status change by], [MAC del MTA],
                [Tipo Red], [Código GIS], [Categorización del número de derivaciones],
                [Agenda confirmada], [Criterios de priorización],
                [Cantidad de derivaciones NO terreno], [Cantidad de derivaciones terreno],
                [Bucket Original], [Tipo de work skill Siebel], [Items Orden],
                [Notas de Suspensión (máx 500 caract. , sin caract especial "&")],
                [Usuario que suspende], [Hora de asignación de actividad],
                [Cierre Suspender], [Marca], [Tipo red producto], [Access ID],
                [Aptitud laboral], [Flag Resultado DROP], [Flag Resultado NAP],
                [Tipo NAP], [QR DROP], [Nap ID], [Nap Ubicación],
                [Puerto NAP], [EOS asociado], [Flag Cambio Pelo],
                [Respuesta Intervencion Cambio Pelo], [Flag Consulta Vecino],
                [Notas Consulta Vecino], [Uso interno plugin cambio de pelo (no modificar)],
                [Intervención neutra], [fecha_integracion]
            )
            SELECT
                s.[Técnico], s.[Orden de Trabajo], s.[Tipo de Actividad], s.[Subtipo],
                s.[Tipo de Orden], s.[Franja], s.[Inicio], s.[Fin], s.[Cliente],
                s.[Dirección], s.[Ciudad], s.[Número Cliente], s.[Celular],
                s.[Clase de Vivienda], s.[Código Ciudad], s.[Código Localidad],
                s.[Código Territorio], s.[Código Zona], s.[Comentarios de la actividad],
                s.[Coord X], s.[Coord Y], s.[Fecha], s.[Ventana de Entrega],
                s.[Descripción de la actividad], s.[Duración], s.[email],
                s.[Estado de la actividad], s.[Fecha Certificada], s.[Flag Estado Aprovisión],
                s.[Flag Fallas Masivas], s.[Flag Materiales], s.[Notas Materiales],
                s.[Flag Niveles], s.[Grupo Socioeconomico], s.[Indicador Capacidad],
                s.[Area derivación], s.[Nodo], s.[Nombre Completo Persona],
                s.[Nro Orden], s.[Nro Solicitud de Servicio], s.[Teléfono],
                s.[Prioridad], s.[Razón de Cancelación], s.[Inicio - Fin],
                s.[Estado], s.[Provincia], s.[Subnodo], s.[Territorio],
                s.[Tipo de Vivienda], s.[Tiempo de viaje], s.[Usuario Creador Actividad],
                s.[Zona de trabajo], s.[Código postal], s.[Zona], s.[Ventana de Servicio],
                s.[Código de Cierre], s.[Notas de Cierre (máx 500 caract. , sin caract especial "&")],
                s.[ID de actividad], s.[Rut o Bucket], s.[Pasos],
                s.[Hora de reserva de actividad], s.[Flag Corte de Acometida],
                s.[Flag Retiro de Materiales], s.[Flag Televisión Análoga],
                s.[Flag que indica si hay Internet], s.[Flag que indica si hay Telefonía],
                s.[Flag que indica si hay Televisión], s.[Equipos sin retirar],
                s.[Inicio SLA], s.[Activity status change by], s.[MAC del MTA],
                s.[Tipo Red], s.[Código GIS], s.[Categorización del número de derivaciones],
                s.[Agenda confirmada], s.[Criterios de priorización],
                s.[Cantidad de derivaciones NO terreno], s.[Cantidad de derivaciones terreno],
                s.[Bucket Original], s.[Tipo de work skill Siebel], s.[Items Orden],
                s.[Notas de Suspensión (máx 500 caract. , sin caract especial "&")],
                s.[Usuario que suspende], s.[Hora de asignación de actividad],
                s.[Cierre Suspender], s.[Marca], s.[Tipo red producto], s.[Access ID],
                s.[Aptitud laboral], s.[Flag Resultado DROP], s.[Flag Resultado NAP],
                s.[Tipo NAP], s.[QR DROP], s.[Nap ID], s.[Nap Ubicación],
                s.[Puerto NAP], s.[EOS asociado], s.[Flag Cambio Pelo],
                s.[Respuesta Intervencion Cambio Pelo], s.[Flag Consulta Vecino],
                s.[Notas Consulta Vecino], s.[Uso interno plugin cambio de pelo (no modificar)],
                s.[Intervención neutra], s.[fecha_integracion]
            FROM ' + @staging_table + ' s
            WHERE NOT EXISTS (
                SELECT 1 FROM ' + @target_table + ' t
                WHERE t.fecha_integracion = s.fecha_integracion
                AND t.[ID de actividad] = s.[ID de actividad]
            );
        ';

        EXEC sp_executesql @sql;
        SET @records_inserted = @@ROWCOUNT;

        -- Log successful completion
        INSERT INTO audit_log (operation, table_name, start_time, end_time, records_processed, status)
        VALUES ('STAGING_TO_HISTORICAL_MIGRATION', @target_table, @start_time, GETDATE(), @records_inserted, 'COMPLETED');

        PRINT 'Successfully migrated ' + CAST(@records_inserted AS NVARCHAR(10)) + ' records from ' + @staging_table + ' to ' + @target_table;

        -- Clean up staging table (optional - can be kept for audit purposes)
        -- EXEC('DROP TABLE ' + @staging_table);

    END TRY
    BEGIN CATCH
        -- Log error
        SET @error_message = ERROR_MESSAGE();
        INSERT INTO audit_log (operation, table_name, start_time, end_time, error_message, status)
        VALUES ('STAGING_TO_HISTORICAL_MIGRATION', @target_table, @start_time, GETDATE(), @error_message, 'FAILED');

        -- Re-throw error
        THROW;
    END CATCH
END
GO

-- Create audit log table if it doesn't exist
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'audit_log')
BEGIN
    CREATE TABLE audit_log (
        id INT IDENTITY(1,1) PRIMARY KEY,
        operation NVARCHAR(255),
        table_name NVARCHAR(255),
        start_time DATETIME,
        end_time DATETIME NULL,
        records_processed INT NULL,
        error_message NVARCHAR(MAX) NULL,
        status NVARCHAR(50)
    );
END
GO

PRINT 'Stored Procedure sp_insert_from_staging_to_tb_toa_reporte_diario created successfully';
PRINT 'Audit log table created/verified';
GO