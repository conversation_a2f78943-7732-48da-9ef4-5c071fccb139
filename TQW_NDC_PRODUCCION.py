
import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, <PERSON><PERSON>n, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import os 
import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

from sqlalchemy import text
import sqlalchemy


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@181.212.32.10/master?driver=ODBC+Driver+17+for+SQL+Server')
Session = sessionmaker(bind=engine)
session = Session()


session.execute("EXEC SP_CREATE_NDC_PRODUCCION '202406'")
session.commit()

session.execute("EXEC SP_TQW_COMISION_2023 '202406'")
session.commit()

# Crear un data frame a partir de una consulta a SQL Server

Data = pd.read_sql_query("SELECT * from ProduccionNDC WHERE FORMAT(mes_contable,'yyyyMM') >= '202310' ", engine)
Data2 = pd.read_sql_query("SELECT * FROM TB_TQW_COMISION_2023", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('tb_paso_pyNdc', engineMYsql, if_exists='replace',index=False)
Data2.to_sql('tb_paso_KPI2023', engineMYsql, if_exists='replace',index=False)
