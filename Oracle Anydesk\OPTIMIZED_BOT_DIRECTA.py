import time
import os
import glob
import psutil
import win32gui
import win32com.client
import pyautogui
import cv2
import numpy as np
import pandas as pd
import traceback
import datetime
import sys
from sqlalchemy import create_engine, text, types, Table, Column, Integer, String, DateTime, MetaData
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from aux_mov import DatabaseManager

# Definición de la tabla de log para seguimiento del proceso
Base = declarative_base()

class ProcessLog(Base):
    __tablename__ = 'TB_ORACLE_BOT_LOG'

    id = Column(Integer, primary_key=True, autoincrement=True)
    paso = Column(String(100), nullable=False)
    estado = Column(String(50), nullable=False)  # EXITO, ERROR, ADVERTENCIA
    mensaje = Column(String(500), nullable=True)
    timestamp = Column(DateTime, default=datetime.datetime.now)
    screenshot_path = Column(String(255), nullable=True)
    ciudad = Column(String(50), nullable=True)
    imagen_buscada = Column(String(255), nullable=True)

class OracleBot:
    def __init__(self, guardar_screenshots=True):
        # Configuración de la base de datos
        self.engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')
        self.Session = sessionmaker(bind=self.engine)

        # Crear tabla de log si no existe
        self.crear_tabla_log()

        # Carpeta para guardar capturas de pantalla
        self.screenshots_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'screenshots')
        if guardar_screenshots and not os.path.exists(self.screenshots_folder):
            os.makedirs(self.screenshots_folder)
        self.guardar_screenshots = guardar_screenshots

        # Variables de control del proceso
        self.paso_actual = None
        self.ciudad_actual = None

        # Configuración de tipos de datos para la tabla
        self.custom_dtypes = {
            'Serial': types.String(50),
            'Item': types.String(50),
            'Org': types.String(50),
            'Revision': types.String(50),
            'Subinventory': types.String(50),
            'Locator': types.String(50),
            'Operation': types.String(50),
            'Job': types.String(50),
            'Step': types.String(50),
            'Lot': types.String(50),
            'State': types.String(50),
            'Status': types.String(50),
            'Receipt Date': types.String(50),
            'Ship Date': types.String(50),
            'Supplier Name': types.String(50),
            'Supplier Lot': types.String(50),
            'Supplier Serial': types.String(50),
            'Unit Number': types.String(50),
            'Attributes': types.String(50),
            '[  ]': types.String(50),
            'Unnamed: 20': types.String(50)
        }
        # Lista de imágenes que pueden no encontrarse
        self.allowed_missing_images = [
            "C:\\Users\\<USER>\\Desktop\\SecurityBox.png",
            "C:\\Users\\<USER>\\Desktop\\SecurityRun.png",
            "C:\\Users\\<USER>\\Desktop\\UsuarioChrome.png",
            "C:\\Users\\<USER>\\Desktop\\CloseRestore.png",
            "C:\\Users\\<USER>\\Desktop\\edge_search.png",
            "C:\\Users\\<USER>\\Desktop\\edge_conservar.png",
            "C:\\Users\\<USER>\\Desktop\\G40Stgo_directo.png",
            "C:\\Users\\<USER>\\Desktop\\G40Stgo_directo2.png",
            "C:\\Users\\<USER>\\Desktop\\D31Vina_directa2.png",
            "C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png",
            "C:\\Users\\<USER>\\Desktop\\J63Conce_directa.png",
            "C:\\Users\\<USER>\\Desktop\\J63Conce_directa2.png",
            "C:\\Users\\<USER>\\Desktop\\A90Anto_directa.png",
            "C:\\Users\\<USER>\\Desktop\\A90Anto_directa2.png",
            "C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta.png",
            "C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta2.png",
            "C:\\Users\\<USER>\\Desktop\\UsuarioOracleJcepeda.png"
        ]

        # Datos de ciudad con sus respectivas imágenes
        self.ciudades = [
            {
                "nombre": "Santiago",
                "imagen_menu": "C:\\Users\\<USER>\\Desktop\\STGO_directa.png",
                "imagen_directa": ["C:\\Users\\<USER>\\Desktop\\G40Stgo_directo.png", "C:\\Users\\<USER>\\Desktop\\G40Stgo_directo2.png"]
            },
            {
                "nombre": "Viña",
                "imagen_menu": "C:\\Users\\<USER>\\Desktop\\Vina.png",
                "imagen_directa": ["C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png", "C:\\Users\\<USER>\\Desktop\\D31Vina_directa2.png"]
            },
            {
                "nombre": "Concepción",
                "imagen_menu": "C:\\Users\\<USER>\\Desktop\\Conce.png",
                "imagen_directa": ["C:\\Users\\<USER>\\Desktop\\J63Conce_directa.png", "C:\\Users\\<USER>\\Desktop\\J63Conce_directa2.png"],
                "scroll": True
            },
            {
                "nombre": "Antofagasta",
                "imagen_menu": "C:\\Users\\<USER>\\Desktop\\AntoDirecta.png",
                "imagen_directa": ["C:\\Users\\<USER>\\Desktop\\A90Anto_directa.png", "C:\\Users\\<USER>\\Desktop\\A90Anto_directa2.png"]
            },
            {
                "nombre": "Temuco",
                "imagen_menu": "C:\\Users\\<USER>\\Desktop\\TemucoDirecta.png",
                "imagen_directa": ["C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta.png", "C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta2.png"],
                "scroll": True
            }
        ]

    def crear_tabla_log(self):
        """Crea la tabla de log si no existe"""
        try:
            Base.metadata.create_all(self.engine)
            print("Tabla de log lista para su uso")
        except Exception as e:
            print(f"Error al crear tabla de log: {str(e)}")

    def registrar_log(self, paso, estado, mensaje=None, imagen_buscada=None, guardar_pantallazo=False):
        """Registra un paso en la consola y solo guarda en la base de datos cuando hay un error"""
        try:
            self.paso_actual = paso
            screenshot_path = None

            # Guardar captura de pantalla si es necesario y es un error
            if estado == "ERROR" and guardar_pantallazo and self.guardar_screenshots:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join(self.screenshots_folder, f"{paso.replace(' ', '_')}_{timestamp}.png")
                pyautogui.screenshot().save(screenshot_path)

            # Imprimir en consola siempre
            print(f"[{estado}] {paso} - {mensaje if mensaje else ''}")

            # Solo guardar en la base de datos si es un error
            if estado == "ERROR":
                session = self.Session()
                log_entry = ProcessLog(
                    paso=paso,
                    estado=estado,
                    mensaje=mensaje,
                    ciudad=self.ciudad_actual,
                    imagen_buscada=imagen_buscada,
                    screenshot_path=screenshot_path
                )
                session.add(log_entry)
                session.commit()
                session.close()
                print(f"[ERROR-DB] {paso} - Error registrado en base de datos")

        except Exception as e:
            print(f"Error al registrar log: {str(e)}")

    def obtener_ultimo_paso_exitoso(self):
        """Obtiene el último paso con error del proceso"""
        try:
            session = self.Session()
            ultimo_error = session.query(ProcessLog).order_by(ProcessLog.id.desc()).first()
            session.close()
            return ultimo_error.paso if ultimo_error else None
        except Exception as e:
            print(f"Error al obtener último paso con error: {str(e)}")
            return None

    def cerrar_proceso(self, nombre_proceso):
        """Cierra procesos por nombre"""
        procesos_cerrados = 0
        for proc in psutil.process_iter():
            try:
                if proc.name().lower() == nombre_proceso.lower():
                    proc.terminate()
                    procesos_cerrados += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        if procesos_cerrados > 0:
            print(f"Se cerraron {procesos_cerrados} procesos de {nombre_proceso}.")
        else:
            print(f"No se encontraron procesos de {nombre_proceso}.")

    def cerrar_ventana_edge(self):
        """Cierra las ventanas de Microsoft Edge"""
        def enum_windows_callback(hwnd, results):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                print(f"Ventana encontrada - Título: '{title}', Clase: '{class_name}'")
                if "Edge" in title or "Microsoft" in title:
                    results.append((hwnd, title, class_name))

        edge_windows = []
        win32gui.EnumWindows(enum_windows_callback, edge_windows)

        if edge_windows:
            print("Ventanas de Edge encontradas:")
            for hwnd, title, class_name in edge_windows:
                print(f"  Handle: {hwnd}, Título: '{title}', Clase: '{class_name}'")

            # Intentar cerrar la primera ventana encontrada
            edge_hwnd, _, _ = edge_windows[0]
            shell = win32com.client.Dispatch("WScript.Shell")
            win32gui.SetForegroundWindow(edge_hwnd)
            shell.SendKeys("%{F4}")
            print(f"Intento de cerrar ventana con handle {edge_hwnd}")
        else:
            print("No se encontraron ventanas que parezcan ser Edge.")

    def buscar_imagen_en_pantalla(self, imagen_a_buscar):
        """Busca una imagen en la pantalla y devuelve las coordenadas del centro"""
        try:
            # Registrar intento de búsqueda
            self.registrar_log(f"Buscando imagen", "INFO", f"Buscando: {os.path.basename(imagen_a_buscar)}", imagen_a_buscar)

            # Toma una captura de pantalla
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

            # Lee la imagen a buscar y conviértela a escala de grises
            template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

            w, h = template.shape[::-1]

            # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
            result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
            loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

            # Si encontró la imagen, devuelve las coordenadas del centro
            for pt in zip(*loc[::-1]):
                centro_x = pt[0] + w // 2
                centro_y = pt[1] + h // 2
                self.registrar_log(f"Imagen encontrada", "EXITO", f"Encontrada: {os.path.basename(imagen_a_buscar)}", imagen_a_buscar)
                return (centro_x, centro_y)

            # Si no encontró la imagen, verifica si es una de las excepciones
            if imagen_a_buscar in self.allowed_missing_images:
                self.registrar_log(f"Imagen no encontrada (permitido)", "ADVERTENCIA",
                                  f"No se encontró: {os.path.basename(imagen_a_buscar)}, pero se permite continuar", imagen_a_buscar)
                return None

            # Si llega aquí, es porque no encontró la imagen y no está en la lista de permitidos
            self.registrar_log(f"Imagen crítica no encontrada", "ERROR",
                              f"No se encontró imagen crítica: {os.path.basename(imagen_a_buscar)}",
                              imagen_a_buscar, guardar_pantallazo=True)

            self.cerrar_proceso("jp2launcher.exe")
            self.cerrar_ventana_edge()
            return "IMAGE_NOT_FOUND"

        except Exception as e:
            # Registrar el error
            error_msg = f"Error buscando imagen: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log(f"Error en búsqueda", "ERROR", error_msg, imagen_a_buscar, guardar_pantallazo=True)

            # Intenta cerrar Edge si ocurre un error
            try:
                coordenadas = self.buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge.png')
                if coordenadas:
                    pyautogui.rightClick(coordenadas[0], coordenadas[1])

                time.sleep(3)
                coordenadas = self.buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CloseWindows.png')
                if coordenadas:
                    pyautogui.click(coordenadas[0], coordenadas[1])
            except:
                pass  # Si falla el intento de cierre, simplemente continuamos

            return "IMAGE_NOT_FOUND"

    def buscar_y_click(self, imagen_path, doble_click=False, espera=3, paso_desc=None):
        """Busca una imagen y hace clic en ella"""
        if paso_desc is None:
            paso_desc = f"Click en {os.path.basename(imagen_path)}"

        # Registrar que vamos a realizar esta acción
        self.registrar_log(paso_desc, "INFO", f"Esperando {espera}s antes de buscar")

        time.sleep(espera)
        coordenadas = self.buscar_imagen_en_pantalla(imagen_path)

        if coordenadas and coordenadas != "IMAGE_NOT_FOUND":
            accion = "doble click" if doble_click else "click"
            self.registrar_log(paso_desc, "INFO", f"Realizando {accion} en ({coordenadas[0]}, {coordenadas[1]})")

            if doble_click:
                pyautogui.doubleClick(coordenadas[0], coordenadas[1])
            else:
                pyautogui.click(coordenadas[0], coordenadas[1])

            self.registrar_log(paso_desc, "EXITO", f"{accion.capitalize()} realizado correctamente")
            return True

        self.registrar_log(paso_desc, "ERROR", "No se pudo realizar la acción, imagen no encontrada", imagen_path, guardar_pantallazo=True)
        return False

    def buscar_alternativas(self, imagenes, doble_click=False, espera=3, paso_desc=None):
        """Busca entre múltiples imágenes alternativas y hace clic en la primera que encuentra"""
        if paso_desc is None:
            if isinstance(imagenes, str):
                paso_desc = f"Click en {os.path.basename(imagenes)}"
            else:
                paso_desc = f"Click en alternativas ({len(imagenes)} opciones)"

        self.registrar_log(paso_desc, "INFO", "Iniciando búsqueda de alternativas")

        if isinstance(imagenes, str):
            return self.buscar_y_click(imagenes, doble_click, espera, paso_desc)

        for i, img in enumerate(imagenes):
            self.registrar_log(paso_desc, "INFO", f"Intentando alternativa {i+1}/{len(imagenes)}: {os.path.basename(img)}")
            if self.buscar_y_click(img, doble_click, 1, f"{paso_desc} - Alternativa {i+1}"):  # Espera reducida entre alternativas
                return True

        self.registrar_log(paso_desc, "ERROR", "Ninguna alternativa encontrada", guardar_pantallazo=True)
        return False

    def iniciar_edge_y_navegar(self):
        """Inicia Edge y navega a la URL de Oracle"""
        # Abrir Edge
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge.png', espera=10)

        # Buscar barra de búsqueda (intentar dos versiones)
        time.sleep(10)
        if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_search.png'):
            self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_search2.png')

        # Escribir URL
        time.sleep(7)
        oracle_url = "ebs.andes.vtr.cl:8000/OA_HTML/AppsLocalLogin.jsp?requestUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FOA.jsp%3Fpage%3D%2Foracle%2Fapps%2Ffnd%2Fframework%2Fnavigate%2Fwebui%2FNewHomePG%26homePage%3DY%26OAPB%3DFWK_HOMEPAGE_BRAND%26oapc%3D2%26transactionid%3D146981584%26oas%3DCfzXvWhkd4wc5PbN_4wdyQ..&cancelUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FAppsLogin&langCode=US"
        pyautogui.write(oracle_url)
        time.sleep(8)
        pyautogui.press('enter')

    def iniciar_sesion(self):
        """Inicia sesión en Oracle"""
        time.sleep(8)
        # Usuario

        pyautogui.write("GGUERRERO-EXT")
        time.sleep(2)
        pyautogui.press('tab')
        # Contraseña
        time.sleep(2)
        pyautogui.write("Gerardo-2025")
        time.sleep(10)

        # Botón de login
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\LoginOracle_edge.png', espera=10)

    def navegar_menu_oracle(self):
        """Navega por los menús principales de Oracle"""
        # Navegación por menús principales
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel1.png', espera=7)
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel2.png', espera=6)
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel3.png', espera=5)

        # Botones adicionales
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_conservar.png', espera=5)
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_abrir_archivo.png', espera=5)

        # Configuración de seguridad
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\SecurityBox.png', espera=15)
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\SecurityRun.png', espera=14)
        self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\CierreX_azul.png', espera=14)

    def procesar_ciudad(self, ciudad):
        """Procesa los datos para una ciudad específica"""
        self.ciudad_actual = ciudad['nombre']
        self.registrar_log(f"Inicio procesamiento ciudad", "INFO", f"Iniciando procesamiento de {ciudad['nombre']}")

        try:
            # Seleccionar desde el sombrero
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\Sombrero.png', espera=3,
                                      paso_desc=f"[{ciudad['nombre']}] Seleccionar menú sombrero"):
                raise Exception("No se pudo encontrar el menú sombrero")

            # Seleccionar ciudad
            if not self.buscar_y_click(ciudad['imagen_menu'], doble_click=True, espera=3,
                                      paso_desc=f"[{ciudad['nombre']}] Seleccionar ciudad en menú"):
                raise Exception(f"No se pudo encontrar la opción de menú para {ciudad['nombre']}")

            # Navegar hasta consulta de transacción
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png', doble_click=True, espera=3,
                                       paso_desc=f"[{ciudad['nombre']}] Abrir Consulta Transacción"):
                raise Exception("No se pudo encontrar el menú Consulta Transacción")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png', doble_click=True, espera=3,
                                       paso_desc=f"[{ciudad['nombre']}] Abrir Consulta Transacción Series"):
                raise Exception("No se pudo encontrar el menú Consulta Transacción Series")

            # Hacer scroll si es necesario
            if ciudad.get('scroll', False):
                self.registrar_log(f"[{ciudad['nombre']}] Scroll", "INFO", "Realizando scroll hacia abajo")
                time.sleep(3)
                for i in range(3):
                    pyautogui.scroll(-1200)
                    time.sleep(0.5)
                    self.registrar_log(f"[{ciudad['nombre']}] Scroll {i+1}/3", "EXITO")

            # Buscar y hacer clic en la imagen específica de la ciudad
            time.sleep(3)
            if not self.buscar_alternativas(ciudad['imagen_directa'], doble_click=True,
                                           paso_desc=f"[{ciudad['nombre']}] Seleccionar opción específica"):
                raise Exception(f"No se pudo encontrar la opción específica para {ciudad['nombre']}")

            # Acciones comunes para todas las ciudades
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\Find.png', espera=5,
                                      paso_desc=f"[{ciudad['nombre']}] Click en Find"):
                raise Exception("No se pudo encontrar el botón Find")

            # Esperar a que carguen los resultados
            self.registrar_log(f"[{ciudad['nombre']}] Espera de resultados", "INFO", "Esperando 24s a que carguen los resultados")
            time.sleep(24)

            # Exportar datos
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\File.png', espera=12,
                                      paso_desc=f"[{ciudad['nombre']}] Click en File"):
                raise Exception("No se pudo encontrar el menú File")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\Export.png', espera=12,
                                      paso_desc=f"[{ciudad['nombre']}] Click en Export"):
                raise Exception("No se pudo encontrar la opción Export")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png', espera=12,
                                      paso_desc=f"[{ciudad['nombre']}] Click en Continue/End"):
                raise Exception("No se pudo encontrar el botón Continue/End")

            # Tiempo para que se complete la exportación
            tiempo_espera = 180 if ciudad['nombre'] in ["Santiago", "Viña"] else 80
            self.registrar_log(f"[{ciudad['nombre']}] Espera exportación", "INFO",
                              f"Esperando {tiempo_espera}s a que se complete la exportación")
            time.sleep(tiempo_espera)

            # Procesar el archivo exportado
            self.procesar_archivo_exportado(ciudad['nombre'])

            # Cerrar ventana actual
            self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\CierreX.png', espera=3,
                              paso_desc=f"[{ciudad['nombre']}] Cerrar ventana")

            self.registrar_log(f"Fin procesamiento ciudad", "EXITO", f"Completado procesamiento de {ciudad['nombre']}")

        except Exception as e:
            error_msg = f"Error al procesar {ciudad['nombre']}: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log(f"Error en procesamiento ciudad", "ERROR", error_msg, guardar_pantallazo=True)
            raise

    def procesar_archivo_exportado(self, ciudad):
        """Procesa el archivo CSV exportado"""
        try:
            # Registrar inicio del procesamiento
            self.registrar_log(f"[{ciudad}] Procesamiento archivo", "INFO", "Buscando archivo exportado")

            # Encontrar el archivo más reciente descargado
            list_of_files = glob.glob('C:/Users/<USER>/Downloads/*')
            latest_file = max(list_of_files, key=os.path.getctime)
            self.registrar_log(f"[{ciudad}] Archivo encontrado", "INFO", f"Archivo: {os.path.basename(latest_file)}")

            # Leer el archivo CSV
            try:
                df = pd.read_csv(latest_file, sep='\t')
                self.registrar_log(f"[{ciudad}] Lectura archivo", "EXITO", f"CSV leído correctamente: {len(df)} filas")
            except Exception as e:
                error_msg = f"Error al leer CSV: {str(e)}\n{traceback.format_exc()}"
                self.registrar_log(f"[{ciudad}] Error lectura", "ERROR", error_msg)
                raise

            # Cargar los datos en la base de datos
            try:
                df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=self.engine, if_exists='append', index=False, dtype=self.custom_dtypes)
                self.registrar_log(f"[{ciudad}] Carga en BD", "EXITO", f"Datos cargados: {len(df)} registros")
            except Exception as e:
                error_msg = f"Error al cargar datos en SQL Server: {str(e)}\n{traceback.format_exc()}"
                self.registrar_log(f"[{ciudad}] Error SQL", "ERROR", error_msg)
                raise

            time.sleep(4)

        except Exception as e:
            error_general = f"Error general en procesamiento de archivo: {str(e)}"
            self.registrar_log(f"[{ciudad}] Error general", "ERROR", error_general)
            raise

    def truncar_tabla(self):
        """Trunca la tabla de destino antes de iniciar el proceso"""
        try:
            self.registrar_log("Truncar tabla", "INFO", "Truncando tabla TB_PASO_0_ORACLE_DIRECTAX")
            session = self.Session()
            session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_DIRECTAX"))
            session.commit()
            session.close()
            self.registrar_log("Truncar tabla", "EXITO", "Tabla truncada correctamente")
        except Exception as e:
            error_msg = f"Error al truncar tabla: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Truncar tabla", "ERROR", error_msg)
            raise

    def ejecutar_sincronizacion(self):
        """Ejecuta el proceso de sincronización final"""
        try:
            self.registrar_log("Sincronización de datos", "INFO", "Iniciando sincronización final de datos")
            db_manager = DatabaseManager()
            try:
                db_manager.process_oracle_data()
                self.registrar_log("Sincronización de datos", "EXITO", "Sincronización completada exitosamente")
            finally:
                db_manager.cleanup()
        except Exception as e:
            error_msg = f"Error en sincronización: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Sincronización de datos", "ERROR", error_msg)
            raise

    def iniciar_edge_y_navegar(self):
        """Inicia Edge y navega a la URL de Oracle"""
        try:
            self.registrar_log("Inicio navegación", "INFO", "Iniciando Edge y navegando a Oracle")

            # Abrir Edge
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge.png', espera=10, paso_desc="Abrir Edge"):
                raise Exception("No se pudo encontrar el icono de Edge")

            # Buscar barra de búsqueda (intentar dos versiones)
            time.sleep(10)
            self.registrar_log("Buscar barra navegación", "INFO", "Buscando barra de búsqueda de Edge")

            encontrado = False
            if self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_search.png', paso_desc="Click en barra de búsqueda (versión 1)"):
                encontrado = True
            elif self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_search2.png', paso_desc="Click en barra de búsqueda (versión 2)"):
                encontrado = True

            if not encontrado:
                raise Exception("No se pudo encontrar la barra de búsqueda de Edge")

            # Escribir URL
            time.sleep(7)
            oracle_url = "ebs.andes.vtr.cl:8000/OA_HTML/AppsLocalLogin.jsp?requestUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FOA.jsp%3Fpage%3D%2Foracle%2Fapps%2Ffnd%2Fframework%2Fnavigate%2Fwebui%2FNewHomePG%26homePage%3DY%26OAPB%3DFWK_HOMEPAGE_BRAND%26oapc%3D2%26transactionid%3D146981584%26oas%3DCfzXvWhkd4wc5PbN_4wdyQ..&cancelUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FAppsLogin&langCode=US"
            self.registrar_log("Escribir URL", "INFO", "Escribiendo URL de Oracle")
            pyautogui.write(oracle_url)
            self.registrar_log("Escribir URL", "EXITO", "URL escrita correctamente")

            time.sleep(8)
            pyautogui.press('enter')
            self.registrar_log("Navegar a URL", "EXITO", "Navegando a la URL de Oracle")

        except Exception as e:
            error_msg = f"Error al iniciar Edge: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Navegación", "ERROR", error_msg, guardar_pantallazo=True)
            raise

    def iniciar_sesion(self):
        """Inicia sesión en Oracle"""
        try:
            self.registrar_log("Inicio sesión", "INFO", "Iniciando sesión en Oracle")

            time.sleep(8)
            # Usuario
            self.registrar_log("Escribir usuario", "INFO", "Escribiendo usuario")
            pyautogui.write("GGUERRERO-EXT")
            self.registrar_log("Escribir usuario", "EXITO")

            time.sleep(2)
            pyautogui.press('tab')

            # Contraseña
            time.sleep(2)
            self.registrar_log("Escribir contraseña", "INFO", "Escribiendo contraseña")
            # pyautogui.write("smwytq68pa")
            pyautogui.write("Gerardo-2025")
            self.registrar_log("Escribir contraseña", "EXITO")

            time.sleep(10)

            # Botón de login
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\LoginOracle_edge.png', espera=10,
                                     paso_desc="Click en botón de Login"):
                raise Exception("No se pudo encontrar el botón de login")

            self.registrar_log("Inicio sesión", "EXITO", "Sesión iniciada correctamente")

        except Exception as e:
            error_msg = f"Error al iniciar sesión: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Inicio sesión", "ERROR", error_msg, guardar_pantallazo=True)
            raise

    def navegar_menu_oracle(self):
        """Navega por los menús principales de Oracle"""
        try:
            self.registrar_log("Navegación menús", "INFO", "Navegando por menús principales")

            # Navegación por menús principales
            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel1.png', espera=7,
                                      paso_desc="Click en menú nivel 1"):
                raise Exception("No se pudo encontrar el menú nivel 1")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel2.png', espera=6,
                                      paso_desc="Click en menú nivel 2"):
                raise Exception("No se pudo encontrar el menú nivel 2")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\OracleNivel3.png', espera=5,
                                      paso_desc="Click en menú nivel 3"):
                raise Exception("No se pudo encontrar el menú nivel 3")

            # Botones adicionales
            self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_conservar.png', espera=5,
                              paso_desc="Click en botón conservar")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\edge_abrir_archivo.png', espera=5,
                                      paso_desc="Click en botón abrir archivo"):
                raise Exception("No se pudo encontrar el botón abrir archivo")

            # Configuración de seguridad
            self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\SecurityBox.png', espera=15,
                              paso_desc="Click en Security Box")

            self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\SecurityRun.png', espera=14,
                              paso_desc="Click en Security Run")

            if not self.buscar_y_click('C:\\Users\\<USER>\\Desktop\\CierreX_azul.png', espera=14,
                                      paso_desc="Click en botón X azul"):
                raise Exception("No se pudo encontrar el botón X azul")

            self.registrar_log("Navegación menús", "EXITO", "Navegación por menús completada")

        except Exception as e:
            error_msg = f"Error en navegación por menús: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Navegación menús", "ERROR", error_msg, guardar_pantallazo=True)
            raise

    # La función verificar_punto_reinicio ha sido eliminada ya que no se requiere reinicio automático

    def ejecutar_proceso_completo(self):
        """Ejecuta todo el proceso de extracción de datos desde el principio"""
        self.registrar_log("Inicio proceso", "INFO", "Iniciando proceso completo de extracción de datos")

        try:
            # Cerrar cualquier ventana de Edge que pueda estar abierta
            self.cerrar_ventana_edge()

            # Truncar la tabla antes de iniciar
            self.truncar_tabla()

            # Iniciar Edge y navegar a Oracle
            self.iniciar_edge_y_navegar()

            # Iniciar sesión en Oracle
            self.iniciar_sesion()

            # Navegar por los menús principales
            self.navegar_menu_oracle()

            # Procesar todas las ciudades
            ciudades_a_procesar = self.ciudades

            # Procesar cada ciudad
            for ciudad in ciudades_a_procesar:
                try:
                    self.procesar_ciudad(ciudad)
                except Exception as e:
                    self.registrar_log(f"Error en ciudad {ciudad['nombre']}", "ERROR",
                                      f"Error al procesar ciudad {ciudad['nombre']}: {str(e)}", guardar_pantallazo=True)
                    # Continuar con la siguiente ciudad en vez de terminar todo el proceso
                    continue

            # Ejecutar el proceso de sincronización final
            self.ejecutar_sincronizacion()

            self.registrar_log("Fin proceso", "EXITO", "Proceso completado exitosamente")

        except Exception as e:
            error_msg = f"Error global en el proceso: {str(e)}\n{traceback.format_exc()}"
            self.registrar_log("Error proceso", "ERROR", error_msg, guardar_pantallazo=True)
            # Intentar cerrar procesos pendientes
            self.cerrar_proceso("jp2launcher.exe")
            self.cerrar_ventana_edge()
            raise

# Método para obtener reporte del último proceso
    def generar_reporte_estado(self):
        """Genera un reporte de los errores registrados"""
        try:
            session = self.Session()

            # Obtener la fecha del último error registrado
            ultimo_error = session.query(ProcessLog).order_by(ProcessLog.id.desc()).first()
            if not ultimo_error:
                return "No hay registros de errores anteriores."

            fecha_ultimo_error = ultimo_error.timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Contar errores registrados
            total_errores = session.query(ProcessLog).count()

            # Obtener los últimos 10 errores
            ultimos_errores = []
            if total_errores > 0:
                errores_logs = session.query(ProcessLog).order_by(ProcessLog.id.desc()).limit(10).all()
                for error in errores_logs:
                    ultimos_errores.append({
                        "paso": error.paso,
                        "mensaje": error.mensaje[:100] + "..." if error.mensaje and len(error.mensaje) > 100 else error.mensaje,
                        "timestamp": error.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                        "ciudad": error.ciudad
                    })

            # Obtener ciudades con errores
            ciudades_con_errores = set()
            for log in session.query(ProcessLog).all():
                if log.ciudad:
                    ciudades_con_errores.add(log.ciudad)

            session.close()

            # Construir reporte
            reporte = f"\n==== REPORTE DE ERRORES DEL PROCESO ====\n"
            reporte += f"Fecha del último error registrado: {fecha_ultimo_error}\n"
            reporte += f"Total de errores registrados: {total_errores}\n\n"

            if ciudades_con_errores:
                reporte += f"Ciudades con errores: {', '.join(ciudades_con_errores)}\n\n"

            if ultimos_errores:
                reporte += "Últimos errores registrados:\n"
                for i, error in enumerate(ultimos_errores, 1):
                    reporte += f"{i}. {error['paso']} ({error['timestamp']})"
                    reporte += f" en {error['ciudad']}" if error['ciudad'] else ""
                    reporte += f": {error['mensaje']}\n"

            return reporte

        except Exception as e:
            return f"Error al generar reporte: {str(e)}"

    def guardar_reporte_a_archivo(self):
        """Guarda el reporte de estado a un archivo de texto"""
        try:
            reporte = self.generar_reporte_estado()
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            ruta_reporte = os.path.join(os.path.dirname(os.path.abspath(__file__)), f'reporte_bot_{timestamp}.txt')

            with open(ruta_reporte, 'w') as f:
                f.write(reporte)

            print(f"Reporte guardado en: {ruta_reporte}")
            return ruta_reporte

        except Exception as e:
            print(f"Error al guardar reporte: {str(e)}")
            return None


# Ejecutar el bot si se llama como script principal
if __name__ == "__main__":
    try:
        bot = OracleBot(guardar_screenshots=True)

        # Verificar si se quiere generar solo un reporte
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == '--reporte':
            bot.guardar_reporte_a_archivo()
            print(bot.generar_reporte_estado())
        else:
            # Ejecutar proceso completo desde el principio
            bot.ejecutar_proceso_completo()
            # Generar y guardar reporte al finalizar
            bot.guardar_reporte_a_archivo()
    except Exception as e:
        print(f"Error crítico: {str(e)}")
        traceback.print_exc()
