- Solución de problemas de integración con bases de datos:
  1. Eliminación de SQLAlchemy:
    - Cambiamos de un enfoque basado en ORM a conexiones
  directas con los drivers nativos
    - Esto evita problemas de compatibilidad entre versiones      
  de SQLAlchemy
  2. Manejo explícito de conexiones y cursores:
    - MySQL: Usamos mysql.connector.connect() y .cursor()
    - SQL Server: Usamos pyodbc.connect() y .cursor()
    - Esto da control total sobre las transacciones y
  ejecución de consultas
  3. Tratamiento correcto de valores NULL/NaN:
    - Convertimos valores NaN de pandas a None de Python:
  None if pd.isna(x) else x
    - Este paso es crítico para evitar el error "Unknown
  column 'nan' in field list"
  4. Procesamiento por lotes (batch):
    - Usamos inserción por lotes para manejar grandes
  volúmenes de datos
    - Permite mostrar progreso y hacer commits frecuentes
  5. Manejo de excepciones específicas:
    - Catch específicos para cada tipo de error posible
    - Mejor diagnóstico mediante impresión de muestra de
  datos problemáticos
  6. Patrones de seguridad:
    - Usamos TRUNCATE TABLE antes de INSERT para evitar
  duplicados
    - Cerramos explícitamente cursores y conexiones