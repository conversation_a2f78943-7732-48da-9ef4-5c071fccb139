2025-06-05 22:46:56,432 - INFO - <PERSON>rea PyDesafioTecnico iniciada correctamente (PID: 40640)
2025-06-05 22:46:56,432 - INFO - <PERSON>rea PyDesafioTecnico iniciada con éxito (2.07 segundos)
2025-06-05 22:46:56,432 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_config.json
2025-06-05 22:48:02,335 - INFO - [PyDesafioTecnico] Iniciando el proceso...
2025-06-05 22:48:02,335 - INFO - [PyDesafioTecnico] Configurando el driver...
2025-06-05 22:48:02,335 - INFO - [PyDesafioTecnico] Driver configurado exitosamente
2025-06-05 22:48:02,351 - INFO - [PyDesafioTecnico] Navegando a la página de login...
2025-06-05 22:48:02,366 - INFO - [PyDesafioTecnico] Completando campo de email...
2025-06-05 22:48:02,382 - INFO - [PyDesafioTecnico] Completando campo de password...
2025-06-05 22:48:02,390 - INFO - [PyDesafioTecnico] Haciendo clic en el botón de login...
2025-06-05 22:48:02,390 - INFO - [PyDesafioTecnico] Login completado exitosamente
2025-06-05 22:48:02,409 - INFO - [PyDesafioTecnico] Esperando 20 segundos para la descarga...
2025-06-05 22:48:02,425 - INFO - [PyDesafioTecnico] Archivo encontrado: C:\Users\<USER>\Desafío Técnico VTR Junio 2025 N°01 (7).xlsx
2025-06-05 22:48:02,432 - INFO - [PyDesafioTecnico] Ocurrió un error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P01_Despues_de_configurar_la_TV_es_crucial_validar_todas_las_funciones_Volumen'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P02_El_objetivo_principal_de_programar_el_control_remoto_TiVo_es_que_solo_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P03_En_algunos_casos_codigos_de_otras_marcas_como_JVC_para_Caixun_pueden_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P04_En_el_metodo_semiautomatico_si_la_marca_de_la_TV_no_aparece_en_las_10_mas_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P05_La_conexion_HDMI_entre_el_sistema_IPTV_y_la_TV_del_cliente_es_importante_para'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P06_La_programacion_automatica_solo_ocurre_durante_la_instalacion_inicial_FTI'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P07_Si_la_marca_de_la_TV_del_cliente_no_esta_en_la_base_de_datos_del_IPTV_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P08_Si_la_marca_de_la_TV_no_esta_en_la_lista_o_ningun_codigo_funciona_se_le_debe'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P09_Si_la_TV_no_responde_al_codigo_IR_debes_seleccionar_la_opcion_Intente_otra_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P10_Siempre_es_posible_encontrar_un_codigo_compatible_para_cualquier_marca_de_TV'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-05 22:48:02,432 - INFO - [PyDesafioTecnico] Detalles del error:
2025-06-05 22:48:02,474 - INFO - [PyDesafioTecnico] Traceback (most recent call last):
2025-06-05 22:48:02,513 - INFO - [PyDesafioTecnico] File "c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\PyDesafioTecnico.py", line 197, in <module>
2025-06-05 22:48:02,531 - INFO - [PyDesafioTecnico] sql_server_cursor.executemany(sql_server_query, batch)
2025-06-05 22:48:02,573 - INFO - [PyDesafioTecnico] pyodbc.ProgrammingError: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P01_Despues_de_configurar_la_TV_es_crucial_validar_todas_las_funciones_Volumen'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P02_El_objetivo_principal_de_programar_el_control_remoto_TiVo_es_que_solo_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P03_En_algunos_casos_codigos_de_otras_marcas_como_JVC_para_Caixun_pueden_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P04_En_el_metodo_semiautomatico_si_la_marca_de_la_TV_no_aparece_en_las_10_mas_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P05_La_conexion_HDMI_entre_el_sistema_IPTV_y_la_TV_del_cliente_es_importante_para'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P06_La_programacion_automatica_solo_ocurre_durante_la_instalacion_inicial_FTI'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P07_Si_la_marca_de_la_TV_del_cliente_no_esta_en_la_base_de_datos_del_IPTV_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P08_Si_la_marca_de_la_TV_no_esta_en_la_lista_o_ningun_codigo_funciona_se_le_debe'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P09_Si_la_TV_no_responde_al_codigo_IR_debes_seleccionar_la_opcion_Intente_otra_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P10_Siempre_es_posible_encontrar_un_codigo_compatible_para_cualquier_marca_de_TV'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
2025-06-05 22:48:02,595 - INFO - [PyDesafioTecnico] 
2025-06-05 22:48:02,634 - INFO - [PyDesafioTecnico] Conexiones a bases de datos cerradas correctamente
2025-06-05 22:48:02,654 - INFO - [PyDesafioTecnico] Cerrando el navegador...
2025-06-05 22:48:02,677 - INFO - [PyDesafioTecnico] Proceso finalizado
2025-06-05 22:48:02,717 - INFO - Proceso PyDesafioTecnico completado exitosamente. Tiempo de ejecución: 68.30 segundos
2025-06-05 22:48:02,733 - INFO - Salida de PyDesafioTecnico: Iniciando el proceso...
Configurando el driver...
Driver configurado exitosamente
Navegando a la página de login...
Completando campo de email...
Completando campo de password...
Haciendo clic en el botón de login...
Login completado exitosamente
Esperando 20 segundos para la descarga...
Archivo encontrado: C:\Users\<USER>\Desafío Técnico VTR Junio 2025 N°01 (7).xlsx
Ocurrió un error: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P01_Despues_de_configurar_la_TV_es_crucial_validar_todas_las_funciones_Volumen'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P02_El_objetivo_principal_de_programar_el_control_remoto_TiVo_es_que_solo_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P03_En_algunos_casos_codigos_de_otras_marcas_como_JVC_para_Caixun_pueden_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P04_En_el_metodo_semiautomatico_si_la_marca_de_la_TV_no_aparece_en_las_10_mas_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P05_La_conexion_HDMI_entre_el_sistema_IPTV_y_la_TV_del_cliente_es_importante_para'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P06_La_programacion_automatica_solo_ocurre_durante_la_instalacion_inicial_FTI'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P07_Si_la_marca_de_la_TV_del_cliente_no_esta_en_la_base_de_datos_del_IPTV_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P08_Si_la_marca_de_la_TV_no_esta_en_la_lista_o_ningun_codigo_funciona_se_le_debe'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P09_Si_la_TV_no_responde_al_codigo_IR_debes_seleccionar_la_opcion_Intente_otra_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P10_Siempre_es_posible_encontrar_un_codigo_compatible_para_cualquier_marca_de_TV'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")
Detalles del error:
Traceback (most recent call last):
File "c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\PyDesafioTecnico.py", line 197, in <module>
sql_server_cursor.executemany(sql_server_query, batch)
pyodbc.ProgrammingError: ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P01_Despues_de_configurar_la_TV_es_crucial_validar_todas_las_funciones_Volumen'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P02_El_objetivo_principal_de_programar_el_control_remoto_TiVo_es_que_solo_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P03_En_algunos_casos_codigos_de_otras_marcas_como_JVC_para_Caixun_pueden_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P04_En_el_metodo_semiautomatico_si_la_marca_de_la_TV_no_aparece_en_las_10_mas_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P05_La_conexion_HDMI_entre_el_sistema_IPTV_y_la_TV_del_cliente_es_importante_para'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P06_La_programacion_automatica_solo_ocurre_durante_la_instalacion_inicial_FTI'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P07_Si_la_marca_de_la_TV_del_cliente_no_esta_en_la_base_de_datos_del_IPTV_el_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P08_Si_la_marca_de_la_TV_no_esta_en_la_lista_o_ningun_codigo_funciona_se_le_debe'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P09_Si_la_TV_no_responde_al_codigo_IR_debes_seleccionar_la_opcion_Intente_otra_'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name 'P10_Siempre_es_posible_encontrar_un_codigo_compatible_para_cualquier_marca_de_TV'. (207); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Statement(s) could not be prepared. (8180)")

Conexiones a bases de datos cerradas correctamente
Cerrando el navegador...
Proceso finalizado
2025-06-05 22:48:02,772 - INFO - Configuración guardada en c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\scheduler_config.json

