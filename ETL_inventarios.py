import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta

import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@181.212.32.10/master?driver=ODBC+Driver+17+for+SQL+Server')

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)



# Crear una sesión
Session = sessionmaker(bind=engineMYsql)
session = Session()

try:
    # Iniciar una transacción
    with session.begin():
        # Ejecutar el procedimiento almacenado
        result = session.execute('CALL SP_CREAR_TABLA_INVENTARIO_RESULTADO_FINAL()')
        
        
        # Confirmar los cambios en la base de datos
        session.commit()
        
except Exception as e:
    # Manejar cualquier excepción que pueda ocurrir
    print(f"Error al ejecutar el procedimiento almacenado: {str(e)}")
    
    # Revertir los cambios en caso de error
    session.rollback()


# Crear un data frame a partir de una consulta a SQL Server
Data2 = pd.read_sql_query("SELECT * FROM TB_INVENTARIO_RESULTADO_FINAL", engineMYsql)
Data2.to_sql('TB_INVENTARIO_RESULTADO_FINAL', engine, if_exists='replace',index=False)

# Crear un data frame a partir de una consulta a SQL Server
Data2 = pd.read_sql_query("SELECT * FROM VW_LOGIS_SEMAFORO ", engineMYsql)
Data2.to_sql('VW_LOGIS_SEMAFORO', engine, if_exists='replace',index=False)


