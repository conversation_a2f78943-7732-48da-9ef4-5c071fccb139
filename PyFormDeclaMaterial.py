import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta

import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine


url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTCoabhOdai7cZCjvqyDQMM26w2A-884v37GER7QPXEttNXUXQJqOLYxWcTEeS6ADX8sESky5btQQaX/pub?gid=1397167275&single=true&output=csv'

df = pd.read_csv(url)


# engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)
# df.to_sql(name='tb_turnos_py', con=engineMYsql, if_exists = 'replace', index=False)


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@************/master?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@*************/master?driver=ODBC+Driver+17+for+SQL+Server')
Session = sessionmaker(bind=engine)
session = Session()


# data_types = {
#     'Marca temporal': sqlalchemy.types.VARCHAR(255),
#     'Nombre técnico': sqlalchemy.types.VARCHAR(255),
#     'Rut cliente (sin puntos y con guion)': sqlalchemy.types.VARCHAR(255),
#     'Numero de orden': sqlalchemy.types.VARCHAR(255),
#     'Tipo actividad': sqlalchemy.types.VARCHAR(255),
#     'Serie equipo retirado': sqlalchemy.types.VARCHAR(255),
#     'Motivo Cambio de equipo': sqlalchemy.types.VARCHAR(255),
#     'Foto serie equipo retirado': sqlalchemy.types.VARCHAR(500),
#     'Serie equipo instalado N°1': sqlalchemy.types.VARCHAR(500),
#     'Serie equipo instalado N°2': sqlalchemy.types.VARCHAR(255),
#     'Serie equipo instalado N°3': sqlalchemy.types.VARCHAR(255),
#     'Serie equipo instalado N°4': sqlalchemy.types.VARCHAR(255),
#     'Serie equipo instalado N°5': sqlalchemy.types.VARCHAR(255),
#     'Evidencia del motivo del cambio': sqlalchemy.types.VARCHAR(1000),
#     'Tipo Cliente': sqlalchemy.types.VARCHAR(255),
#     'Dirección': sqlalchemy.types.VARCHAR(255),
#     'Comuna': sqlalchemy.types.VARCHAR(255),
#     'Detalle de los trabajos realizados': sqlalchemy.types.VARCHAR(4000),
#     'Fecha de ejecución de la actividad': sqlalchemy.types.VARCHAR(255),
#     'Horario Inicio': sqlalchemy.types.VARCHAR(255),
#     'Horario Finalización': sqlalchemy.types.VARCHAR(255),
#     'registro material': sqlalchemy.types.VARCHAR(255),
#     'Serie física retirada ¿corresponde a la sistémica?': sqlalchemy.types.VARCHAR(1000),
#     'Registrar serie sistémica': sqlalchemy.types.VARCHAR(255)
# }


df.to_sql('TB_LOGIST_PY_FORM_DECLARA', engine, if_exists='replace',index=False)


## Close the session
session.close()





