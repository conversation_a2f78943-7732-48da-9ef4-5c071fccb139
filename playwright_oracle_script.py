from playwright.sync_api import sync_playwright
import time
import os
import logging

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('playwright_oracle_script.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Mostrar banner de inicio
start_time = time.time()
print("="*80)
print(f"INICIANDO SCRIPT DE NAVEGACIÓN A ORACLE CLOUD CON PLAYWRIGHT - {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*80)

def run_oracle_navigation():
    try:
        with sync_playwright() as p:
            # Configuración del navegador
            print("1. Configurando navegador Playwright...")
            browser = p.chromium.launch(headless=False)  # Cambiar a True para modo headless
            context = browser.new_context()
            page = context.new_page()

            # Configurar tiempo de espera
            page.set_default_timeout(15000)  # 15 segundos

            # Navegar a Oracle Cloud
            print("2. Navegando a Oracle Cloud...")
            oracle_url = "https://vtr.fs.ocs.oraclecloud.com/"
            print(f"   - Accediendo a: {oracle_url}")
            page.goto(oracle_url)

            # Esperar a que la página cargue
            print("   - Esperando que la página de Oracle Cloud cargue completamente...")
            page.wait_for_load_state('networkidle')

            # Verificar título de la página
            titulo = page.title()
            print(f"   - Página de Oracle Cloud cargada. Título: {titulo}")

            # VERIFICAR SI YA ESTAMOS EN LA ÚLTIMA ETAPA
            print("   - Verificando si ya estamos en la sesión de Oracle Cloud...")
            try:
                # Verificar específicamente si el elemento con ID elId50 está presente
                elemento_indicador = page.locator("#elId50")
                if elemento_indicador.is_visible():
                    print("   - Elemento con ID 'elId50' encontrado - Sesión activa en etapa final confirmada")

                    print("\n" + "="*80)
                    print("¡SESIÓN ACTIVA EN ETAPA FINAL DETECTADA!")
                    print("Se detectó el elemento 'elId50' que confirma que estamos en la etapa final de Oracle Cloud.")
                    print("Saltando directamente a la etapa final.")
                    print("="*80 + "\n")

                    # Tomar screenshot de la sesión activa
                    page.screenshot(path="session_already_active.png")

                    # Confirmación de navegación exitosa por sesión activa detectada
                    print("9. Navegación a Oracle Cloud completada exitosamente (elemento elId50 detectado)")
                    print("   - Navegador abierto y listo para uso.")

                    # PASO 10: Interactuar con el elemento elId50 ya detectado
                    print("10. Interactuando con el elemento elId50 detectado...")
                    try:
                        print("   - Haciendo clic en el elemento elId50 previamente detectado...")
                        elemento_indicador.click()
                        print("   - Clic realizado correctamente en el elemento elId50")
                    except Exception as e:
                        print(f"   - Error al hacer clic normal: {str(e)}")
                        # Intentar con JavaScript como alternativa
                        try:
                            print("   - Intentando hacer clic con JavaScript...")
                            page.evaluate("arguments[0].click();", elemento_indicador.element_handle())
                            print("   - Clic realizado con JavaScript en elemento elId50")
                        except Exception as js_e:
                            print(f"   - Error también con JavaScript: {str(js_e)}")

                    # Tomar screenshot después de hacer clic
                    page.screenshot(path="after_click_elId50_session_active.png")
                    print("   - Interacción con elemento elId50 completada")

                    print("   - El navegador NO se cerrará automáticamente.")
                    return

                else:
                    print("   - No se encontró el elemento específico elId50")
                    raise Exception("No se encontró el elemento específico elId50")

            except Exception as e:
                print("   - No se detectó sesión activa, procediendo con el inicio de sesión normal.")

            # PASO 1: Hacer clic en "Conectarse con SSO"
            print("3. Haciendo clic en 'Conectarse con SSO'...")
            try:
                sso_button = page.locator("button:has-text('Conectarse con SSO')")
                sso_button.click()
                print("   - Botón 'Conectarse con SSO' clickeado correctamente")

                # PASO 2: Ingresar nombre de usuario para SSO
                print("4. Ingresando nombre de usuario para SSO...")
                time.sleep(3)  # Espera para asegurar que la página ha cambiado

                # Esperar a que cambie la URL
                print("   - Esperando a que la página de SSO cargue completamente...")
                page.wait_for_url(lambda url: "microsoftonline.com" in url or "login" in url, timeout=30000)

                # Dar tiempo adicional para que los elementos se carguen
                time.sleep(5)

                # Imprimir la URL actual para verificar
                print(f"   - URL actual: {page.url}")

                # Tomar screenshot antes de buscar el campo
                page.screenshot(path="before_username_field.png")

                # Buscar el campo de usuario
                print("   - Buscando campo de usuario...")
                username_input = page.locator("#sso_username").or_(
                    page.locator("#username")
                ).or_(
                    page.locator("input[placeholder*='usuario']")
                ).or_(
                    page.locator("input[type='text']").first
                )

                if username_input.is_visible():
                    print("   - Campo de usuario encontrado")

                    # Limpiar e ingresar usuario
                    username_input.fill("")
                    username_input.fill("ncornejoh")
                    print("   - Nombre de usuario ingresado correctamente")

                    # Tomar screenshot después de ingresar
                    page.screenshot(path="after_username_input.png")
                    print("   - Nombre de usuario ingresado")

                    # Hacer clic en "Continuar con SSO"
                    print("   - Buscando botón 'Continuar con SSO'...")
                    continue_button = page.locator("#signin-with-sso-button").or_(
                        page.locator("#continue-with-sso")
                    ).or_(
                        page.locator("button[type='submit']")
                    ).or_(
                        page.locator("input[type='submit']")
                    )

                    if continue_button.is_visible():
                        continue_button.click()
                        print("   - Botón 'Continuar con SSO' clickeado correctamente")
                    else:
                        # Enviar Enter en el campo de usuario
                        username_input.press("Enter")
                        print("   - Se envió Enter en el campo de usuario")

                    # Tomar screenshot después del clic
                    page.screenshot(path="after_continue_button.png")
                    print("   - Botón 'Continuar con SSO' procesado")

                    # PASO 3: Microsoft Login - Ingresar correo
                    print("5. Procesando página de Microsoft Login...")
                    time.sleep(8)

                    # Tomar screenshot
                    page.screenshot(path="microsoft_login.png")

                    # Esperar a que aparezca la página de Microsoft
                    page.wait_for_url(lambda url: "microsoftonline.com" in url, timeout=30000)
                    print("   - Página de Microsoft detectada")

                    time.sleep(5)
                    print(f"   - URL actual: {page.url}")

                    # Tomar captura antes de buscar email
                    page.screenshot(path="before_email_input.png")

                    # Buscar campo de correo
                    print("   - Buscando campo para ingresar correo electrónico...")
                    email_input = page.locator("#i0116").or_(
                        page.locator("#loginfmt")
                    ).or_(
                        page.locator("input[placeholder*='Email']")
                    ).or_(
                        page.locator("input[type='email']")
                    ).or_(
                        page.locator("input[type='text']").first
                    )

                    if email_input.is_visible():
                        print("   - Campo de correo encontrado")

                        # Ingresar correo
                        email_input.fill("<EMAIL>")
                        print("   - Correo electrónico ingresado")

                        # Tomar screenshot
                        page.screenshot(path="after_email_input.png")

                        # Hacer clic en Siguiente
                        next_button = page.locator("#idSIButton9").or_(
                            page.locator("input[value='Siguiente']")
                        ).or_(
                            page.locator("input[value='Next']")
                        )

                        if next_button.is_visible():
                            next_button.click()
                            print("   - Botón Siguiente clickeado")
                        else:
                            email_input.press("Enter")
                            print("   - Enter enviado en el campo de correo")

                        # Tomar screenshot
                        page.screenshot(path="after_email_next.png")

                        # PASO 4: Ingresar contraseña
                        print("6. Ingresando contraseña...")
                        time.sleep(3)

                        # Tomar captura antes de contraseña
                        page.screenshot(path="before_password.png")

                        # Buscar campo de contraseña
                        password_input = page.locator("#i0118").or_(
                            page.locator("#passwd")
                        ).or_(
                            page.locator("input[type='password']")
                        )

                        if password_input.is_visible():
                            password_input.fill("Telqway.202517")
                            print("   - Contraseña ingresada")

                            # Hacer clic en Sign in
                            sign_in_button = page.locator("#idSIButton9").or_(
                                page.locator("input[type='submit']")
                            ).or_(
                                page.locator("button:has-text('Sign in')")
                            )

                            if sign_in_button.is_visible():
                                sign_in_button.click()
                                print("   - Botón 'Sign in' clickeado")
                            else:
                                password_input.press("Enter")
                                print("   - Enter enviado en el campo de contraseña")

                            # PASO 5: Verificar diálogo "Stay signed in?"
                            print("7. Verificando diálogo 'Stay signed in'...")
                            time.sleep(5)

                            try:
                                yes_button = page.locator("input[value='Yes']")
                                if yes_button.is_visible():
                                    yes_button.click()
                                    print("   - Se hizo clic en 'Yes' en el diálogo 'Stay signed in'")
                            except:
                                print("   - No se encontró diálogo 'Stay signed in', continuando...")

                            # PASO 6: Esperar carga completa de Oracle Cloud
                            print("8. Esperando carga completa de Oracle Cloud...")
                            page.wait_for_url(lambda url: "oraclecloud.com" in url and "Oracle Field Service" in page.title, timeout=60000)
                            time.sleep(5)

                            print("   - Oracle Cloud cargado completamente")
                            print("   - Login con SSO completado exitosamente")

                        else:
                            print("   - No se encontró campo de contraseña")
                            page.screenshot(path="no_password_field.png")

                    else:
                        print("   - No se encontró campo de correo")
                        page.screenshot(path="no_email_field.png")

                else:
                    print("   - No se encontró campo de usuario")
                    page.screenshot(path="no_username_field.png")

            except Exception as e:
                print(f"   - ERROR en proceso de autenticación: {str(e)}")
                page.screenshot(path="error_auth.png")
                raise

            # Confirmación de navegación exitosa
            print("9. Navegación a Oracle Cloud completada exitosamente")
            print("   - Navegador abierto y listo para uso.")

            # PASO 10: Buscar e interactuar con el elemento elId50
            print("10. Buscando e interactuando con el elemento elId50...")
            try:
                elemento_elId50 = page.locator("#elId50")
                elemento_elId50.wait_for(state='visible', timeout=15000)

                # Tomar screenshot antes de clic
                page.screenshot(path="before_click_elId50.png")

                print("   - Elemento elId50 encontrado. Haciendo clic...")
                elemento_elId50.click()
                print("   - Clic realizado correctamente en el elemento elId50")

                # Tomar screenshot después de clic
                page.screenshot(path="after_click_elId50.png")
                print("   - Interacción con elemento elId50 completada")

            except Exception as e:
                print(f"   - ERROR: No se pudo encontrar o interactuar con el elemento elId50: {str(e)}")
                page.screenshot(path="error_elId50.png")
                print("   - Continuando con el flujo a pesar del error")

            print("   - El navegador NO se cerrará automáticamente.")

    except Exception as e:
        print(f"   - ERROR: No se pudo navegar a Oracle Cloud: {str(e)}")
        logger.error(f"Error en la ejecución: {str(e)}")
        import traceback
        print("Detalles:")
        print(traceback.format_exc())
        raise

    finally:
        # Mostrar resumen final
        end_time = time.time()
        duration = end_time - start_time
        print("\n" + "="*80)
        print(f"PROCESO FINALIZADO - {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Tiempo de ejecución: {duration:.2f} segundos")
        print("="*80)

        # NO cerrar el navegador
        print("\n¡IMPORTANTE! El navegador permanecerá abierto para que puedas interactuar con Oracle Cloud.")
        print("Para cerrar el navegador manualmente, simplemente cierra la ventana cuando hayas terminado.")

        # Mantener el script en ejecución
        try:
            print("\nPresiona Ctrl+C en la consola cuando quieras terminar el script...")
            while True:
                time.sleep(10)
        except KeyboardInterrupt:
            print("\nScript terminado por el usuario. El navegador permanecerá abierto.")

# Ejecutar la función principal
if __name__ == "__main__":
    run_oracle_navigation()