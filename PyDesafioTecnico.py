from selenium import webdriver 
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import sys
import pandas as pd
from datetime import datetime
import mysql.connector
import pyodbc
import re
import logging
import subprocess

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('PyDesafioTecnico.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Mostrar banner de inicio
start_time = datetime.now()
print("="*60)
print(f"INICIANDO PROCESO DESAFÍO TÉCNICO - {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*60)

# Función para enviar correo directamente como respaldo
def try_direct_email_send(html_content):
    """Intenta enviar correo directamente usando los módulos de correo disponibles"""
    try:
        # Intentar usar email_utils_v2 si está disponible
        try:
            logger.info("Intentando usar email_utils_v2 para enviar correo...")
            from email_utils_v2 import enviar_correo
            resultado_envio = enviar_correo(
                destinatario=["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                asunto="Reporte Desafío Técnico - Usuarios Pendientes",
                cuerpo=html_content,
                html=True,
                debug=True
            )
        except ImportError:
            # Si no está disponible, usar email_utils
            logger.info("Módulo email_utils_v2 no encontrado, usando email_utils...")
            from email_utils import enviar_correo
            resultado_envio = enviar_correo(
                destinatario=["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                asunto="Reporte Desafío Técnico - Usuarios Pendientes",
                cuerpo=html_content,
                html=True
            )
        
        if resultado_envio['exito']:
            logger.info("Correo enviado exitosamente con método alternativo")
            print("Correo enviado exitosamente con método alternativo")
        else:
            logger.error(f"Error enviando correo con método alternativo: {resultado_envio['mensaje']}")
            print(f"Error enviando correo: {resultado_envio['mensaje']}")
            
    except Exception as e:
        logger.error(f"Error en envío de correo alternativo: {str(e)}")
        print(f"Error en envío de correo: {str(e)}")


def limpiar_nombres_columnas_simple(df):
    """Función simplificada para limpiar nombres de columnas"""
    nuevo_nombres = {}
    
    for columna in df.columns:
        # Limpieza básica y eficiente
        nombre_limpio = re.sub(r'[^a-zA-Z0-9_]', '', str(columna).strip())
        
        # Si está vacío o comienza con número, agregar prefijo
        if not nombre_limpio or nombre_limpio[0].isdigit():
            nombre_limpio = f'col_{nombre_limpio}' if nombre_limpio else f'col_{len(nuevo_nombres)}'
        
        # Limitar longitud
        if len(nombre_limpio) > 120:
            nombre_limpio = nombre_limpio[:120]
        
        nuevo_nombres[columna] = nombre_limpio
    
    return df.rename(columns=nuevo_nombres)


def recrear_tabla_sql_server(cursor, conn, df):
    """Función para recrear la tabla de manera simple"""
    try:
        # Eliminar tabla si existe
        cursor.execute("IF OBJECT_ID('tb_desafio_tecnico', 'U') IS NOT NULL DROP TABLE tb_desafio_tecnico")
        
        # Crear tabla con columnas dinámicas
        columnas_sql = ", ".join([f"[{col}] NVARCHAR(MAX)" for col in df.columns])
        cursor.execute(f"CREATE TABLE tb_desafio_tecnico (id INT IDENTITY(1,1) PRIMARY KEY, {columnas_sql})")
        conn.commit()
        print("Tabla tb_desafio_tecnico recreada exitosamente")
        
    except Exception as e:
        print(f"Error al recrear tabla: {str(e)}")
        raise


def insertar_datos_lote(cursor, conn, df, tabla):
    """Función simplificada para insertar datos en lotes"""
    # Preparar datos
    data = [tuple(None if pd.isna(x) else str(x) for x in row) for row in df.values]
    columns = list(df.columns)
    columns_str = ", ".join([f"[{col}]" for col in columns])
    placeholders = ", ".join(["?" for _ in columns])
    
    # Query de inserción
    query = f"INSERT INTO {tabla} ({columns_str}) VALUES ({placeholders})"
    
    # Insertar en lotes
    batch_size = 100
    total_rows = len(data)
    
    for i in range(0, total_rows, batch_size):
        batch = data[i:min(i+batch_size, total_rows)]
        cursor.executemany(query, batch)
        conn.commit()
        print(f"Progreso {tabla}: {min(i+batch_size, total_rows)}/{total_rows} registros")
    
    return total_rows


try:
    # El banner de inicio ya se muestra arriba con timestamp
    
    # ===== CONFIGURACIÓN DE SELENIUM =====
    print("1. Configurando navegador...")
    edge_options = Options()
    edge_options.add_argument('--start-maximized')
    edge_options.add_argument('--disable-gpu')
    edge_options.add_argument('--no-sandbox')
    edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    # Usar el driver local en vez de la descarga automática
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")
        
        if not os.path.exists(msedgedriver_path):
            print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
            print("Por favor, ejecute descargar_directo.py para obtener el driver")
            sys.exit(1)
        
        print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
        service = Service(msedgedriver_path)
        driver = webdriver.Edge(service=service, options=edge_options)
        print("Driver Edge inicializado correctamente")
    except Exception as e:
        print(f"Error de inicialización del driver: {str(e)}")
        sys.exit(1)
        
    wait = WebDriverWait(driver, 10)

    # ===== AUTOMATIZACIÓN WEB =====
    print("2. Realizando login y descarga...")
    print("   - Navegando a la página web...")
    driver.get("https://test.isiac.cl/vtr/public/login")
    
    # Login
    print("   - Ingresando credenciales...")
    email_field = wait.until(EC.element_to_be_clickable((By.ID, "email")))
    email_field.send_keys("<EMAIL>")
    
    password_field = wait.until(EC.element_to_be_clickable((By.ID, "password")))
    password_field.send_keys("**********")
    
    print("   - Iniciando sesión...")
    login_button = wait.until(EC.element_to_be_clickable((By.XPATH, "/html/body/div[2]/div/div/div[1]/div/div/div/div/div/div[2]/form/div[4]/button")))
    login_button.click()
    print("   - Sesión iniciada correctamente")
    time.sleep(3)

    # Seleccionar última opción
    print("   - Seleccionando informe más reciente...")
    select_element = wait.until(EC.element_to_be_clickable((By.ID, "informe_id")))
    options = select_element.find_elements(By.TAG_NAME, "option")
    valid_options = [opt for opt in options if opt.get_attribute("value")]
    
    if valid_options:
        selected_option = valid_options[-1]  # Último elemento
        option_text = selected_option.text
        print(f"   - Seleccionado informe: {option_text}")
        selected_option.click()
    else:
        print("   - ADVERTENCIA: No se encontraron informes disponibles")
    
    print("   - Esperando carga de datos (10 segundos)...")
    time.sleep(10)

    # Descargar
    print("   - Iniciando descarga del archivo Excel...")
    download_button = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="download-excel"]')))
    download_button.click()
    
    print("   - Esperando que se complete la descarga (20 segundos)...")
    for i in range(4):
        time.sleep(5)
        print(f"      Progreso: {(i+1)*25}%")
    print("   - Descarga completada")
    

    # ===== PROCESAMIENTO DE ARCHIVO =====
    print("3. Procesando archivo descargado...")
    downloads_path = os.path.expanduser("~/Downloads")
    print(f"   - Buscando archivo en: {downloads_path}")
    
    # Listar archivos y filtrar solo Excel
    files = [os.path.join(downloads_path, f) for f in os.listdir(downloads_path) 
             if f.endswith('.xlsx') or f.endswith('.xls')]
    
    if not files:
        print("   - ERROR: No se encontraron archivos Excel en la carpeta de descargas")
        raise FileNotFoundError("No se encontraron archivos Excel en la carpeta de descargas")
        
    latest_file = max(files, key=os.path.getctime)
    file_size = os.path.getsize(latest_file) / 1024  # KB
    print(f"   - Archivo encontrado: {os.path.basename(latest_file)} ({file_size:.1f} KB)")

    # Leer y limpiar datos
    print("   - Leyendo datos del archivo Excel...")
    df = pd.read_excel(latest_file)
    print(f"   - Datos leídos: {len(df)} filas, {len(df.columns)} columnas")
    
    # Limpiar datos
    print("   - Limpiando nombres de columnas...")
    df.columns = df.columns.str.strip()
    df = limpiar_nombres_columnas_simple(df)
    
    # Agregar columna de fecha
    print("   - Agregando columna de fecha de carga...")
    timestamp = datetime.now()
    df['fecha_carga'] = timestamp
    
    print(f"   - Datos procesados exitosamente: {len(df)} filas, {len(df.columns)} columnas")

    # ===== CONEXIONES A BASES DE DATOS =====
    print("4. Conectando a bases de datos...")
    
    # SQL Server
    print("   - Conectando a SQL Server (************)...")
    try:
        sql_server_conn = pyodbc.connect(
            'DRIVER={ODBC Driver 17 for SQL Server};SERVER=************;DATABASE=telqway;UID=ncornejo;PWD=**********'
        )
        sql_server_cursor = sql_server_conn.cursor()
        print("   - Conexión a SQL Server establecida correctamente")
    except Exception as e:
        print(f"   - ERROR: No se pudo conectar a SQL Server: {str(e)}")
        raise
    
    # MySQL
    print("   - Conectando a MySQL (**************)...")
    try:
        mysql_conn = mysql.connector.connect(
            host='**************',
            user='ncornejo',
            password='**********',
            database='operaciones_tqw'
        )
        mysql_cursor = mysql_conn.cursor()
        print("   - Conexión a MySQL establecida correctamente")
    except Exception as e:
        print(f"   - ERROR: No se pudo conectar a MySQL: {str(e)}")
        raise

    # ===== CARGA A SQL SERVER =====
    print("5. Cargando datos a SQL Server...")
    recrear_tabla_sql_server(sql_server_cursor, sql_server_conn, df)
    total_insertado = insertar_datos_lote(sql_server_cursor, sql_server_conn, df, "tb_desafio_tecnico")
    print(f"Insertados {total_insertado} registros en SQL Server")

    # ===== EJECUTAR STORED PROCEDURE =====
    print("6. Ejecutando stored procedure...")
    sql_server_cursor.execute("EXEC sp_vw_desafio_tecnico")
    sql_server_conn.commit()
    print("Stored procedure ejecutado exitosamente")

    # ===== GENERAR REPORTE =====
    print("7. Generando reporte de pendientes...")
    query_pendientes = """
        SELECT Estado_Evaluacion, Nombre_Completo, Supervisor
        FROM vw_desafio_tecnico
        WHERE Estado_Evaluacion = 'No Realizado'
        AND Justificaciones = 'No'
        ORDER BY Supervisor DESC
    """
    
    df_pendientes = pd.read_sql(query_pendientes, sql_server_conn)
    
    if not df_pendientes.empty:
        print(f"Usuarios pendientes: {len(df_pendientes)}")
        
        # Enviar correo con método mejorado
        cuerpo_html = df_pendientes.to_html(index=False, table_id="tabla-pendientes", classes="table table-striped")
        
        # Mejorar el HTML para mejor presentación
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h3 {{ color: #2c3e50; text-align: center; }}
                .table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .table th {{ background-color: #3498db; color: white; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .table tr:nth-child(even) {{ background-color: #f8f9fa; }}
                .table tr:hover {{ background-color: #e9ecef; }}
                .footer {{ margin-top: 20px; font-size: 90%; color: #6c757d; text-align: center; }}
            </style>
        </head>
        <body>
            <h3>Usuarios Pendientes de Realizar Desafío Técnico</h3>
            {cuerpo_html}
            <div class="footer">
                <p>Este es un correo automático, por favor no responder.</p>
            </div>
        </body>
        </html>
        """
        
        # Enviar correo directamente sin usar test_email_report.py
        logger.info("Enviando correo directamente...")
        try_direct_email_send(html_content)
    else:
        print("No hay usuarios pendientes")

    # ===== SINCRONIZACIÓN CON MYSQL =====
    print("8. Sincronizando con MySQL...")
    df_vista = pd.read_sql("SELECT * FROM vw_desafio_tecnico", sql_server_conn)
    
    if not df_vista.empty:
        # Limpiar datos para MySQL
        df_vista_limpia = limpiar_nombres_columnas_simple(df_vista)
        
        # Verificar si la columna ya existe en la tabla MySQL
        try:
            # Obtener estructura de la tabla destino
            mysql_cursor.execute("DESCRIBE vw_desafio_tecnico")
            columnas_tabla = [row[0] for row in mysql_cursor.fetchall()]
            
            # Solo agregar la columna fecha_sincronizacion si existe en la tabla
            if 'fecha_sincronizacion' in columnas_tabla:
                df_vista_limpia['fecha_sincronizacion'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.info("Columna fecha_sincronizacion agregada al DataFrame")
            else:
                logger.info("La columna fecha_sincronizacion no existe en la tabla MySQL, se omitirá")
        except Exception as e:
            logger.warning(f"Error al verificar columnas: {e}, continuando sin fecha_sincronizacion")
            # Continuar sin agregar la columna fecha_sincronizacion
        
        # Truncar y cargar
        mysql_cursor.execute("TRUNCATE TABLE vw_desafio_tecnico")
        mysql_conn.commit()
        
        # Insertar en MySQL
        data_mysql = [tuple(None if pd.isna(x) else str(x) for x in row) for row in df_vista_limpia.values]
        columns_mysql = [f"`{col}`" for col in df_vista_limpia.columns]
        placeholders_mysql = ", ".join(["%s" for _ in columns_mysql])
        
        mysql_query = f"INSERT INTO vw_desafio_tecnico ({', '.join(columns_mysql)}) VALUES ({placeholders_mysql})"
        
        batch_size = 100
        for i in range(0, len(data_mysql), batch_size):
            batch = data_mysql[i:min(i+batch_size, len(data_mysql))]
            mysql_cursor.executemany(mysql_query, batch)
            mysql_conn.commit()
        
        print(f"Sincronizados {len(data_mysql)} registros con MySQL")
    else:
        print("No hay datos en la vista para sincronizar")

    # Mostrar resumen final con información de tiempo transcurrido
    end_time = datetime.now()
    duration = end_time - start_time
    print("\n" + "="*60)
    print(f"PROCESO COMPLETADO EXITOSAMENTE - {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Tiempo de ejecución: {duration.total_seconds():.2f} segundos")
    print("="*60)

except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    print("Detalles:")
    print(traceback.format_exc())

finally:
    # Limpiar recursos
    try:
        if 'mysql_cursor' in locals():
            mysql_cursor.close()
        if 'mysql_conn' in locals():
            mysql_conn.close()
        if 'sql_server_cursor' in locals():
            sql_server_cursor.close()
        if 'sql_server_conn' in locals():
            sql_server_conn.close()
        if 'driver' in locals():
            driver.quit()
        print("Recursos liberados correctamente")
    except Exception as e:
        print(f"Error liberando recursos: {e}")
