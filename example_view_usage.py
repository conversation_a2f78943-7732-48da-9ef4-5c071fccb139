#!/usr/bin/env python3
"""
Example script showing how to use the latest data view functionality.
"""

from database import create_and_query_latest_data_view, DatabaseManager

def main():
    """Example usage of the latest data view functionality."""

    print("📊 Example: Creating and querying latest data view")
    print("=" * 60)

    # Method 1: Using the standalone function
    print("🔧 Method 1: Using create_and_query_latest_data_view()")
    results = create_and_query_latest_data_view()

    if results:
        print(f"✅ Retrieved {len(results)} records from latest data view")
        print("\n📋 First few records:")
        for i, row in enumerate(results[:3]):
            print(f"   Row {i+1}: {row[:5]}...")  # Show first 5 columns
    else:
        print("❌ No data retrieved from view")

    print("\n" + "=" * 60)

    # Method 2: Using DatabaseManager directly
    print("🔧 Method 2: Using DatabaseManager directly")

    db_manager = DatabaseManager()

    try:
        if db_manager.connect():
            # Create view
            if db_manager.create_view_latest_data('tb_toa_reporte_diario'):
                print("✅ View created successfully")

                # Query view
                view_results = db_manager.query_view('tb_toa_reporte_diario_latest', limit=5)
                if view_results:
                    print(f"✅ Retrieved {len(view_results)} records from view")
                else:
                    print("❌ No data in view")
            else:
                print("❌ Failed to create view")
        else:
            print("❌ Failed to connect to database")

    except Exception as e:
        print(f"❌ Error: {str(e)}")

    finally:
        db_manager.disconnect()

    print("\n" + "=" * 60)
    print("📝 SQL Query equivalent:")
    print("SELECT *, fecha_integracion AS fecha_intv2")
    print("FROM tb_toa_reporte_diario")
    print("WHERE fecha_integracion = (SELECT MAX(fecha_integracion) FROM tb_toa_reporte_diario)")
    print("=" * 60)

if __name__ == "__main__":
    main()