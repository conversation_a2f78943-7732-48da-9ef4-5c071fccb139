#!/usr/bin/env python3
"""
Script to clean up the test table for debugging purposes.
"""

import logging
from database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def cleanup_table():
    """Clean up the test table."""
    print("🧹 Cleaning up test table...")

    db_manager = DatabaseManager()
    if not db_manager.connect():
        print("❌ Database connection failed")
        return False

    try:
        table_name = 'tb_toa_reporte_diario'

        # Check if table exists
        if db_manager.table_exists(table_name):
            print(f"📋 Table '{table_name}' exists. Dropping...")

            # Get column count before dropping
            columns = db_manager.get_table_columns(table_name)
            print(f"📊 Table had {len(columns)} columns")

            # Drop the table
            if db_manager.drop_table(table_name):
                print("✅ Table dropped successfully")
                return True
            else:
                print("❌ Failed to drop table")
                return False
        else:
            print(f"📋 Table '{table_name}' does not exist")
            return True

    except Exception as e:
        print(f"❌ Error during cleanup: {str(e)}")
        return False

    finally:
        db_manager.disconnect()

def show_table_info():
    """Show information about the test table."""
    print("📊 Showing table information...")

    db_manager = DatabaseManager()
    if not db_manager.connect():
        print("❌ Database connection failed")
        return False

    try:
        table_name = 'tb_toa_reporte_diario'

        if db_manager.table_exists(table_name):
            columns = db_manager.get_table_columns(table_name)
            print(f"📋 Table '{table_name}' exists with {len(columns)} columns:")
            for i, col in enumerate(columns):
                print(f"   {i+1:2d}. {col}")
        else:
            print(f"📋 Table '{table_name}' does not exist")

        return True

    except Exception as e:
        print(f"❌ Error getting table info: {str(e)}")
        return False

    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_table()
    elif len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_table_info()
    else:
        print("Usage:")
        print("  python cleanup_table.py --cleanup    # Drop the test table")
        print("  python cleanup_table.py --info       # Show table information")
        print("\nCurrent table status:")
        show_table_info()