"""
Oracle Cloud Field Service Bot
Bot automatizado para navegar en Oracle Cloud Field Service usando Playwright
"""

from playwright.sync_api import sync_playwright, <PERSON>, Browser, BrowserContext
import time
import logging
from typing import Optional

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OracleCloudBot:
    def __init__(self, headless: bool = False, slow_mo: int = 1000, instance_pause_seconds: float = 3.0):
        """
        Inicializar el bot de Oracle Cloud

        Args:
            headless: Si ejecutar en modo headless (sin interfaz gráfica)
            slow_mo: Retraso en milisegundos entre acciones (para debugging)
        """
        self.headless = headless
        self.slow_mo = slow_mo
        # Inicializar atributos sin anotaciones (compatibilidad)
        self.browser = None
        self.context = None
        self.page = None
        # Pausa entre cada "instancia" / paso en segundos
        self.instance_pause_seconds = float(instance_pause_seconds)
        
    def start_browser(self):
        """Iniciar el navegador y crear una nueva página"""
        logger.info("Iniciando navegador...")
        
        playwright = sync_playwright().start()
        self.browser = playwright.chromium.launch(
            headless=self.headless,
            slow_mo=self.slow_mo
        )
        
        # Crear contexto con configuraciones adicionales
        self.context = self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        self.page = self.context.new_page()
        
        # Habilitar logging de console para debugging
        self.page.on("console", lambda msg: logger.debug(f"Console: {msg.text}"))
        
        logger.info("Navegador iniciado correctamente")
        
    def clear_sessions_and_cookies(self):
        """Limpiar sesiones y cookies para evitar conflictos"""
        logger.info("Limpiando sesiones y cookies...")
        
        try:
            # Limpiar cookies del contexto actual
            self.context.clear_cookies()
            
            # Limpiar almacenamiento local y de sesión
            self.page.evaluate("() => { localStorage.clear(); sessionStorage.clear(); }")
            
            logger.info("Sesiones y cookies limpiadas exitosamente")
            
        except Exception as e:
            logger.warning(f"Error al limpiar sesiones: {e}")
        
    def navigate_to_oracle_cloud(self):
        """Navegar a la página principal de Oracle Cloud Field Service"""
        logger.info("Navegando a Oracle Cloud Field Service...")
        
        try:
            self.page.goto("https://vtr.fs.ocs.oraclecloud.com/", wait_until='networkidle')
            logger.info(f"Página cargada: {self.page.title()}")
            
            # Verificar que estamos en la página correcta
            if "Oracle Field Service" not in self.page.title():
                raise Exception("No se pudo cargar la página de Oracle Field Service")
                
        except Exception as e:
            logger.error(f"Error al navegar a Oracle Cloud: {e}")
            raise
            
    def click_sso_button(self):
        """Hacer clic en el botón 'Conectarse con SSO'"""
        logger.info("Buscando y haciendo clic en el botón 'Conectarse con SSO'...")
        
        try:
            # Esperar a que el botón sea visible
            sso_button = self.page.wait_for_selector("button:has-text('Conectarse con SSO')", timeout=10000)
            
            if not sso_button:
                raise Exception("No se encontró el botón 'Conectarse con SSO'")
            
            # Hacer clic en el botón
            sso_button.click()
            logger.info("Botón 'Conectarse con SSO' clickeado exitosamente")
            
            # Esperar a que la página cambie
            self.page.wait_for_load_state('networkidle')
            time.sleep(2)  # Espera adicional para asegurar que la página se cargue completamente
            
        except Exception as e:
            logger.error(f"Error al hacer clic en el botón SSO: {e}")
            raise
            
    def fill_username_and_continue(self, username: str):
        """
        Llenar el campo de nombre de usuario y hacer clic en continuar
        
        Args:
            username: El nombre de usuario a ingresar
        """
        logger.info(f"Llenando el campo de usuario con: {username}")
        
        try:
            # Esperar a que la página se estabilice
            self.page.wait_for_load_state('networkidle', timeout=15000)
            self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            time.sleep(2)
            
            # Verificar qué tipo de página tenemos
            page_url = self.page.url
            page_title = self.page.title()
            logger.info(f"Página actual: {page_url} - Título: {page_title}")
            
            # Si ya estamos en Microsoft Login, saltar este paso
            if "login.microsoftonline.com" in page_url:
                logger.info("Ya estamos en Microsoft Login, saltando paso de usuario Oracle")
                return
            
            # Si la página muestra logout, navegar de nuevo
            if "Cerró la sesión" in self.page.content() or "logout" in page_url.lower():
                logger.warning("Detectada página de logout, navegando de nuevo...")
                self.navigate_to_oracle_cloud()
                self.click_sso_button()
                time.sleep(2)
            
            # Verificar si estamos en la pantalla principal o en la pantalla de SSO
            try:
                # Primero intentamos detectar el botón 'Conectarse con SSO' (pantalla principal)
                sso_button = self.page.wait_for_selector("button:has-text('Conectarse con SSO')", timeout=3000)
                if sso_button and sso_button.is_visible():
                    logger.info("Detectada pantalla principal. Haciendo clic en 'Conectarse con SSO'")
                    sso_button.click()
                    # Esperar a que cargue la pantalla de SSO
                    self.page.wait_for_load_state('networkidle', timeout=10000)
                    time.sleep(2)
            except Exception as e:
                logger.info(f"No se encontró botón 'Conectarse con SSO', probablemente ya estamos en la pantalla de SSO: {e}")
                
            # Ahora deberíamos estar en la pantalla de SSO con el campo de usuario
            try:
                # Buscar el campo de usuario con role=textbox y name=Nombre de usuario
                username_input = self.page.locator('input[placeholder="Nombre de usuario"]').first
                logger.info("Campo de usuario encontrado con locator específico")
            except Exception as e:
                logger.warning(f"Error al buscar campo de usuario con locator: {e}")
                # Intentar con Playwright roles
                try:
                    username_input = self.page.get_by_role('textbox', name='Nombre de usuario')
                    logger.info("Campo de usuario encontrado con get_by_role")
                except Exception as e2:
                    logger.warning(f"Error al buscar campo con get_by_role: {e2}")
                    # Último recurso: tomar captura y buscar cualquier input
                    self.take_screenshot("debug_find_username_field.png")
                    try:
                        username_input = self.page.locator('input').first
                        logger.info("Usando el primer input disponible")
                    except Exception as e3:
                        logger.error(f"No se pudo encontrar ningún campo de entrada: {e3}")
                        raise Exception("No se encontró campo de usuario")
            
            # Limpiar y llenar el campo
            if username_input:
                # Verificar si está visible antes de interactuar
                if username_input.is_visible():
                    username_input.fill('')  # Limpiar primero
                    username_input.type(username, delay=100)  # Escribir lentamente para mayor estabilidad
                    logger.info("Campo de usuario llenado correctamente")
                    time.sleep(1)  # Esperar a que se registre el valor
                else:
                    logger.warning("Campo de usuario encontrado pero no está visible")
                    self.take_screenshot("username_field_not_visible.png")
                    raise Exception("Campo de usuario no visible")
            
            # Verificar si el botón está habilitado (debiera estarlo después de escribir el usuario)
            try:
                # Buscar el botón 'Continuar con SSO'
                continue_button = self.page.locator("button:has-text('Continuar con SSO')").first
                
                # Verificar si está habilitado
                if continue_button.is_disabled():
                    logger.warning("Botón 'Continuar con SSO' deshabilitado, esperando un momento...")
                    time.sleep(3)  # Esperar más tiempo a que se habilite
                    # Verificar de nuevo
                    if continue_button.is_disabled():
                        # Si sigue deshabilitado, puede que el campo no tenga el foco o no se haya registrado
                        # Intentar hacer clic en el campo y escribir de nuevo
                        username_input.click()
                        username_input.fill('')
                        username_input.type(username, delay=100)
                        time.sleep(2)
                
                # Hacer clic en el botón (ahora debería estar habilitado)
                logger.info("Haciendo clic en 'Continuar con SSO'...")
                continue_button.click()
                
            except Exception as e:
                logger.error(f"Error al interactuar con el botón 'Continuar con SSO': {e}")
                self.take_screenshot("error_continue_button.png")
                raise
            
            # Esperar a la redirección a Microsoft
            try:
                # Esperar a que la URL cambie a Microsoft
                self.page.wait_for_url("**login.microsoftonline.com**", timeout=30000)
                logger.info("Redirección a Microsoft detectada por URL")
            except Exception as e:
                logger.warning(f"No se detectó cambio de URL a Microsoft: {e}")
                # Esperar manualmente
                time.sleep(5)
                # Verificar después de la espera
                if "login.microsoftonline.com" in self.page.url:
                    logger.info("Redirección a Microsoft exitosa después de espera manual")
                else:
                    logger.warning(f"No se redireccionó a Microsoft. URL actual: {self.page.url}")
                    self.take_screenshot("redirect_microsoft_failed.png")
                    raise Exception("Fallo en redirección a Microsoft")
            
            # Esperar a que la página de Microsoft cargue completamente
            self.page.wait_for_load_state('domcontentloaded', timeout=30000)
            self.page.wait_for_load_state('networkidle', timeout=30000)
            time.sleep(3)  # Espera adicional para asegurar carga completa
            
            # Verificar si hay una página de selección de cuenta
            try:
                # Buscar botón con el correo del usuario
                account_button = self.page.locator(f"button:has-text('{username}@VTR.CL')").first
                if account_button and account_button.is_visible():
                    logger.info(f"Detectada pantalla de selección de cuenta. Seleccionando {username}@VTR.CL")
                    account_button.click()
                    # Esperar a que cargue la pantalla de contraseña
                    self.page.wait_for_load_state('networkidle', timeout=10000)
                    time.sleep(2)
            except Exception as e:
                logger.info(f"No se encontró pantalla de selección de cuenta, probablemente ya estamos en pantalla de contraseña: {e}")
            
            # Verificación final
            logger.info(f"Completado paso de usuario. Página actual: {self.page.url}")
            self.take_screenshot("after_username_step.png")
            
        except Exception as e:
            logger.error(f"Error al llenar usuario y continuar: {e}")
            # Tomar captura de pantalla para debugging
            self.take_screenshot("error_step4_username.png")
            raise
            
    def take_screenshot(self, filename: str = "oracle_cloud_screenshot.png"):
        """Tomar una captura de pantalla de la página actual"""
        try:
            screenshot_path = f"c:\\Users\\<USER>\\Desktop\\aunclick_home_react\\{filename}"
            self.page.screenshot(path=screenshot_path)
            logger.info(f"Captura de pantalla guardada: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            logger.error(f"Error al tomar captura de pantalla: {e}")
            return None
            
    def fill_email_and_continue(self, email: str):
        """
        Llenar el campo de email y hacer clic en continuar
        
        Args:
            email: El email a ingresar
        """
        logger.info(f"Llenando el campo de email con: {email}")
        
        try:
            # Esperar a que la página cargue completamente
            self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            self.page.wait_for_load_state('networkidle', timeout=15000)
            time.sleep(2)
            
            # Verificar que estamos en la página de Microsoft Login
            if "login.microsoftonline.com" not in self.page.url:
                logger.warning(f"Página inesperada: {self.page.url}")
                self.take_screenshot("unexpected_page_email_step.png")
                raise Exception(f"No estamos en Microsoft Login: {self.page.url}")
            
            # Verificar si ya tenemos un campo de contraseña visible (pantalla de password directa)
            try:
                password_field = self.page.locator("input[type='password']").first
                if password_field and password_field.is_visible():
                    logger.info("Detectada pantalla de contraseña directamente, saltando paso de email")
                    return
            except Exception as e:
                logger.info(f"No se encontró campo de contraseña directo: {e}")
            
            # Intentar diferentes estrategias para encontrar el campo de email
            email_input = None
            
            # Estrategia 1: Usar locator con type='email'
            try:
                email_input = self.page.locator("input[type='email']").first
                if email_input and email_input.is_visible():
                    logger.info("Campo de email encontrado con type='email'")
                else:
                    email_input = None
            except Exception as e:
                logger.warning(f"No se encontró campo de email con type='email': {e}")
            
            # Estrategia 2: Usar get_by_role
            if not email_input:
                try:
                    email_input = self.page.get_by_role('textbox', name=lambda n: 'email' in n.lower() or 'correo' in n.lower())
                    if email_input and email_input.is_visible():
                        logger.info("Campo de email encontrado con get_by_role")
                    else:
                        email_input = None
                except Exception as e:
                    logger.warning(f"No se encontró campo de email con get_by_role: {e}")
            
            # Estrategia 3: Buscar cualquier input visible
            if not email_input:
                try:
                    # Tomar una captura para debug
                    self.take_screenshot("before_email_fallback.png")
                    # Usar el primer input visible
                    email_input = self.page.locator("input:visible").first
                    if email_input and email_input.is_visible():
                        logger.info("Usando primer input visible como campo de email")
                    else:
                        raise Exception("No se encontró ningún input visible")
                except Exception as e:
                    logger.error(f"No se pudo encontrar ningún campo de entrada para email: {e}")
                    self.take_screenshot("email_field_not_found.png")
                    raise Exception("No se encontró campo de email")
            
            # Limpiar y llenar el campo
            email_input.fill('')  # Limpiar primero
            email_input.type(email, delay=100)  # Escribir lentamente para mayor estabilidad
            logger.info("Campo de email llenado correctamente")
            time.sleep(1)  # Esperar a que se registre el valor
            
            # Buscar y hacer clic en el botón siguiente
            # Intentar diferentes estrategias para encontrar el botón
            next_button = None
            
            # Estrategia 1: Usar texto específico
            button_texts = ['Siguiente', 'Next', 'Continuar', 'Continue', 'Sign in']
            for text in button_texts:
                try:
                    next_button = self.page.get_by_role('button', name=text)
                    if next_button and next_button.is_visible():
                        logger.info(f"Botón encontrado con texto: {text}")
                        break
                except Exception:
                    continue
            
            # Estrategia 2: Usar selector de tipo submit
            if not next_button:
                try:
                    next_button = self.page.locator("button[type='submit']").first
                    if next_button and next_button.is_visible():
                        logger.info("Botón encontrado con type='submit'")
                    else:
                        next_button = None
                except Exception as e:
                    logger.warning(f"No se encontró botón submit: {e}")
            
            # Si no encontramos botón, intentar con Enter
            if not next_button:
                logger.warning("No se encontró botón siguiente, intentando con Enter")
                self.take_screenshot("no_next_button_found.png")
                email_input.press('Enter')
                logger.info("Presionado Enter en campo de email")
            else:
                # Hacer clic en el botón
                logger.info("Haciendo clic en botón Siguiente...")
                next_button.click()
            
            # Esperar a que ocurra la navegación o cambio en la página
            try:
                # Esperar a que aparezca el campo de contraseña
                self.page.wait_for_selector("input[type='password']", timeout=10000)
                logger.info("Campo de contraseña detectado después de navegación")
            except Exception as e:
                logger.warning(f"No se detectó campo de contraseña después de navegación: {e}")
                # Esperar manualmente
                time.sleep(5)
            
            # Esperar a que la página procese la solicitud
            self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            self.page.wait_for_load_state('networkidle', timeout=15000)
            
            # Verificación final
            logger.info(f"Completado paso de email. Página actual: {self.page.url}")
            self.take_screenshot("after_email_step.png")
            
        except Exception as e:
            logger.error(f"Error al llenar email y continuar: {e}")
            self.take_screenshot("error_step5_email.png")
            raise
            
    def fill_password_and_login(self, password: str):
        """
        Llenar el campo de contraseña y hacer clic en iniciar sesión
        
        Args:
            password: La contraseña a ingresar
        """
        logger.info("Llenando el campo de contraseña...")
        
        try:
            # Esperar a que la página cargue completamente
            self.page.wait_for_load_state('domcontentloaded', timeout=15000)
            self.page.wait_for_load_state('networkidle', timeout=15000)
            time.sleep(2)
            
            # Verificar que estamos en la página de Microsoft Login
            if "login.microsoftonline.com" not in self.page.url:
                logger.warning(f"Página inesperada para paso de contraseña: {self.page.url}")
                self.take_screenshot("unexpected_page_password_step.png")
            
            # Tomar una captura para debug
            self.take_screenshot("before_password_step.png")
            
            # Buscar el input de contraseña con múltiples estrategias
            password_input = None
            
            # Estrategia 1: Usar selector de tipo password
            try:
                password_input = self.page.locator("input[type='password']").first
                if password_input and password_input.is_visible():
                    logger.info("Campo de contraseña encontrado con type='password'")
                else:
                    password_input = None
            except Exception as e:
                logger.warning(f"No se encontró campo de contraseña con type='password': {e}")
            
            # Estrategia 2: Usar get_by_role
            if not password_input:
                try:
                    password_input = self.page.get_by_role('textbox', name=lambda n: 'password' in n.lower() or 'contraseña' in n.lower())
                    if password_input and password_input.is_visible():
                        logger.info("Campo de contraseña encontrado con get_by_role")
                    else:
                        password_input = None
                except Exception as e:
                    logger.warning(f"No se encontró campo de contraseña con get_by_role: {e}")
            
            # Estrategia 3: Último recurso - buscar cualquier input visible que no sea el email
            if not password_input:
                try:
                    # Tomar una captura para debug
                    self.take_screenshot("password_field_fallback.png")
                    # Probar con todos los inputs visibles
                    inputs = self.page.locator("input:visible").all()
                    for input_elem in inputs:
                        # Descartar el input que contenga el email
                        if "@" not in input_elem.input_value():
                            password_input = input_elem
                            logger.info("Usando input alternativo como campo de contraseña")
                            break
                        
                    if not password_input:
                        raise Exception("No se encontró ningún input adecuado para contraseña")
                except Exception as e:
                    logger.error(f"No se pudo encontrar ningún campo para contraseña: {e}")
                    self.take_screenshot("password_field_not_found.png")
                    raise Exception("No se encontró campo de contraseña")
            
            # Limpiar y llenar el campo
            password_input.fill('')  # Limpiar primero
            password_input.type(password, delay=100)  # Escribir lentamente para mayor estabilidad
            logger.info("Campo de contraseña llenado correctamente")
            time.sleep(1)  # Esperar a que se registre el valor
            
            # Buscar y hacer clic en el botón de iniciar sesión
            # Intentar diferentes estrategias para encontrar el botón
            login_button = None
            
            # Estrategia 1: Usar texto específico
            button_texts = ['Sign in', 'Iniciar sesión', 'Iniciar', 'Login', 'Ingresar']
            for text in button_texts:
                try:
                    login_button = self.page.get_by_role('button', name=text)
                    if login_button and login_button.is_visible():
                        logger.info(f"Botón de login encontrado con texto: {text}")
                        break
                except Exception:
                    continue
            
            # Estrategia 2: Usar selector de tipo submit
            if not login_button:
                try:
                    login_button = self.page.locator("button[type='submit']").first
                    if login_button and login_button.is_visible():
                        logger.info("Botón de login encontrado con type='submit'")
                    else:
                        login_button = None
                except Exception as e:
                    logger.warning(f"No se encontró botón submit: {e}")
            
            # Si no encontramos botón, intentar con Enter
            if not login_button:
                logger.warning("No se encontró botón de login, intentando con Enter")
                self.take_screenshot("no_login_button_found.png")
                password_input.press('Enter')
                logger.info("Presionado Enter en campo de contraseña")
            else:
                # Hacer clic en el botón
                logger.info("Haciendo clic en botón de login...")
                login_button.click()
            
            # Esperar a que ocurra la navegación o cambio en la página
            # Tenemos que manejar múltiples posibles estados después del login
            time.sleep(3)  # Esperar un poco para que inicie la navegación
            
            # Esperar a que la página procese el login
            try:
                self.page.wait_for_load_state('domcontentloaded', timeout=30000)
                self.page.wait_for_load_state('networkidle', timeout=30000)
            except Exception as e:
                logger.warning(f"Error esperando carga de página: {e}")
                time.sleep(5)  # Esperar manualmente
            
            # Verificar si aparece la pregunta "Mantener sesión iniciada"
            try:
                # Buscar texto similar a "Stay signed in", "Mantener sesión iniciada", etc.
                heading = self.page.get_by_role('heading', name=lambda n: 'sign' in n.lower() or 'sesión' in n.lower())
                
                if heading and heading.is_visible():
                    logger.info(f"Detectado diálogo: {heading.text_content()}")
                    
                    # Buscar botón "Sí" o "Yes"
                    yes_button = self.page.get_by_role('button', name=lambda n: n.lower() in ['yes', 'sí', 'si'])
                    
                    if yes_button and yes_button.is_visible():
                        logger.info("Haciendo clic en 'Sí'/'Yes'")
                        yes_button.click()
                        
                        # Esperar a que Oracle Field Service cargue completamente
                        try:
                            self.page.wait_for_url("**vtr.fs.ocs.oraclecloud.com**", timeout=60000)
                            logger.info("Redirección a Oracle detectada")
                        except Exception as e:
                            logger.warning(f"No se detectó redirección a Oracle: {e}")
                            time.sleep(10)  # Esperar más tiempo
                        
                        self.page.wait_for_load_state('domcontentloaded', timeout=60000)
                        self.page.wait_for_load_state('networkidle', timeout=60000)
                    else:
                        logger.warning("No se encontró botón 'Sí'/'Yes'")
                        self.take_screenshot("no_yes_button_found.png")
            except Exception as e:
                logger.info(f"No se detectó diálogo de mantener sesión: {e}")
            
            # Verificación final del login
            logger.info(f"Login completado - Página final: {self.page.url}")
            logger.info(f"Título de página: {self.page.title()}")
            self.take_screenshot("login_completed.png")
            
            # Verificar que estamos en Oracle (si no hay diálogo intermedio, debemos estar ya en Oracle)
            if "vtr.fs.ocs.oraclecloud.com" not in self.page.url:
                logger.warning(f"No llegamos a Oracle después del login. URL actual: {self.page.url}")
                self.take_screenshot("login_unexpected_destination.png")
            
        except Exception as e:
            logger.error(f"Error al llenar contraseña y hacer login: {e}")
            self.take_screenshot("error_step6_password.png")
            raise
    # Fin de fill_password_and_login
            
    def close_browser(self):
        """Cerrar el navegador y limpiar recursos"""
        logger.info("Cerrando navegador...")
        
        if self.page:
            self.page.close()
        if self.context:
            self.context.close()
        if self.browser:
            self.browser.close()
            
        logger.info("Navegador cerrado correctamente")

    def get_page_info(self):
        """Obtener información actual de la página (URL, título y fragmento del body)."""
        try:
            snippet = ""
            if self.page:
                body = self.page.locator('body').inner_text()
                snippet = (body[:200] + "...") if body else ""

            info = {
                'url': self.page.url if self.page else None,
                'title': self.page.title() if self.page else None,
                'content_snippet': snippet
            }
            logger.info(f"Información de página: URL={info['url']}, Título={info['title']}")
            return info
        except Exception as e:
            logger.error(f"Error al obtener información de página: {e}")
            return None
        
    def run_automation(self, username: str, email: str, password: str):
        """
        Ejecutar el flujo completo de automatización con autenticación SSO
        
        Args:
            username: El nombre de usuario para el SSO
            email: El email para la autenticación
            password: La contraseña para la autenticación
        """
        try:
            # Paso 1: Iniciar navegador
            self.start_browser()
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")

            # Paso 2: Navegar a Oracle Cloud
            self.navigate_to_oracle_cloud()
            
            # Paso 2.5: Limpiar sesiones para evitar conflictos
            self.clear_sessions_and_cookies()
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")

            # Paso 3: Hacer clic en "Conectarse con SSO"
            self.click_sso_button()
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")

            # Paso 4: Llenar usuario y continuar
            self.fill_username_and_continue(username)
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")

            # Paso 5: Llenar email y continuar
            self.fill_email_and_continue(email)
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")

            # Paso 6: Llenar contraseña y hacer login
            self.fill_password_and_login(password)
            time.sleep(self.instance_pause_seconds)
            logger.info(f"Pausa de {self.instance_pause_seconds}s antes del siguiente paso")
            
            # Paso 7: Tomar captura de pantalla del resultado
            self.take_screenshot("oracle_login_success.png")
            
            # Paso 8: Obtener información de la página final
            page_info = self.get_page_info()
            
            logger.info("Automatización completada exitosamente")
            return page_info
            
        except Exception as e:
            logger.error(f"Error en la automatización: {e}")
            # Tomar captura de pantalla del error
            self.take_screenshot("oracle_error_screenshot.png")
            raise
        finally:
            # Siempre cerrar el navegador
            self.close_browser()


def main():
    """Función principal para ejecutar el bot"""
    
    # Configuración del bot
    bot = OracleCloudBot(
        headless=False,  # Cambiar a True para modo headless
        slow_mo=1500     # Retraso entre acciones para ver qué está pasando
    )
    
    # Credenciales para la automatización
    username = "ncornejoh"           # Usuario SSO
    email = "<EMAIL>"       # Email corporativo
    password = "Telqway.202517"      # Contraseña
    
    try:
        result = bot.run_automation(username, email, password)
        print("✅ Automatización completada exitosamente")
        print(f"📄 Información final: {result}")
        
    except Exception as e:
        print(f"❌ Error en la automatización: {e}")


if __name__ == "__main__":
    main()