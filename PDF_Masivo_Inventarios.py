from selenium import webdriver 
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from sqlalchemy import create_engine, text
import time

try:
    print("Iniciando el proceso...")
    
    # Configurar opciones de Edge
    edge_options = Options()
    edge_options.add_argument('--start-maximized')
    edge_options.add_argument('--disable-gpu')
    edge_options.add_argument('--no-sandbox')
    edge_options.add_argument('--disable-dev-shm-usage')
    # Desactivar los mensajes de DevTools
    edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    
    print("Configurando el driver...")
    service = Service(EdgeChromiumDriverManager().install())
    driver = webdriver.Edge(service=service, options=edge_options)
    print("Driver configurado exitosamente")
    
    # Configurar tiempo de espera implícito
    driver.implicitly_wait(10)
    
    print("Conectando a la base de datos...")
    engineMYsql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)

    id_cierre = '67bd9fc254'

    
    query = text("""
        SELECT DISTINCT tut.rut
        FROM tb_cierre_inventario_faltante tlci
        LEFT JOIN tb_user_tqw tut ON tut.Nombre_short = tlci.id_tecnico
        WHERE ID_CIERRE = :id_cierre
          AND tut.rut IS NOT NULL
                         and vigente = 'Si'
          AND id_tecnico NOT IN ('INGRESO MANUAL', 'ANALISIS', 'DISPONIBLE')
             
    """)
    
    print("Ejecutando consulta SQL...")
    with engineMYsql.connect() as connection:
        result = connection.execute(query, {'id_cierre': id_cierre})
        ruts = [row[0] for row in result]
    
    print(f"Se encontraron {len(ruts)} RUTs para procesar")
    
    # Procesar cada URL
    for index, rut in enumerate(ruts, 1):
        try:
            url = f"https://appoperaciones.telqway.cl/APP_TQW/dist/uploadCSV_PDF.php?rut={rut}&id_inventario={id_cierre}"
            print(f"[{index}/{len(ruts)}] Procesando RUT {rut}")
            print(f"Accediendo a URL: {url}")
            
            driver.get(url)
            print(f"URL cargada exitosamente para RUT {rut}")
            
            # Esperar y verificar que la página se cargó
            time.sleep(5)
            print(f"Título de la página: {driver.title}")
            print(f"Completado RUT {rut}\n")
            
        except Exception as e:
            print(f"Error procesando RUT {rut}: {str(e)}")
            continue

except Exception as e:
    print(f"Error general en el proceso: {str(e)}")
finally:
    if 'driver' in locals():
        print("Cerrando el driver...")
        driver.quit()
        print("Driver cerrado")

print("Proceso finalizado")