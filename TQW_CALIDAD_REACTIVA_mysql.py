

import pandas as pd 
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Col<PERSON>n, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import os 
import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

from sqlalchemy import text
import sqlalchemy





engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@181.212.32.10/master?driver=ODBC+Driver+17+for+SQL+Server')
Session = sessionmaker(bind=engine)
session = Session()


session.execute("exec SP_INSERT_CALIDAD_REACTIVA 0") 
session.commit()


session.execute(" EXEC SP_FACTURA_2023 '202401'")
session.commit()


session.execute("EXEC SP_TQW_COMISION_2023 '202401'")
session.commit()



# Crear un data frame a partir de una consulta a SQL Server
Data = pd.read_sql_query("SELECT * 
FROM vw_CalidadFlujo 
WHERE FORMAT(MES_CONTABLE, 'yyyyMM') >= '202404') ", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('tb_py_Flujo_Calidad', engineMYsql, if_exists='replace',index=False)










