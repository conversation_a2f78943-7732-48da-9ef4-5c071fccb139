"""
Script simple para probar el bot de Oracle Cloud paso a paso
"""

from playwright.sync_api import sync_playwright

def test_oracle_cloud_step_by_step():
    """Probar cada paso del bot individualmente con flujo completo de autenticación"""
    
    with sync_playwright() as p:
        # Configurar navegador
        browser = p.chromium.launch(headless=False, slow_mo=2000)
        page = browser.new_page()
        
        try:
            print("🌐 Paso 1: Navegando a Oracle Cloud...")
            page.goto("https://vtr.fs.ocs.oraclecloud.com/")
            print(f"✅ Página cargada: {page.title()}")
            
            input("Presiona Enter para continuar al paso 2...")
            
            print("🔘 Paso 2: Buscando botón 'Conectarse con SSO'...")
            sso_button = page.locator("button:has-text('Conectarse con SSO')")
            
            if sso_button.is_visible():
                print("✅ Botón 'Conectarse con SSO' encontrado")
                sso_button.click()
                print("✅ Botón clickeado")
            else:
                print("❌ No se encontró el botón 'Conectarse con SSO'")
                return
            
            input("Presiona Enter para continuar al paso 3...")
            
            print("📝 Paso 3: Llenando campo de usuario con 'ncornejoh'...")
            username_input = page.locator("input[type='text'], textbox").first
            
            if username_input.is_visible():
                print("✅ Campo de usuario encontrado")
                username_input.fill("ncornejoh")
                print("✅ Usuario 'ncornejoh' ingresado")
            else:
                print("❌ No se encontró el campo de usuario")
                return
            
            input("Presiona Enter para continuar al paso 4...")
            
            print("▶️ Paso 4: Haciendo clic en 'Continuar con SSO'...")
            continue_button = page.locator("button:has-text('Continuar con SSO')")
            
            if continue_button.is_visible() and not continue_button.is_disabled():
                continue_button.click()
                print("✅ Botón 'Continuar con SSO' clickeado")
                
                # Esperar a que cargue la nueva página
                page.wait_for_load_state('networkidle')
                print(f"📄 Nueva página: {page.url}")
                print(f"📋 Título: {page.title()}")
            else:
                print("❌ No se pudo hacer clic en 'Continuar con SSO'")
                return
            
            input("Presiona Enter para continuar al paso 5...")
            
            print("📧 Paso 5: Buscando campo de email y llenando con '<EMAIL>'...")
            # Esperar a que aparezca el input de email
            page.wait_for_timeout(3000)  # Esperar 3 segundos
            
            # Buscar el input de email (puede ser de varios tipos)
            email_input = page.locator("input[type='email'], input[type='text'], input[placeholder*='correo'], input[placeholder*='email'], textbox").first
            
            if email_input.is_visible():
                print("✅ Campo de email encontrado")
                email_input.fill("<EMAIL>")
                print("✅ Email '<EMAIL>' ingresado")
            else:
                print("❌ No se encontró el campo de email")
                # Intentar con selector más genérico
                email_input = page.locator("input").first
                if email_input.is_visible():
                    print("✅ Campo de input genérico encontrado")
                    email_input.fill("<EMAIL>")
                    print("✅ Email '<EMAIL>' ingresado")
                else:
                    print("❌ No se encontró ningún campo de input")
                    return
            
            input("Presiona Enter para continuar al paso 6...")
            
            print("➡️ Paso 6: Buscando y haciendo clic en botón siguiente...")
            # Buscar botón siguiente (puede tener varios textos)
            next_button = page.locator("button:has-text('Siguiente'), button:has-text('Continuar'), button:has-text('Next'), button[type='submit']").first
            
            if next_button.is_visible():
                next_button.click()
                print("✅ Botón siguiente clickeado")
                
                # Esperar a que cargue la nueva página
                page.wait_for_load_state('networkidle')
                print(f"📄 Nueva página: {page.url}")
                print(f"📋 Título: {page.title()}")
            else:
                print("❌ No se encontró el botón siguiente")
                return
            
            input("Presiona Enter para continuar al paso 7...")
            
            print("🔐 Paso 7: Buscando campo de contraseña y llenando con 'Telqway.202517'...")
            # Esperar a que aparezca el input de contraseña
            page.wait_for_timeout(3000)  # Esperar 3 segundos
            
            # Buscar el input de contraseña
            password_input = page.locator("input[type='password'], input[placeholder*='contraseña'], input[placeholder*='password']").first
            
            if password_input.is_visible():
                print("✅ Campo de contraseña encontrado")
                password_input.fill("Telqway.202517")
                print("✅ Contraseña ingresada")
            else:
                print("❌ No se encontró el campo de contraseña")
                # Intentar con input genérico
                password_input = page.locator("input").first
                if password_input.is_visible():
                    print("✅ Campo de input genérico encontrado")
                    password_input.fill("Telqway.202517")
                    print("✅ Contraseña ingresada")
                else:
                    print("❌ No se encontró ningún campo de input")
                    return
            
            input("Presiona Enter para continuar al paso 8...")
            
            print("🚀 Paso 8: Buscando y haciendo clic en botón 'Iniciar sesión'...")
            # Buscar botón de iniciar sesión
            login_button = page.locator("button:has-text('Iniciar sesión'), button:has-text('Iniciar'), button:has-text('Sign in'), button:has-text('Login'), button[type='submit']").first
            
            if login_button.is_visible():
                login_button.click()
                print("✅ Botón 'Iniciar sesión' clickeado")
                
                # Esperar a que cargue la nueva página
                page.wait_for_load_state('networkidle')
                print(f"📄 Página final: {page.url}")
                print(f"📋 Título final: {page.title()}")
                
                # Tomar captura de pantalla del resultado
                page.screenshot(path="oracle_login_result.png")
                print("📸 Captura de pantalla guardada: oracle_login_result.png")
                
            else:
                print("❌ No se encontró el botón 'Iniciar sesión'")
            
            input("✅ Proceso completo! Presiona Enter para cerrar...")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            # Tomar captura de pantalla del error
            page.screenshot(path="oracle_error.png")
            print("📸 Captura de error guardada: oracle_error.png")
            
        finally:
            browser.close()

if __name__ == "__main__":
    test_oracle_cloud_step_by_step()