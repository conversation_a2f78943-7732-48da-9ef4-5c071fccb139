#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import requests
import logging
import os
import socket
import traceback

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conectividad_test.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_internet_connection():
    """Verificar si hay conexión a Internet"""
    hosts = ['www.google.com', 'www.microsoft.com', 'www.cloudflare.com']
    
    for host in hosts:
        try:
            # Intentar resolver el DNS
            socket.gethostbyname(host)
            logger.info(f"✓ Resolución DNS exitosa para {host}")
            return True
        except:
            logger.warning(f"✗ No se pudo resolver DNS para {host}")
    
    logger.error("No hay conexión a Internet o hay problemas de DNS")
    return False

def check_proxy_settings():
    """Verificar la configuración de proxy del sistema"""
    proxy_env_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'no_proxy', 'NO_PROXY']
    
    logger.info("Verificando configuración de proxy:")
    for var in proxy_env_vars:
        value = os.environ.get(var)
        if value:
            logger.info(f"✓ {var} = {value}")
        else:
            logger.info(f"✗ {var} no está configurado")

def test_edge_driver_connection():
    """Probar conexión específica a los servidores de msedgedriver"""
    urls = [
        # URLs antiguas (ya no funcionan)
        "https://msedgedriver.azureedge.net/LATEST_STABLE",
        "https://msedgedriver.azureedge.net/LATEST_RELEASE",
        # URL de almacenamiento actual
        "https://msedgewebdriverstorage.z22.web.core.windows.net",
        # URL oficial de descarga
        "https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/"
    ]
    
    logger.info("Probando conexión a servidores de Microsoft Edge WebDriver:")
    
    for url in urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                logger.info(f"✓ Conexión exitosa a {url}")
            else:
                logger.warning(f"✗ Respuesta inválida de {url}: {response.status_code}")
        except Exception as e:
            logger.error(f"✗ Error conectando a {url}: {str(e)}")

def test_webdriver_manager():
    """Probar funcionamiento de webdriver-manager sin instalar"""
    try:
        from webdriver_manager.microsoft import EdgeChromiumDriverManager
        
        logger.info("Probando webdriver-manager:")
        
        # Solo verificar la URL sin descargar
        edge_manager = EdgeChromiumDriverManager()
        driver_url = edge_manager.driver_url
        
        logger.info(f"URL del driver: {driver_url}")
        
        # Verificar las variables usadas por webdriver-manager
        logger.info("Variables de webdriver-manager:")
        logger.info(f"Driver cache dir: {os.environ.get('WDM_CACHE_DIR', 'No configurado')}")
        logger.info(f"SSL verify: {os.environ.get('WDM_SSL_VERIFY', 'No configurado')}")
        logger.info(f"Proxy URL: {os.environ.get('WDM_PROXY', 'No configurado')}")
        
    except ImportError:
        logger.error("No se pudo importar webdriver-manager")
    except Exception as e:
        logger.error(f"Error al probar webdriver-manager: {str(e)}")
        logger.error(traceback.format_exc())

def main():
    """Función principal para probar la conectividad"""
    logger.info("=== INICIO DE PRUEBAS DE CONECTIVIDAD ===")
    
    # Verificar conexión a Internet
    internet_connection = check_internet_connection()
    
    # Verificar configuración de proxy
    check_proxy_settings()
    
    # Probar conexión a servidores de Edge WebDriver
    if internet_connection:
        test_edge_driver_connection()
    else:
        logger.warning("Omitiendo prueba de conexión a servidores de Edge WebDriver debido a falta de conexión a Internet")
    
    # Probar webdriver-manager
    test_webdriver_manager()
    
    logger.info("\n=== DIAGNÓSTICO ===")
    if internet_connection:
        logger.info("Conexión a Internet: DISPONIBLE")
        logger.info("""
Posibles causas del problema:
1. Firewall o política de seguridad bloqueando conexión a msedgedriver.azureedge.net
2. Proxy mal configurado o no funcional
3. Problemas temporales con los servidores de Microsoft
4. Caché corrupto de webdriver-manager

Soluciones:
1. Verificar si otros sitios web funcionan pero msedgedriver.azureedge.net está bloqueado
2. Configurar correctamente las variables de entorno HTTP_PROXY y HTTPS_PROXY
3. Esperar e intentar más tarde, o utilizar el script de descarga manual
4. Limpiar caché: borrar carpeta ~/.wdm/ o C:\\Users\\<USER>\\.wdm\\
""")
    else:
        logger.info("Conexión a Internet: NO DISPONIBLE")
        logger.info("""
No hay conexión a Internet o hay problemas de DNS. Soluciones:
1. Verificar conexión a Internet
2. Verificar configuración DNS
3. Utilizar el script de descarga manual cuando se restablezca la conexión
""")
    
    logger.info("=== FIN DE PRUEBAS DE CONECTIVIDAD ===")

if __name__ == "__main__":
    main()