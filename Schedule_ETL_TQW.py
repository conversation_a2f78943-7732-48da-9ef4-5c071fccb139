import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import os
import json
import logging
import schedule
import time
from datetime import datetime, timedelta
import sys
from pathlib import Path
import getpass

class ETLScheduler:
    def __init__(self):
        # Configuración básica
        self.user = getpass.getuser()
        self.base_path = self.get_base_path()
        self.setup_logging()
        self.tasks = {}
        self.running_tasks = set()
        self.max_concurrent_tasks = 3
        
        # <PERSON>gar tareas
        self.load_tasks()
        
        # Configurar interfaz
        self.setup_gui()
        
        # Iniciar programador
        self.start_scheduler()
    
    def get_base_path(self):
        """Determina la ruta base para los scripts"""
        # Siempre usar el directorio donde está este script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        return script_dir
    
    def setup_logging(self):
        """Configura el sistema de logs"""
        self.log_dir = os.path.join(self.base_path, "logs")
        os.makedirs(self.log_dir, exist_ok=True)
        
        log_file = os.path.join(self.log_dir,
f"scheduler_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            filename=log_file,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        self.logger = logging.getLogger('ETLScheduler')
        
        # También enviar logs a consola
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        self.logger.info(f"ETL Scheduler iniciado - Usuario: {self.user}")
        self.logger.info(f"Directorio base: {self.base_path}")
    
    def load_tasks(self):
        """Carga las tareas desde el archivo de configuración o crea una configuración por
defecto"""
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)),
"scheduler_config.json")
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                
                # Cargar tareas desde el archivo
                for category in config.get("tasks", {}):
                    for name, task_data in config["tasks"][category].items():
                        task_data["name"] = name
                        self.tasks[name] = self.create_task_from_dict(task_data)
                
                self.logger.info(f"Configuración cargada: {len(self.tasks)} tareas")
                
                # Asegurar que FlujoMysql_i tenga el intervalo correcto de 30 minutos
                if "FlujoMysql_i" in self.tasks:
                    flujo_task = self.tasks["FlujoMysql_i"]
                    if flujo_task["interval"] != 30:
                        self.logger.warning(f"Corrigiendo intervalo de FlujoMysql_i: cambiando de {flujo_task['interval']} a 30 minutos")
                        flujo_task["interval"] = 30
                        flujo_task["execution_time"] = None  # Asegurar que no esté como tarea diaria
                        self.save_tasks()  # Guardar cambios
                
                # Verificar que PyTOA30 tenga su ventana de tiempo configurada
                if "PyTOA30" in self.tasks:
                    pytoa30_task = self.tasks["PyTOA30"]
                    if "time_window" not in pytoa30_task:
                        self.logger.info("Configurando ventana de tiempo para PyTOA30...")
                        pytoa30_task["time_window"] = {
                            "start": "11:00",
                            "end": "20:30"
                        }
                        self.save_tasks()
            else:
                self.create_default_tasks()
        except Exception as e:
            self.logger.error(f"Error al cargar tareas: {e}")
            self.create_default_tasks()
    
    def create_task_from_dict(self, data):
        """Crea un objeto de tarea a partir de un diccionario"""
        task = {
            "name": data["name"],
            "path": data["path"],
            "status": data.get("status", "pending"),
            "last_run": None,
            "last_duration": None,
            "execution_start": None,
            "execution_end": None,
            "execution_time_real": None,
            "next_execution": None,
            "process_pid": None,
            "success_count": data.get("success_count", 0),
            "failure_count": data.get("failure_count", 0),
            "max_runtime": data.get("max_runtime", 300),
            "priority": data.get("priority", 1),
            "retries": data.get("retries", 2)
        }
        
        # Convertir cadena de fecha a objeto datetime si existe
        if "last_run" in data and data["last_run"]:
            try:
                task["last_run"] = datetime.fromisoformat(data["last_run"])
            except:
                pass
        
        # Añadir configuración de programación
        if "execution_time" in data:
            try:
                hours, minutes = map(int, data["execution_time"].split(":"))
                task["execution_time"] = f"{hours:02d}:{minutes:02d}"
            except:
                task["execution_time"] = None
        else:
            task["execution_time"] = None
        
        if "interval" in data:
            task["interval"] = data["interval"]
        else:
            task["interval"] = None
        
        # Añadir configuración de ventana de tiempo si existe
        if "time_window" in data:
            task["time_window"] = data["time_window"]
        
        return task
    
    def create_default_tasks(self):
        """Crea una configuración por defecto"""
        self.logger.info("Creando configuración por defecto")
        self.tasks = {}
        
        # Tareas diarias
        daily_tasks = {
            "ReporteOtDigital": {"time": "11:40"},
            "PyTOA_Flujo23": {"time": "05:40"},
            "PyTOAFinal": {"time": "05:30"},
            "CalidadNaranja_11AM": {"time": "11:00"},
            "CalidadNaranja_3PM": {"time": "15:00"},
            "CalidadNaranja_6PM": {"time": "18:00"},
            "CalidadNew23": {"time": "11:00"},
            "PyDesafioTecnico": {"time": "10:00"},
            "PyDesafioTecnico2": {"time": "16:00"},
            "PyAuditoriaTerreno_9am": {"time": "09:00"},
            "PyAuditoriaTerreno_2pm": {"time": "14:00"},
            "PyAuditoriaTerreno_7pm": {"time": "19:00"}
        }
        
        for name, info in daily_tasks.items():
            file_name = name.split("_")[0] + ".py" if "_" in name else name + ".py"
            self.tasks[name] = {
                "name": name,
                "path": os.path.join(self.base_path, file_name),
                "execution_time": info["time"],
                "interval": None,
                "status": "pending",
                "last_run": None,
                "last_duration": None,
                "execution_start": None,
                "execution_end": None,
                "execution_time_real": None,
                "next_execution": None,
                "process_pid": None,
                "success_count": 0,
                "failure_count": 0
            }
        
        # Tareas cada 120 minutos
        interval_120_tasks = ["NdcBot", "Py_INSERT_SQL_SERVER"]
        for name in interval_120_tasks:
            self.tasks[name] = {
                "name": name,
                "path": os.path.join(self.base_path, name + ".py"),
                "execution_time": None,
                "interval": 120,
                "status": "pending",
                "last_run": None,
                "last_duration": None,
                "execution_start": None,
                "execution_end": None,
                "execution_time_real": None,
                "next_execution": None,
                "process_pid": None,
                "success_count": 0,
                "failure_count": 0
            }
            
        # Tarea PyTOA30 que se ejecuta cada 60 minutos entre 11:00 y 20:30
        self.tasks["PyTOA30"] = {
            "name": "PyTOA30",
            "path": os.path.join(self.base_path, "PyTOA30.py"),
            "execution_time": None,
            "interval": 60,  # Una vez por hora
            "status": "pending",
            "last_run": None,
            "last_duration": None,
            "execution_start": None,
            "execution_end": None,
            "execution_time_real": None,
            "next_execution": None,
            "process_pid": None,
            "success_count": 0,
            "failure_count": 0,
            "max_runtime": 600,  # 10 minutos máximo para PyTOA30
            "time_window": {
                "start": "11:00",  # Desde las 11:00 AM
                "end": "20:30"    # Hasta las 8:30 PM
            }
        }
        
        # Tareas cada 30 minutos (excluyendo PyTOA30 que ya está configurado con ventana de tiempo)
        interval_30_tasks = ["FlujoMysql_i", "PyTurnos", "PyLogisticaMat"]
        for name in interval_30_tasks:
            file_name = name + ".py"
            self.tasks[name] = {
                "name": name,
                "path": os.path.join(self.base_path, file_name),
                "execution_time": None,
                "interval": 30,
                "status": "pending",
                "last_run": None,
                "last_duration": None,
                "execution_start": None,
                "execution_end": None,
                "execution_time_real": None,
                "next_execution": None,
                "process_pid": None,
                "success_count": 0,
                "failure_count": 0
            }
        
        # Guardar configuración
        self.save_tasks()
    
    def save_tasks(self):
        """Guarda las tareas en el archivo de configuración"""
        try:
            config = {
                "tasks": {
                    "daily": {},
                    "interval": {},
                    "manual": {}
                }
            }
            
            for name, task in self.tasks.items():
                task_dict = {
                    "name": task["name"],
                    "path": task["path"],
                    "status": task["status"],
                    "success_count": task["success_count"],
                    "failure_count": task["failure_count"],
                    "max_runtime": task.get("max_runtime", 300),
                    "priority": task.get("priority", 1),
                    "retries": task.get("retries", 2)
                }
                
                if task["last_run"]:
                    task_dict["last_run"] = task["last_run"].isoformat()
                
                if task["last_duration"]:
                    task_dict["last_duration"] = task["last_duration"]
                
                if task["execution_time"]:
                    task_dict["execution_time"] = task["execution_time"]
                    config["tasks"]["daily"][name] = task_dict
                elif task["interval"]:
                    task_dict["interval"] = task["interval"]
                    # Añadir configuración de ventana de tiempo si existe
                    if "time_window" in task:
                        task_dict["time_window"] = task["time_window"]
                    config["tasks"]["interval"][name] = task_dict
                else:
                    config["tasks"]["manual"][name] = task_dict
            
            config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)),
"scheduler_config.json")
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            self.logger.info(f"Configuración guardada en {config_file}")
        except Exception as e:
            self.logger.error(f"Error al guardar configuración: {e}")
    
    def execute_task(self, task_name):
        """Ejecuta una tarea"""
        if task_name not in self.tasks or task_name in self.running_tasks:
            return
        
        task = self.tasks[task_name]
        
        # Verificar ventana de tiempo para PyTOA30
        if task_name == "PyTOA30" and "time_window" in task:
            current_time = datetime.now().time()
            start_time = datetime.strptime(task["time_window"]["start"], "%H:%M").time()
            end_time = datetime.strptime(task["time_window"]["end"], "%H:%M").time()
            
            if not (start_time <= current_time <= end_time):
                self.logger.info(f"[VENTANA] PyTOA30 NO ejecutado - fuera de ventana horaria ({task['time_window']['start']}-{task['time_window']['end']}). Hora actual: {current_time.strftime('%H:%M')}")
                return
            else:
                self.logger.info(f"[VENTANA] PyTOA30 dentro de ventana horaria ({task['time_window']['start']}-{task['time_window']['end']}). Ejecutando...")
        
        # Marcar como en ejecución
        task["status"] = "running"
        task["last_run"] = datetime.now()
        self.running_tasks.add(task_name)
        
        self.logger.info(f"Iniciando tarea: {task_name}")
        
        # Actualizar GUI
        self.update_summary_panel(task_name, "running", 0, task["last_run"])
        self.refresh_gui(force=True)
        
        # Iniciar thread para ejecutar la tarea
        threading.Thread(target=self._execute_process, args=(task_name,), daemon=True).start()
    
    def _execute_process(self, task_name):
        """Ejecuta una tarea como un proceso separado"""
        task = self.tasks[task_name]
        start_time = task["last_run"]
        success = False
        process = None

        try:
            python_exe = sys.executable
            task_path = task["path"]

            # Verificar si el archivo existe
            if not os.path.exists(task_path):
                file_name = os.path.basename(task_path)
                alt_path = os.path.join(self.base_path, file_name)

                if os.path.exists(alt_path):
                    task_path = alt_path
                    task["path"] = alt_path
                    self.save_tasks()
                else:
                    raise FileNotFoundError(f"No se encuentra el archivo: {task_path}")

            self.logger.info(f"Ejecutando: {python_exe} {task_path}")

            # Ejecutar proceso de forma no bloqueante con configuración mejorada
            env = os.environ.copy()
            # Configurar variables de entorno para suprimir mensajes de DevTools
            env['PYTHONIOENCODING'] = 'utf-8'
            env['WDM_LOG_LEVEL'] = '0'
            env['SELENIUM_LOG_LEVEL'] = 'WARNING'
            
            # Configurar flags de creación para Windows
            creation_flags = 0
            if sys.platform == 'win32':
                creation_flags = subprocess.CREATE_NO_WINDOW  # Evita ventanas de consola adicionales
            
            # Extraer modo de ejecución si existe en el nombre de la tarea
            execution_mode = ""
            if "_" in task_name:
                parts = task_name.split("_")
                if len(parts) > 1 and parts[0] in ["CalidadNew23", "CalidadNaranja"]:
                    execution_mode = parts[1]  # Obtener 11AM, 3PM, 6PM, etc.
            
            # Preparar comando con argumentos si es necesario
            command = [python_exe, task_path]
            if execution_mode:
                command.extend(["--execution", execution_mode])
                
            self.logger.info(f"Comando completo: {' '.join(command)}")
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False,
                text=False,  # Cambiado a False para obtener bytes en lugar de texto
                env=env,
                creationflags=creation_flags
            )
            
            # Guardar PID del proceso para seguimiento
            task["process_pid"] = process.pid
            task["execution_start"] = datetime.now()
            
            # Esperar brevemente para detectar fallos inmediatos
            try:
                return_code = process.wait(timeout=2)
                if return_code != 0:
                    stderr_output = process.stderr.read()
                    
                    # Decodificar stderr_output a string
                    try:
                        stderr_str = stderr_output.decode('utf-8', errors='replace')
                    except (UnicodeDecodeError, AttributeError):
                        stderr_str = str(stderr_output)
                    
                    # Filtrar mensajes conocidos de DevTools/Selenium
                    filtered_lines = []
                    for line in stderr_str.split('\n'):
                        if line.strip() and not any(ignore in line for ignore in [
                            'DevTools listening on',
                            'segmentation_platform',
                            'OptimizationTargetUserTopicOnUrlProtobuf',
                            'CustomInputError',
                            '[WARNING:',
                            '[INFO:'
                        ]):
                            filtered_lines.append(line.strip())
                    
                    error_msg = f"El proceso terminó con código de error: {return_code}"
                    if filtered_lines:
                        error_msg += f"\nError: {chr(10).join(filtered_lines)}"
                    raise Exception(error_msg)
                else:
                    # Si termina rápidamente con éxito
                    task["execution_end"] = datetime.now()
                    execution_time = (task["execution_end"] - task["execution_start"]).total_seconds()
                    self.logger.info(f"Tarea {task_name} completada rápidamente en {execution_time:.2f} segundos")
                    success = True
            except subprocess.TimeoutExpired:
                # Esto es normal - significa que el proceso sigue ejecutándose
                self.logger.info(f"Tarea {task_name} iniciada correctamente (PID: {process.pid})")
                
                # Iniciar un hilo separado para monitorear el proceso
                monitor_thread = threading.Thread(
                    target=self._monitor_process,
                    args=(task_name, process),
                    daemon=True
                )
                monitor_thread.start()
                
                success = True

        except Exception as e:
            self.logger.error(f"Error al ejecutar {task_name}: {e}")
            success = False
            
            # Registrar el fin de la ejecución en caso de error
            task["execution_end"] = datetime.now()
            
        finally:
            # Actualizar estadísticas
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            task["last_duration"] = duration
            
            # Calcular próxima ejecución
            self._calculate_next_execution(task_name)

            if success:
                task["status"] = "running" if process and process.poll() is None else "completed"
                task["success_count"] += 1
                self.logger.info(f"Tarea {task_name} iniciada con éxito ({duration:.2f} segundos)")
            else:
                task["status"] = "failed"
                task["failure_count"] += 1
                self.logger.error(f"Tarea {task_name} falló ({duration:.2f} segundos)")
                
                # Si falla, eliminar de tareas en ejecución
                if task_name in self.running_tasks:
                    self.running_tasks.remove(task_name)

            # Actualizar GUI
            self.update_summary_panel(task_name, task["status"], duration, end_time)
            self.refresh_gui(force=True)

            # Guardar configuración
            self.save_tasks()
            
    def _monitor_process(self, task_name, process):
        """Monitorea un proceso en ejecución hasta que termine con salida en tiempo real"""
        if task_name not in self.tasks:
            return
            
        task = self.tasks[task_name]
        
        try:
            # Leer salida en tiempo real
            import select
            import threading
            
            output_lines = []
            error_lines = []
            
            def read_output(pipe, lines_list, pipe_name):
                """Lee la salida de un pipe línea por línea con manejo de codificación"""
                try:
                    for line in iter(pipe.readline, b''):
                        if line:
                            try:
                                # Decodificar línea con UTF-8 y manejo de errores
                                decoded_line = line.decode('utf-8', errors='replace').strip()
                                lines_list.append(decoded_line)
                                # Mostrar progreso en tiempo real
                                if pipe_name == "stdout":
                                    self.logger.info(f"[{task_name}] {decoded_line}")
                                else:
                                    # Filtrar mensajes de DevTools
                                    if not any(ignore in decoded_line for ignore in [
                                        'DevTools listening on',
                                        'segmentation_platform',
                                        'OptimizationTargetUserTopicOnUrlProtobuf'
                                    ]):
                                        self.logger.warning(f"[{task_name}] {decoded_line}")
                            except Exception as decode_error:
                                self.logger.error(f"Error decodificando salida: {decode_error}")
                except Exception as e:
                    self.logger.error(f"Error leyendo {pipe_name}: {e}")
                finally:
                    pipe.close()
            
            # Crear threads para leer stdout y stderr
            stdout_thread = threading.Thread(
                target=read_output, 
                args=(process.stdout, output_lines, "stdout"),
                daemon=True
            )
            stderr_thread = threading.Thread(
                target=read_output,
                args=(process.stderr, error_lines, "stderr"),
                daemon=True
            )
            
            stdout_thread.start()
            stderr_thread.start()
            
            # Esperar a que el proceso termine
            process.wait()
            
            # Esperar a que los threads terminen de leer
            stdout_thread.join(timeout=2)
            stderr_thread.join(timeout=2)
            
            # Registrar tiempo de finalización
            task["execution_end"] = datetime.now()
            execution_time = (task["execution_end"] - task["execution_start"]).total_seconds()
            
            # Unir las líneas capturadas
            stdout_output = '\n'.join(output_lines)
            stderr_output = '\n'.join(error_lines)
            
            # Verificar código de salida
            if process.returncode == 0:
                self.logger.info(f"Proceso {task_name} completado exitosamente. Tiempo de ejecución: {execution_time:.2f} segundos")
                if stdout_output.strip():
                    self.logger.info(f"Salida de {task_name}: {stdout_output.strip()}")
                task["status"] = "completed"
            else:
                error_msg = f"Proceso {task_name} terminó con error (código {process.returncode}). Tiempo de ejecución: {execution_time:.2f} segundos"
                if stderr_output.strip():
                    error_msg += f"\nError: {stderr_output.strip()}"
                self.logger.error(error_msg)
                task["status"] = "failed"
                
            # Guardar tiempo de ejecución real
            task["execution_time_real"] = execution_time
                
        except Exception as e:
            self.logger.error(f"Error al monitorear proceso {task_name}: {e}")
            task["status"] = "failed"
            
        finally:
            # Eliminar de tareas en ejecución
            if task_name in self.running_tasks:
                self.running_tasks.remove(task_name)
                
            # Actualizar interfaz
            self.root.after(100, lambda: self.update_summary_panel(
                task_name, 
                task["status"], 
                task.get("execution_time_real", 0), 
                task.get("execution_end", datetime.now())
            ))
            self.root.after(200, lambda: self.refresh_gui(force=True))
            
            # Guardar configuración
            self.save_tasks()
    
    def _calculate_next_execution(self, task_name):
        """Calcula la próxima ejecución programada para una tarea"""
        if task_name not in self.tasks:
            return
            
        task = self.tasks[task_name]
        now = datetime.now()
        
        # Para tareas con hora específica
        if task["execution_time"]:
            try:
                hours, minutes = map(int, task["execution_time"].split(":"))
                next_run = now.replace(hour=hours, minute=minutes, second=0, microsecond=0)
                
                # Si la hora ya pasó, programar para mañana
                if next_run <= now:
                    next_run = next_run + timedelta(days=1)
                    
                task["next_execution"] = next_run
            except Exception as e:
                self.logger.error(f"Error al calcular próxima ejecución diaria para {task_name}: {e}")
                
        # Para tareas con intervalo
        elif task["interval"]:
            try:
                # Si nunca se ha ejecutado, programar desde ahora
                if not task["last_run"]:
                    next_run = now + timedelta(minutes=task["interval"])
                else:
                    # Si ya se ejecutó, programar desde la última ejecución
                    next_run = task["last_run"] + timedelta(minutes=task["interval"])
                    
                    # Si la próxima ejecución ya pasó, reprogramar desde ahora
                    if next_run <= now:
                        next_run = now + timedelta(minutes=task["interval"])
                        
                task["next_execution"] = next_run
            except Exception as e:
                self.logger.error(f"Error al calcular próxima ejecución por intervalo para {task_name}: {e}")
        
        # Para tareas manuales, no hay próxima ejecución programada
        else:
            task["next_execution"] = None

    def start_scheduler(self):
        """Inicia el programador de tareas"""
        self.logger.info("Iniciando programador de tareas")

        # Limpiar programación previa
        schedule.clear()

        # Programar tareas diarias
        for name, task in self.tasks.items():
            if task["execution_time"]:
                schedule.every().day.at(task["execution_time"]).do(
                    self.execute_task, name
                )
                self.logger.info(f"Tarea diaria programada: {name} a las {task['execution_time']}")

        # Programar tareas por intervalo
        interval_tasks = {}
        for name, task in self.tasks.items():
            if task["interval"]:
                # Verificación específica para FlujoMysql_i
                if name == "FlujoMysql_i" and task["interval"] != 30:
                    self.logger.warning(f"Corrigiendo intervalo de FlujoMysql_i durante la programación: forzando a 30 minutos")
                    task["interval"] = 30
                
                interval = task["interval"]
                if interval not in interval_tasks:
                    interval_tasks[interval] = []
                interval_tasks[interval].append(name)

        for interval, tasks in interval_tasks.items():
            job = schedule.every(interval).minutes.do(
                lambda t=tasks: [self.execute_task(task_name) for task_name in t]
            )
            self.logger.info(f"Tareas cada {interval} minutos programadas: {', '.join(tasks)}")

        # Iniciar thread para ejecutar schedule
        threading.Thread(target=self._run_scheduler, daemon=True).start()

    def _run_scheduler(self):
        """Ejecuta el bucle principal del programador"""
        self.logger.info("Bucle principal del programador iniciado")
        last_check_time = time.time()

        while True:
            try:
                # Ejecutar las tareas pendientes
                schedule.run_pending()
                
                # Cada 5 minutos, verificar y corregir la configuración de intervalos
                current_time = time.time()
                if current_time - last_check_time >= 300:  # 5 minutos
                    self.verify_task_intervals()
                    last_check_time = current_time
                
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"Error en el bucle principal: {e}")
                time.sleep(5)  # Esperar un poco más si hay error
    
    def verify_task_intervals(self):
        """Verifica y corrige los intervalos de las tareas"""
        try:
            # Verificación específica para FlujoMysql_i
            if "FlujoMysql_i" in self.tasks:
                task = self.tasks["FlujoMysql_i"]
                if task["interval"] != 30:
                    self.logger.warning(f"Verificación periódica: Corrigiendo intervalo de FlujoMysql_i de {task['interval']} a 30 minutos")
                    # Corregir el intervalo
                    task["interval"] = 30
                    # Guardar cambios
                    self.save_tasks()
                    # Reprogramar tareas
                    self.logger.info("Reprogramando tareas después de corregir intervalo")
                    self.start_scheduler()
        except Exception as e:
            self.logger.error(f"Error al verificar intervalos de tareas: {e}")

    def setup_gui(self):
        """Configura la interfaz gráfica"""
        self.root = tk.Tk()
        self.root.title(f"ETL Scheduler v2.0 - {self.user}")
        self.root.geometry("1000x700")

        # Configurar estilo
        self.style = ttk.Style()
        self.style.configure("Running.TLabel", foreground="blue", font=("Helvetica", 9,
"bold"))
        self.style.configure("Completed.TLabel", foreground="green", font=("Helvetica", 9,
"bold"))
        self.style.configure("Failed.TLabel", foreground="red", font=("Helvetica", 9, "bold"))

        # Marco principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Barra de herramientas (ahora en la parte superior)
        self.setup_toolbar(main_frame)
        
        # Panel de resumen
        self.setup_summary_panel(main_frame)

        # Marco de contenido principal (2 columnas)
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Panel izquierdo (tareas)
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Panel derecho (logs)
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # Configurar notebook y pestañas
        self.setup_task_tabs(left_frame)

        # Configurar panel de log
        self.setup_log_panel(right_frame)

        # Iniciar actualizaciones periódicas
        self.refresh_gui()

        self.logger.info("Interfaz gráfica iniciada")

    def setup_summary_panel(self, parent):
        """Configura el panel de resumen"""
        self.summary_frame = ttk.LabelFrame(parent, text="Última ejecución")
        self.summary_frame.pack(fill=tk.X, padx=5, pady=5)

        self.last_task_label = ttk.Label(self.summary_frame, text="Última tarea: Ninguna")
        self.last_task_label.pack(anchor=tk.W, padx=5, pady=2)

        self.last_status_label = ttk.Label(self.summary_frame, text="Estado: -")
        self.last_status_label.pack(anchor=tk.W, padx=5, pady=2)

        self.last_duration_label = ttk.Label(self.summary_frame, text="Duración: -")
        self.last_duration_label.pack(anchor=tk.W, padx=5, pady=2)

        self.last_time_label = ttk.Label(self.summary_frame, text="Hora: -")
        self.last_time_label.pack(anchor=tk.W, padx=5, pady=2)

    def setup_task_tabs(self, parent):
        """Configura las pestañas de tareas"""
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Crear pestañas
        self.daily_tab = ttk.Frame(self.notebook)
        self.interval_tab = ttk.Frame(self.notebook)
        self.manual_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.daily_tab, text='Tareas Diarias')
        self.notebook.add(self.interval_tab, text='Tareas por Intervalo')
        self.notebook.add(self.manual_tab, text='Tareas Manuales')
        
        # Directorio base
        ttk.Label(self.daily_tab, text=f"Directorio base: {self.base_path}",
foreground="blue").pack(anchor=tk.W, padx=5, pady=5)
        ttk.Label(self.interval_tab, text=f"Directorio base: {self.base_path}",
foreground="blue").pack(anchor=tk.W, padx=5, pady=5)
        ttk.Label(self.manual_tab, text=f"Directorio base: {self.base_path}",
foreground="blue").pack(anchor=tk.W, padx=5, pady=5)
        
        # Crear TreeViews con columnas ampliadas
        self.daily_tree = ttk.Treeview(
            self.daily_tab, 
            columns=("Hora", "Estado", "Última ejecución", "Tiempo Real", "Próxima ejecución"), 
            show="headings tree"
        )
        self.daily_tree.heading("#0", text="Nombre")
        self.daily_tree.heading("Hora", text="Hora")
        self.daily_tree.heading("Estado", text="Estado")
        self.daily_tree.heading("Última ejecución", text="Última ejecución")
        self.daily_tree.heading("Tiempo Real", text="Tiempo Real (seg)")
        self.daily_tree.heading("Próxima ejecución", text="Próxima ejecución")
        self.daily_tree.column("#0", width=180)
        self.daily_tree.column("Hora", width=80)
        self.daily_tree.column("Estado", width=80)
        self.daily_tree.column("Última ejecución", width=140)
        self.daily_tree.column("Tiempo Real", width=100)
        self.daily_tree.column("Próxima ejecución", width=140)
        self.daily_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Panel de intervalo
        interval_frame = ttk.Frame(self.interval_tab)
        interval_frame.pack(fill=tk.BOTH, expand=True)
        
        interval30_frame = ttk.LabelFrame(interval_frame, text="Cada 30 minutos")
        interval30_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.interval30_tree = ttk.Treeview(
            interval30_frame, 
            columns=("Estado", "Última ejecución", "Tiempo Real", "Próxima ejecución"), 
            show="headings tree"
        )
        self.interval30_tree.heading("#0", text="Nombre")
        self.interval30_tree.heading("Estado", text="Estado")
        self.interval30_tree.heading("Última ejecución", text="Última ejecución")
        self.interval30_tree.heading("Tiempo Real", text="Tiempo Real (seg)")
        self.interval30_tree.heading("Próxima ejecución", text="Próxima ejecución")
        self.interval30_tree.column("#0", width=180)
        self.interval30_tree.column("Estado", width=80)
        self.interval30_tree.column("Última ejecución", width=140)
        self.interval30_tree.column("Tiempo Real", width=100)
        self.interval30_tree.column("Próxima ejecución", width=140)
        self.interval30_tree.pack(fill=tk.BOTH, expand=True)
        
        interval120_frame = ttk.LabelFrame(interval_frame, text="Cada 120 minutos")
        interval120_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.interval120_tree = ttk.Treeview(
            interval120_frame, 
            columns=("Estado", "Última ejecución", "Tiempo Real", "Próxima ejecución"), 
            show="headings tree"
        )
        self.interval120_tree.heading("#0", text="Nombre")
        self.interval120_tree.heading("Estado", text="Estado")
        self.interval120_tree.heading("Última ejecución", text="Última ejecución")
        self.interval120_tree.heading("Tiempo Real", text="Tiempo Real (seg)")
        self.interval120_tree.heading("Próxima ejecución", text="Próxima ejecución")
        self.interval120_tree.column("#0", width=180)
        self.interval120_tree.column("Estado", width=80)
        self.interval120_tree.column("Última ejecución", width=140)
        self.interval120_tree.column("Tiempo Real", width=100)
        self.interval120_tree.column("Próxima ejecución", width=140)
        self.interval120_tree.pack(fill=tk.BOTH, expand=True)
        
        # Panel de manuales
        self.manual_tree = ttk.Treeview(
            self.manual_tab, 
            columns=("Estado", "Última ejecución", "Tiempo Real"), 
            show="headings tree"
        )
        self.manual_tree.heading("#0", text="Nombre")
        self.manual_tree.heading("Estado", text="Estado")
        self.manual_tree.heading("Última ejecución", text="Última ejecución")
        self.manual_tree.heading("Tiempo Real", text="Tiempo Real (seg)")
        self.manual_tree.column("#0", width=200)
        self.manual_tree.column("Estado", width=100)
        self.manual_tree.column("Última ejecución", width=150)
        self.manual_tree.column("Tiempo Real", width=120)
        self.manual_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Enlazar eventos
        self.daily_tree.bind("<Double-1>", self.on_task_double_click)
        self.interval30_tree.bind("<Double-1>", self.on_task_double_click)
        self.interval120_tree.bind("<Double-1>", self.on_task_double_click)
        self.manual_tree.bind("<Double-1>", self.on_task_double_click)

    def setup_log_panel(self, parent):
        """Configura el panel de log"""
        log_frame = ttk.LabelFrame(parent, text="Log de ejecución")
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=("Helvetica", 8))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Configurar estilos de texto con fuente más pequeña
        self.log_text.tag_configure("error", foreground="red", font=("Helvetica", 8, "bold"))
        self.log_text.tag_configure("success", foreground="green", font=("Helvetica", 8,
"bold"))
        self.log_text.tag_configure("warning", foreground="orange", font=("Helvetica", 8))
        self.log_text.tag_configure("info", foreground="blue", font=("Helvetica", 8))

        # Scrollbar
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text['yscrollcommand'] = scrollbar.set

        # Handler personalizado
        class TextHandler(logging.Handler):
            def __init__(self, text_widget):
                logging.Handler.__init__(self)
                self.text_widget = text_widget

            def emit(self, record):
                msg = self.format(record)

                def append():
                    if record.levelno >= logging.ERROR:
                        self.text_widget.insert(tk.END, msg + '\n', "error")
                    elif record.levelno >= logging.WARNING:
                        self.text_widget.insert(tk.END, msg + '\n', "warning")
                    elif "exitoso" in msg.lower() or "éxito" in msg.lower():
                        self.text_widget.insert(tk.END, msg + '\n', "success")
                    else:
                        self.text_widget.insert(tk.END, msg + '\n', "info")

                    self.text_widget.see(tk.END)

                # Ejecutar en el hilo principal
                self.text_widget.after(0, append)

        # Añadir handler al logger
        handler = TextHandler(self.log_text)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

        # Botón para limpiar log
        ttk.Button(log_frame, text="Limpiar Log", command=lambda: self.log_text.delete(1.0,
tk.END)).pack(side=tk.BOTTOM, padx=5, pady=5)

    def setup_toolbar(self, parent):
        """Configura la barra de herramientas"""
        toolbar_frame = ttk.LabelFrame(parent, text="Acciones")
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)

        # Contenedor para organizar los botones
        button_container = ttk.Frame(toolbar_frame)
        button_container.pack(fill=tk.X, padx=10, pady=5)

        # Botones organizados en fila con espaciado uniforme
        ttk.Button(button_container, text="Ejecutar seleccionado",
command=self.execute_selected_task).grid(row=0, column=0, padx=10, pady=5)
        ttk.Button(button_container, text="Abrir archivo",
command=self.open_selected_file).grid(row=0, column=1, padx=10, pady=5)
        ttk.Button(button_container, text="Ver detalles",
command=self.show_task_details).grid(row=0, column=2, padx=10, pady=5)
        ttk.Button(button_container, text="↻ Actualizar", command=lambda:
self.refresh_gui(force=True)).grid(row=0, column=3, padx=10, pady=5)

        # Etiqueta de información
        ttk.Label(button_container, text="Actualización automática cada 2 minutos",
foreground="gray").grid(row=0, column=4, padx=10, pady=5)

    def execute_selected_task(self):
        """Ejecuta la tarea seleccionada"""
        task_name = self.get_selected_task()
        if task_name and task_name in self.tasks:
            if messagebox.askyesno("Confirmar ejecución", f"¿Está seguro de ejecutar la tarea '{task_name}'?"):
                self.logger.info(f"Ejecutando manualmente: {task_name}")
                self.execute_task(task_name)
        else:
            messagebox.showinfo("Selección", "Seleccione una tarea válida primero")

    def get_selected_task(self):
        """Obtiene la tarea seleccionada en la pestaña actual"""
        current_tab = self.notebook.select()
        tab_name = self.notebook.tab(current_tab, "text")

        if tab_name == "Tareas Diarias":
            selection = self.daily_tree.selection()
            if selection:
                return self.daily_tree.item(selection[0], "text")

        elif tab_name == "Tareas por Intervalo":
            selection = self.interval30_tree.selection()
            if selection:
                return self.interval30_tree.item(selection[0], "text")

            selection = self.interval120_tree.selection()
            if selection:
                return self.interval120_tree.item(selection[0], "text")

        elif tab_name == "Tareas Manuales":
            selection = self.manual_tree.selection()
            if selection:
                return self.manual_tree.item(selection[0], "text")

        return None

    def open_selected_file(self):
        """Abre el archivo de la tarea seleccionada usando el diálogo del sistema"""
        task_name = self.get_selected_task()
        if not task_name or task_name not in self.tasks:
            messagebox.showinfo("Selección", "Seleccione una tarea válida primero")
            return
            
        task = self.tasks[task_name]
        file_path = task["path"]
        
        # Verificar si el archivo existe
        if not os.path.exists(file_path):
            messagebox.showerror("Error", f"El archivo no existe:\n{file_path}")
            return
            
        try:
            # Obtener la ruta absoluta del directorio del archivo
            file_dir = os.path.dirname(os.path.abspath(file_path))
            
            # Usar el administrador de archivos del sistema
            if sys.platform == 'win32':
                os.startfile(file_dir)  # Abre el explorador en la carpeta del archivo
                # Opcional: Seleccionar el archivo (solo Windows)
                subprocess.Popen(f'explorer /select,"{file_path}"')
            elif sys.platform == 'darwin':  # macOS
                subprocess.Popen(['open', '-R', file_path])  # Muestra el archivo en Finder
            else:  # Linux y otros UNIX
                subprocess.Popen(['xdg-open', file_dir])  # Abre el administrador de archivos
                
        except Exception as e:
            messagebox.showerror("Error", f"No se pudo abrir el archivo:\n{str(e)}")

    def show_task_details(self):
        """Muestra detalles de la tarea seleccionada"""
        task_name = self.get_selected_task()
        if task_name and task_name in self.tasks:
            task = self.tasks[task_name]

            details_window = tk.Toplevel(self.root)
            details_window.title(f"Detalles: {task_name}")
            details_window.geometry("500x450")  # Aumentado para acomodar más información

            # Marco de información
            info_frame = ttk.LabelFrame(details_window, text="Información")
            info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Mostrar detalles
            row = 0
            details = [
                ("Nombre:", task_name),
                ("Ruta:", task["path"]),
                ("Estado:", task["status"]),
                ("Ejecuciones exitosas:", str(task["success_count"])),
                ("Ejecuciones fallidas:", str(task["failure_count"])),
                ("Última ejecución:", task["last_run"].strftime("%Y-%m-%d %H:%M:%S") if
task["last_run"] else "Nunca")
            ]
            
            # Añadir tiempo real de ejecución
            if task.get("execution_time_real"):
                details.append(("Tiempo de ejecución real:", f"{task['execution_time_real']:.2f} segundos"))
            else:
                details.append(("Tiempo de ejecución real:", "N/A"))
                
            # Añadir información de próxima ejecución
            if task.get("next_execution"):
                details.append(("Próxima ejecución:", task["next_execution"].strftime("%Y-%m-%d %H:%M:%S")))
            
            # Añadir tipo de ejecución
            if task["execution_time"]:
                details.append(("Hora ejecución diaria:", task["execution_time"]))
            elif task["interval"]:
                details.append(("Intervalo de ejecución:", f"Cada {task['interval']} minutos"))

            for label, value in details:
                ttk.Label(info_frame, text=label).grid(row=row, column=0, sticky=tk.W, padx=5,
pady=2)
                ttk.Label(info_frame, text=str(value)).grid(row=row, column=1, sticky=tk.W,
padx=5, pady=2)
                row += 1

            # Botones
            button_frame = ttk.Frame(details_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="Ejecutar ahora",
                    command=lambda: [self.execute_task(task_name),
details_window.destroy()]).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="Abrir archivo",
                    command=lambda: os.startfile(task["path"]) if os.path.exists(task["path"]) else messagebox.showerror("Error", f"El archivo {task['path']} no existe")).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="Cerrar",
                    command=details_window.destroy).pack(side=tk.RIGHT, padx=5)
        else:
            messagebox.showinfo("Selección", "Seleccione una tarea válida primero")

    def on_task_double_click(self, event):
        """Maneja doble clic en una tarea"""
        self.show_task_details()

    def update_summary_panel(self, task_name, status, duration, timestamp, additional_info=""):
        """Actualiza el panel de resumen"""
        def update():
            self.last_task_label.config(text=f"Última tarea: {task_name}")

            status_style = "Header.TLabel"
            if status == "completed":
                status_text = "EXITOSO"
                status_style = "Completed.TLabel"
            elif status == "failed":
                status_text = "FALLIDO"
                status_style = "Failed.TLabel"
            elif status == "running":
                status_text = "EN EJECUCIÓN"
                status_style = "Running.TLabel"
            else:
                status_text = status.upper()

            if additional_info:
                status_text += f" ({additional_info})"

            self.last_status_label.config(text=f"Estado: {status_text}", style=status_style)

            if status != "running":
                self.last_duration_label.config(text=f"Duración: {duration:.2f} segundos")
            else:
                self.last_duration_label.config(text="Duración: En proceso...")

            self.last_time_label.config(text=f"Hora: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

        # Ejecutar en el hilo principal
        if hasattr(self, 'root') and self.root:
            self.root.after(100, update)

    def refresh_gui(self, force=False):
        """Actualiza la interfaz con la información más reciente"""
        def update_ui():
            # Actualizar listas de tareas
            self.update_task_lists()

            # Programar próxima actualización si no fue forzada
            if not force:
                self.root.after(120000, self.refresh_gui)  # 2 minutos

        # Ejecutar en el hilo principal
        self.root.after(100, update_ui)

    def update_task_lists(self):
        """Actualiza las listas de tareas en la interfaz"""
        # Limpiar listas
        self.daily_tree.delete(*self.daily_tree.get_children())
        self.interval30_tree.delete(*self.interval30_tree.get_children())
        self.interval120_tree.delete(*self.interval120_tree.get_children())
        self.manual_tree.delete(*self.manual_tree.get_children())

        # Clasificar tareas
        daily_tasks = []
        interval30_tasks = []
        interval120_tasks = []
        manual_tasks = []

        for name, task in self.tasks.items():
            if task["execution_time"]:
                daily_tasks.append(task)
            elif task["interval"] == 30:
                interval30_tasks.append(task)
            elif task["interval"] == 120:
                interval120_tasks.append(task)
            else:
                manual_tasks.append(task)

        # Actualizar TreeViews con información adicional
        for task in daily_tasks:
            # Para tareas diarias: añadir tiempo real y próxima ejecución
            self.daily_tree.insert("", tk.END, text=task["name"], values=(
                task["execution_time"],
                task["status"],
                task["last_run"].strftime("%Y-%m-%d %H:%M:%S") if task["last_run"] else "Nunca",
                f"{task.get('execution_time_real', 0):.1f}" if task.get('execution_time_real') else "N/A",
                task.get("next_execution").strftime("%Y-%m-%d %H:%M:%S") if task.get("next_execution") else "No programada"
            ), tags=(task["status"],))

        for task in interval30_tasks:
            # Para tareas de intervalo 30: añadir tiempo real y próxima ejecución
            self.interval30_tree.insert("", tk.END, text=task["name"], values=(
                task["status"],
                task["last_run"].strftime("%Y-%m-%d %H:%M:%S") if task["last_run"] else "Nunca",
                f"{task.get('execution_time_real', 0):.1f}" if task.get('execution_time_real') else "N/A",
                task.get("next_execution").strftime("%Y-%m-%d %H:%M:%S") if task.get("next_execution") else "No programada"
            ), tags=(task["status"],))

        for task in interval120_tasks:
            # Para tareas de intervalo 120: añadir tiempo real y próxima ejecución
            self.interval120_tree.insert("", tk.END, text=task["name"], values=(
                task["status"],
                task["last_run"].strftime("%Y-%m-%d %H:%M:%S") if task["last_run"] else "Nunca",
                f"{task.get('execution_time_real', 0):.1f}" if task.get('execution_time_real') else "N/A",
                task.get("next_execution").strftime("%Y-%m-%d %H:%M:%S") if task.get("next_execution") else "No programada"
            ), tags=(task["status"],))

        for task in manual_tasks:
            # Para tareas manuales: añadir solo tiempo real (no tienen próxima ejecución)
            self.manual_tree.insert("", tk.END, text=task["name"], values=(
                task["status"],
                task["last_run"].strftime("%Y-%m-%d %H:%M:%S") if task["last_run"] else "Nunca",
                f"{task.get('execution_time_real', 0):.1f}" if task.get('execution_time_real') else "N/A"
            ), tags=(task["status"],))

        # Configurar colores para los estados
        for tree in [self.daily_tree, self.interval30_tree, self.interval120_tree,
self.manual_tree]:
            tree.tag_configure("running", foreground="blue")
            tree.tag_configure("completed", foreground="green")
            tree.tag_configure("failed", foreground="red")

    def run(self):
        """Inicia la aplicación"""
        # Configurar manejo de cierre
        def on_closing():
            if messagebox.askokcancel("Salir", "¿Está seguro de que desea salir?\nLas tareas programadas seguirán ejecutándose."):
                self.logger.info("Aplicación cerrada por el usuario")
                self.root.destroy()

        self.root.protocol("WM_DELETE_WINDOW", on_closing)

        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error en el bucle principal de la interfaz: {e}")
            print(f"Error en la interfaz: {e}")


if __name__ == "__main__":
    try:
        # Crear e iniciar la aplicación
        app = ETLScheduler()
        app.run()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        import traceback
        traceback.print_exc()
        input("Presione Enter para salir...")