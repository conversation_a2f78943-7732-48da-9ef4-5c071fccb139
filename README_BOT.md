# Oracle Cloud Field Service Bot

Bot automatizado para navegar en Oracle Cloud Field Service usando Playwright.

## 🚀 Instalación

### 1. Instalar Python (si no lo tienes)
Descarga Python desde https://python.org/downloads/

### 2. Instalar dependencias
```bash
# Navegar al directorio del proyecto
cd "c:\Users\<USER>\Desktop\aunclick_home_react"

# Instalar Playwright
pip install -r requirements_bot.txt

# Instalar navegadores de Playwright
playwright install
```

### 3. Verificar instalación
```bash
playwright --version
```

## 🎯 Uso

### Opción 1: Script de prueba paso a paso
```bash
python test_oracle_bot.py
```
Este script te permite probar cada paso individualmente y es ideal para debugging.

### Opción 2: Bot completo automatizado
```bash
python oracle_cloud_bot.py
```
Este ejecuta todo el flujo de forma automática.

## 📋 Funcionalidades implementadas

✅ **Paso 1**: Navegar a https://vtr.fs.ocs.oraclecloud.com/
✅ **Paso 2**: Encontrar y hacer clic en "Conectarse con SSO"
✅ **Paso 3**: Llenar campo "Nombre de usuario" con "ncornejoh"
✅ **Paso 4**: Hacer clic en "Continuar con SSO"
✅ **Paso 5**: Llenar campo de email con "<EMAIL>"
✅ **Paso 6**: Hacer clic en "Siguiente"
✅ **Paso 7**: Llenar campo de contraseña con "Telqway.202517"
✅ **Paso 8**: Hacer clic en "Iniciar sesión"
✅ **Capturas automáticas**: Screenshots en cada etapa
✅ **Logging detallado**: Con timestamps y estados

### Opción 3: Script de login rápido
```bash
python quick_login_test.py
```
Este ejecuta todo el proceso de login sin pausas para pruebas rápidas.

## ⚙️ Configuración

En `oracle_cloud_bot.py` puedes modificar:

```python
# Configuración del bot
bot = OracleCloudBot(
    headless=False,  # True para ejecutar sin interfaz gráfica
    slow_mo=1500     # Velocidad entre acciones (ms)
)

# Credenciales para la automatización
username = "ncornejoh"           # Usuario SSO
email = "<EMAIL>"       # Email corporativo
password = "Telqway.202517"      # Contraseña
```

## 🔧 Personalización

Para añadir más funcionalidades al bot:

1. **Añadir nuevos métodos** en la clase `OracleCloudBot`
2. **Usar selectores CSS o XPath** para elementos más específicos
3. **Añadir manejo de errores** para casos específicos
4. **Implementar esperas condicionales** para elementos dinámicos

### Ejemplo de selector avanzado:
```python
# Buscar por texto específico
element = page.locator("button:has-text('Texto exacto')")

# Buscar por atributos
element = page.locator("[data-testid='mi-elemento']")

# Buscar por CSS selector
element = page.locator(".clase-css #id-elemento")
```

## 🐛 Debugging

Para debugging, usa:

```python
# Tomar captura de pantalla
page.screenshot(path="debug.png")

# Imprimir HTML de la página
print(page.content())

# Esperar por un elemento específico
page.wait_for_selector("selector", timeout=10000)

# Verificar si un elemento existe
if page.locator("selector").is_visible():
    print("Elemento encontrado")
```

## 📝 Notas importantes

- El bot está configurado para ejecutarse en **modo visible** por defecto para que puedas ver qué está haciendo
- Las capturas de pantalla se guardan en el directorio del proyecto
- Los logs se muestran en consola con timestamps
- El bot maneja automáticamente las esperas de red y carga de página

## 🚨 Consideraciones de seguridad

- **NO** hardcodees credenciales en el código
- Usa variables de entorno para datos sensibles
- Ten cuidado con los logs que pueden contener información sensible

## 📞 Soporte

Si encuentras errores:

1. Revisa que Playwright esté correctamente instalado
2. Verifica que tengas conexión a internet
3. Comprueba que la URL de Oracle Cloud sea accesible
4. Revisa los logs en consola para más detalles del error