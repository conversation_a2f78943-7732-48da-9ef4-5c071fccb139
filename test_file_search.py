#!/usr/bin/env python3
"""
Test script for file search optimizations.
"""

import os
import time
from file_utils import find_latest_excel_file

def test_file_search():
    """Test the optimized file search functionality."""
    print("🧪 Testing optimized file search...")

    # Test 1: Search with default parameters
    print("\n📋 Test 1: Default search (last 3 minutes)")
    result = find_latest_excel_file()
    if result:
        print(f"✅ Found file: {os.path.basename(result)}")
    else:
        print("ℹ️  No recent files found (expected if no Excel files in last 3 minutes)")

    # Test 2: Search with longer time window
    print("\n📋 Test 2: Extended search (last 15 minutes)")
    result = find_latest_excel_file(max_age_minutes=15)
    if result:
        print(f"✅ Found file: {os.path.basename(result)}")
    else:
        print("ℹ️  No files found in last 15 minutes")

    # Test 3: Search with custom directory
    downloads_dir = os.path.join(os.path.expanduser("~"), "Downloads")
    print(f"\n📋 Test 3: Custom directory search ({downloads_dir})")
    result = find_latest_excel_file(download_dir=downloads_dir, max_age_minutes=15)
    if result:
        print(f"✅ Found file: {os.path.basename(result)}")
    else:
        print("ℹ️  No files found in Downloads directory")

    print("\n✅ File search tests completed!")

if __name__ == "__main__":
    test_file_search()