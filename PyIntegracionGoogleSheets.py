import pandas as pd
import datetime as date
import requests
import json
import mysql.connector
import numpy as np
from datetime import datetime, timedelta
import sys
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('PyIntegracionGoogleSheets.log'),
        logging.StreamHandler()
    ]
)

# Función para convertir fechas con múltiples formatos (definida a nivel global)
def parse_date(date_str):
    if pd.isna(date_str) or date_str is None:
        return None
        
    date_str = str(date_str).strip()
    
    # Lista de formatos posibles
    formats = [
        '%d/%m/%Y',    # 29/5/2023
        '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
        '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
        '%m/%d/%Y',    # 5/29/2023
        '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
        '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
        '%Y-%m-%d',    # 2023-05-29
        '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
        '%d-%m-%Y',    # 29-05-2023
        '%m-%d-%Y',    # 05-29-2023
    ]
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            # Determinar si es una fecha con hora o solo fecha
            if '%H' in fmt:
                # Si tiene hora, preservar el formato datetime completo para MySQL
                return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                # Si es solo fecha, usar formato de fecha para MySQL
                return parsed_date.strftime('%Y-%m-%d')
        except ValueError:
            continue
    
    logging.warning(f"No se pudo parsear la fecha: {date_str}")
    return None

# Función para convertir timestamps (definida a nivel global)
def parse_timestamp(ts_str):
    if pd.isna(ts_str) or ts_str is None:
        return None
            
    ts_str = str(ts_str).strip()
    
    # Formatos específicos para timestamps
    timestamp_formats = [
        '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
        '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
        '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
        '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
        '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
        '%Y-%m-%d %H:%M',     # 2023-05-29 13:05
    ]
    
    for fmt in timestamp_formats:
        try:
            parsed_date = datetime.strptime(ts_str, fmt)
            # Formato para MySQL
            return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
        except ValueError:
            continue
    
    # Si no funciona ningún formato timestamp, intentar como fecha simple y agregar hora 00:00:00
    for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y']:
        try:
            parsed_date = datetime.strptime(ts_str, fmt)
            # Formato para MySQL con hora 00:00:00
            return parsed_date.strftime('%Y-%m-%d 00:00:00')
        except ValueError:
            continue
    
    logging.warning(f"No se pudo parsear la marca temporal: {ts_str}")
    return None

def standardize_date_format(df, date_column='Fecha', is_timestamp=False):
    """
    Standardiza el formato de fechas en el DataFrame
    """
    if date_column not in df.columns:
        logging.warning(f"Columna {date_column} no encontrada en el DataFrame")
        return df
    
    logging.info(f"Standardizando formato de fechas en columna: {date_column}")
    
    # Aplicar la función de conversión
    original_count = len(df)
    if is_timestamp:
        # Para marca_temporal, usamos parse_timestamp
        logging.info(f"Procesando columna {date_column} como marca temporal (fecha+hora)")
        df[date_column] = df[date_column].apply(parse_timestamp)
    else:
        # Para fechas normales
        df[date_column] = df[date_column].apply(parse_date)
    
    # Contar fechas válidas vs inválidas
    valid_dates = df[date_column].notna().sum()
    invalid_dates = original_count - valid_dates
    
    logging.info(f"Fechas procesadas: {valid_dates} válidas, {invalid_dates} inválidas")
    
    if invalid_dates > 0:
        logging.warning("Registros con fechas inválidas:")
        invalid_samples = df[df[date_column].isna()].head()
        for idx, row in invalid_samples.iterrows():
            logging.warning(f"  Fila {idx}: {row.get('RUT', 'N/A')} - {row.get('NOMBRE', 'N/A')}")
    
    return df

def validate_data_quality(df):
    """
    Valida la calidad de los datos
    """
    logging.info("Validando calidad de datos...")
    
    issues = []
    
    # Verificar las columnas presentes en el DataFrame y definir columnas críticas
    available_columns = df.columns.tolist()
    logging.info(f"Columnas disponibles en el CSV: {available_columns}")
    
    # Intentar determinar columnas críticas basado en nombres comunes con variaciones en mayúsculas/minúsculas
    critical_columns = []
    possible_date_columns = ['Fecha', 'FECHA', 'fecha']
    possible_rut_columns = ['Rut', 'RUT', 'rut']
    possible_name_columns = ['Nombre', 'NOMBRE', 'nombre']
    
    # Buscar columnas de fecha
    date_column_found = False
    for col in possible_date_columns:
        if col in available_columns:
            critical_columns.append(col)
            date_column_found = True
            logging.info(f"Columna de fecha encontrada: {col}")
            break
    
    # Buscar columnas de RUT
    for col in possible_rut_columns:
        if col in available_columns:
            critical_columns.append(col)
            logging.info(f"Columna de RUT encontrada: {col}")
            break
    
    # Buscar columnas de nombre
    for col in possible_name_columns:
        if col in available_columns:
            critical_columns.append(col)
            logging.info(f"Columna de nombre encontrada: {col}")
            break
    
    if not date_column_found:
        logging.warning("No se encontró una columna de fecha reconocible en el CSV")
        # Verificar si existe alguna columna que contenga la palabra 'fecha'
        date_related_columns = [col for col in available_columns if 'fecha' in col.lower()]
        if date_related_columns:
            logging.info(f"Columnas posiblemente relacionadas con fechas: {date_related_columns}")
    
    for col in critical_columns:
        if col in df.columns:
            null_count = df[col].isna().sum()
            if null_count > 0:
                issues.append(f"Columna {col}: {null_count} valores nulos")
    
    # Verificar rango de fechas si se encontró una columna de fecha
    date_col = None
    for col in possible_date_columns:
        if col in df.columns:
            date_col = col
            break
    
    if date_col:
        logging.info(f"Verificando rango de fechas para columna: {date_col}")
        valid_dates = pd.to_datetime(df[date_col], errors='coerce')
        min_date = valid_dates.min()
        max_date = valid_dates.max()
        
        if min_date and max_date:
            logging.info(f"Rango de fechas: {min_date.date()} a {max_date.date()}")
            
            # Verificar si hay fechas muy antiguas o muy futuras
            today = datetime.now()
            if min_date < today - timedelta(days=365):
                issues.append(f"Hay fechas muy antiguas (anterior a {(today - timedelta(days=365)).date()})")
            if max_date > today + timedelta(days=365):
                issues.append(f"Hay fechas muy futuras (posterior a {(today + timedelta(days=365)).date()})")
    
    if issues:
        logging.warning("Problemas de calidad detectados:")
        for issue in issues:
            logging.warning(f"  - {issue}")
    else:
        logging.info("Validación de calidad: OK")
    
    return issues

def create_mysql_table(cursor, dataframe, table_name):
    """
    Crea una tabla MySQL basada en las columnas del DataFrame
    """
    logging.info(f"Creando tabla MySQL {table_name} si no existe...")
    
    # Mapeo de tipos de datos de pandas a MySQL
    type_mapping = {
        'int64': 'INT',
        'float64': 'FLOAT',
        'bool': 'BOOLEAN',
        'object': 'TEXT',
        'datetime64[ns]': 'DATETIME'
    }
    
    # Obtener los tipos de datos de cada columna
    column_types = {}
    for col in dataframe.columns:
        # Manejar columnas que parecen fechas
        if 'fecha' in col.lower() or 'date' in col.lower():
            column_types[col] = 'DATE'
        elif 'marca_temporal' in col.lower() or 'timestamp' in col.lower():
            column_types[col] = 'DATETIME'
        else:
            pandas_type = str(dataframe[col].dtype)
            # Determinar el tipo MySQL basado en el tipo pandas
            if pandas_type in type_mapping:
                column_types[col] = type_mapping[pandas_type]
            else:
                column_types[col] = 'TEXT'  # Por defecto, usar TEXT
    
    # Crear la consulta SQL
    create_table_query = f"CREATE TABLE IF NOT EXISTS {table_name} (\n"
    create_table_query += "id INT AUTO_INCREMENT PRIMARY KEY,\n"
    
    for i, col in enumerate(dataframe.columns):
        # Sanitizar nombre de columna reemplazando espacios y caracteres especiales
        sanitized_col = col.replace(' ', '_').replace('-', '_').lower()
        create_table_query += f"`{sanitized_col}` {column_types[col]}"
        
        if i < len(dataframe.columns) - 1:
            create_table_query += ",\n"
    
    create_table_query += "\n);"
    
    logging.info(f"Ejecutando consulta CREATE TABLE: {create_table_query}")
    cursor.execute(create_table_query)
    
    return sanitized_column_names(dataframe.columns)

def sanitized_column_names(columns):
    """
    Sanitiza los nombres de columnas para MySQL
    """
    sanitized = []
    for col in columns:
        sanitized.append(col.replace(' ', '_').replace('-', '_').lower())
    return sanitized

try:
    logging.info("Iniciando proceso de obtención y carga de datos desde Google Sheets...")
    url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTteU27KE8UKXoec9RT1XvNsQWIWJ-6-lsNl0jurlHwgoSMpviPbcnH9U-0LCQkePYZXL1sZFoDaF7s/pub?gid=784730613&single=true&output=csv'

    logging.info("Descargando datos CSV desde Google Sheets...")
    df = pd.read_csv(url, low_memory=False)
    logging.info(f"Datos descargados correctamente. {len(df)} filas obtenidas.")
    
    # Mostrar información de las columnas
    logging.info(f"Columnas encontradas: {list(df.columns)}")
    
    # Mostrar ejemplo de las primeras filas para identificar el formato de los datos
    logging.info("Muestra de datos:")
    sample_rows = df.head(5).to_string()
    logging.info(sample_rows)
    
    # Procesar columnas de marca_temporal y fecha
    timestamp_col = None
    timestamp_variants = ['Marca temporal', 'marca_temporal', 'MARCA TEMPORAL', 'Marca Temporal']
    
    # Buscar columna de marca_temporal
    for variant in timestamp_variants:
        if variant in df.columns:
            timestamp_col = variant
            break
    
    if timestamp_col:
        logging.info(f"Encontrada columna de marca temporal: {timestamp_col}")
        logging.info(f"Ejemplos de valores en columna '{timestamp_col}':")
        logging.info(df[timestamp_col].head(10).tolist())
        
        # Standardizar formato de marca_temporal como timestamp
        logging.info(f"Procesando columna de marca temporal: {timestamp_col}")
        df = standardize_date_format(df, timestamp_col, is_timestamp=True)
    else:
        logging.warning("No se encontró columna de marca temporal.")
    
    # Procesar columna de fecha (sin hora)
    date_column = None
    for col in ['Fecha', 'FECHA', 'fecha']:
        if col in df.columns:
            date_column = col
            break
    
    if date_column:
        logging.info(f"Usando columna de fecha: {date_column}")
        # Mostrar ejemplos
        logging.info(f"Ejemplos de valores en columna '{date_column}':")
        logging.info(df[date_column].head(10).tolist())
        
        # Standardizar formato de fechas
        df = standardize_date_format(df, date_column, is_timestamp=False)
    else:
        logging.warning("No se encontró columna de fecha. Continuando sin standardización de fechas.")
    
    # Validar calidad de datos
    quality_issues = validate_data_quality(df)
    
    # Reemplazar NaN con None para que SQL los maneje como NULL
    logging.info("Limpiando datos...")
    df = df.replace({np.nan: None})
    
    # Filtrar solo registros con fechas válidas si existe columna de fecha
    original_count = len(df)
    filtered_count = original_count
    
    if date_column and date_column in df.columns:
        df = df[df[date_column].notna()]
        filtered_count = len(df)
        if original_count != filtered_count:
            logging.warning(f"Se filtraron {original_count - filtered_count} registros con fechas inválidas")
    else:
        logging.info("No se aplicó filtro de fechas ya que no se encontró columna de fecha")
    
    logging.info(f"Registros a procesar: {filtered_count}")
    
    # MySQL connection directa sin SQLAlchemy
    logging.info("Conectando a MySQL...")
    mysql_conn = mysql.connector.connect(
        host="**************",
        user="ncornejo",
        password="N1c0l7as17",
        database="operaciones_tqw"
    )
    mysql_cursor = mysql_conn.cursor()
    
    # Nombre de la tabla a utilizar
    table_name = "tb_tecnico_kpi_documentos"
    logging.info(f"Nombre de tabla a utilizar: {table_name}")
    
    # Crear la tabla si no existe
    sanitized_cols = create_mysql_table(mysql_cursor, df, table_name)
    
    # Eliminar la tabla anterior tb_googlesheets_datos si existe
    logging.info("Eliminando tabla tb_googlesheets_datos si existe...")
    mysql_cursor.execute("DROP TABLE IF EXISTS tb_googlesheets_datos")
    mysql_conn.commit()
    
    # Verificar si la tabla nueva fue creada correctamente
    mysql_cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
    if not mysql_cursor.fetchone():
        logging.error(f"Error: La tabla {table_name} no se creó correctamente")
        raise Exception(f"La tabla {table_name} no existe después de intentar crearla")
    
    logging.info(f"Truncando tabla MySQL {table_name}...")
    mysql_cursor.execute(f"TRUNCATE TABLE {table_name}")
    mysql_conn.commit()
    
    logging.info(f"Insertando datos en tabla MySQL {table_name}...")
    # Renombrar columnas en el DataFrame para que coincidan con las de la tabla
    df.columns = sanitized_cols
    
    # Convertir DataFrame a lista de tuplas para inserción
    cols = df.columns.tolist()
    placeholders = ", ".join(["%s"] * len(cols))
    columns = ", ".join([f"`{col}`" for col in cols])
    
    # Preparar consulta INSERT
    mysql_insert_query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
    
    # Insertar por lotes para mejorar rendimiento
    batch_size = 1000
    total_records = len(df)
    
    try:
        for i in range(0, total_records, batch_size):
            batch_end = min(i + batch_size, total_records)
            # Convertir los valores a lista de tuplas, reemplazando NaN por None
            batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
            mysql_cursor.executemany(mysql_insert_query, batch_data)
            mysql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records} registros en MySQL...")
    except Exception as e:
        logging.error(f"Error al insertar en MySQL: {str(e)}")
        # Mostrar una muestra de los datos para diagnóstico
        logging.error("Muestra de datos que causaron el error:")
        sample = df.iloc[i:i+5] if i < len(df) else df.iloc[-5:]
        logging.error(str(sample))
        logging.error(f"Columnas en df: {list(df.columns)}")
        logging.error(f"Columnas en consulta SQL: {columns}")
        raise
    
    mysql_cursor.close()
    mysql_conn.close()
    logging.info("Datos insertados correctamente en MySQL.")

    logging.info("Proceso completado con éxito.")
    
    # Resumen final
    logging.info("=== RESUMEN DEL PROCESO ===")
    logging.info(f"Registros procesados: {filtered_count}")
    logging.info(f"Problemas de calidad: {len(quality_issues)}")
    if quality_issues:
        for issue in quality_issues:
            logging.info(f"  - {issue}")

except requests.exceptions.RequestException as e:
    logging.error(f"Error al obtener datos del CSV: {str(e)}")
    sys.exit(1)
except mysql.connector.Error as e:
    logging.error(f"Error en MySQL: {str(e)}")
    sys.exit(1)
except Exception as e:
    logging.error(f"Error inesperado: {str(e)}")
    import traceback
    logging.error(f"Detalle del error: {traceback.format_exc()}")
    sys.exit(1)
finally:
    logging.info("Proceso finalizado.")