"""
Sistema Optimizado de Carga Masiva por Chunks con Descarga Automática
Version: 2.1
Soporte: SQL Server + MySQL
Características: Descarga automática + Chunks + Tolerancia a fallos + UTF-8
"""

# Configuración de codificación forzada a UTF-8
import sys
import io
import os
import locale

# Forzar la codificación del sistema a UTF-8
if sys.platform == 'win32':
    # Configurar la consola de Windows para UTF-8
    os.system('chcp 65001 > nul')
    
    # Configurar stdout y stderr para usar UTF-8 con manejo de errores
    if sys.stdout.encoding != 'utf-8' or sys.stderr.encoding != 'utf-8':
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    
    # Configurar la configuración regional para usar UTF-8
    os.environ["PYTHONIOENCODING"] = "utf-8"
    os.environ["PYTHONLEGACYWINDOWSSTDIO"] = "utf-8"
    
    # Configurar la configuración regional para usar UTF-8
    try:
        locale.setlocale(locale.LC_ALL, 'es_CL.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'Spanish_Chile.1252')
        except:
            pass  # Usar configuración regional predeterminada
else:
    # Para sistemas Unix/Linux
    os.environ["PYTHONIOENCODING"] = "utf-8"
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace', line_buffering=True)
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace', line_buffering=True)

import pandas as pd
import pandas.api.types
from sqlalchemy import create_engine
import os
import logging
from datetime import datetime
import time
import sys
import math
import traceback
import subprocess

# Selenium para descarga automática con Edge pero con configuración mejorada
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configuración de logging con manejo de errores mejorado
class SafeFlushStreamHandler(logging.StreamHandler):
    def emit(self, record):
        try:
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            try:
                stream.flush()
            except (OSError, AttributeError) as e:
                # Ignorar errores de flush, pero registrar el error
                pass
        except Exception:
            self.handleError(record)

# Configurar logging
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'toa30_upload_log.log')
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Configurar manejador de archivo
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Configurar manejador de consola solo si no estamos en un entorno sin consola
if hasattr(sys, 'frozen') and hasattr(sys, '_MEIPASS'):
    # Entorno empaquetado (pyinstaller)
    pass
else:
    try:
        console_handler = SafeFlushStreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(console_handler)
    except Exception as e:
        # Si no podemos configurar el manejador de consola, continuar sin él
        pass
logger = logging.getLogger(__name__)

# Configurar la consola para UTF-8 en Windows
if sys.platform.startswith('win'):
    try:
        import os
        os.system('chcp 65001 > nul 2>&1')  # Cambiar a UTF-8 sin mostrar salida
    except:
        pass

class DatabaseConfig:
    """Configuración de bases de datos"""
    SQL_SERVER_CONNECTION = 'mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server'
    MYSQL_CONNECTION = 'mysql+mysqlconnector://ncornejo:N1c0l7as17@**************:3306/operaciones_tqw'

class FileConfig:
    """Configuración de archivos y descarga"""
    # Descarga automática
    DOWNLOAD_URL = 'https://desarrollo.telqway.cl/index.php/s/X5z8TkpncsLcHQx/download/30diasfiltro.xlsx'
    DOWNLOAD_PATH = 'C:\\Users\\<USER>\\Downloads'
    INPUT_FILE = 'C:\\Users\\<USER>\\Downloads\\30diasfiltro.xlsx'
    
    # Configuración de archivo
    SHEET_NAME = 'conso'
    BATCH_SIZE = 5000  # Tamaño de lote para inserción
    DOWNLOAD_WAIT = 25  # Tiempo de espera para descarga en segundos
    
    # Tablas de destino
    SQL_SERVER_TABLE = 'TB_TOA_30DIAS_CLOUD'
    MYSQL_TABLE = 'tb_toa_30dias_cloud'

class FileDownloader:
    """Maneja la descarga automática del archivo"""
    
    def __init__(self):
        self.driver = None
        
    def setup_driver(self) -> bool:
        """Configura el driver de Edge para descarga con opciones avanzadas"""
        try:
            logger.info("[DESCARGA] Configurando navegador Edge...")
            
            # Configurar opciones avanzadas para Edge
            edge_options = webdriver.EdgeOptions()
            
            # Suprimir mensajes de DevTools y errores de segmentación
            edge_options.add_argument('--log-level=3')  # Solo errores fatales
            edge_options.add_argument('--disable-logging')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_argument('--disable-web-security')
            edge_options.add_argument('--disable-features=VizDisplayCompositor')
            edge_options.add_argument('--disable-features=IsolateOrigins,site-per-process')
            
            # Deshabilitar características específicas de Edge que causan errores
            edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            edge_options.add_experimental_option('useAutomationExtension', False)
            edge_options.add_argument('--disable-features=msEdgeOptimizationTargetUserTopicOnUrlProtobuf')
            edge_options.add_argument('--disable-features=OptimizationGuideModelDownloading')
            edge_options.add_argument('--disable-features=OptimizationHintsFetching')
            edge_options.add_argument('--disable-features=OptimizationTargetPrediction')
            edge_options.add_argument('--disable-features=OptimizationHints')
            
            # Configuraciones generales
            edge_options.add_argument('--inprivate')  # Modo privado
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_argument('--disable-extensions')
            edge_options.add_argument('--disable-plugins')
            edge_options.add_argument('--disable-images')  # No cargar imágenes para ser más rápido
            
            # Configurar preferencias para descarga automática
            edge_options.add_experimental_option('prefs', {
                'download.default_directory': FileConfig.DOWNLOAD_PATH,
                'download.prompt_for_download': False,
                'download.directory_upgrade': True,
                'safebrowsing.enabled': False
            })
            
            # Configurar el servicio para suprimir logs
            import os
            os.environ['WDM_LOG_LEVEL'] = '0'  # Suprimir logs de webdriver-manager
            
            # Usar servicio con las opciones configuradas
            service = Service(EdgeChromiumDriverManager().install())
            service.creation_flags = 0x08000000  # CREATE_NO_WINDOW en Windows
            service.log_path = 'NUL' if sys.platform == 'win32' else '/dev/null'
            
            self.driver = webdriver.Edge(service=service, options=edge_options)
            
            # Configurar timeouts más largos
            self.driver.set_page_load_timeout(60)
            self.driver.set_script_timeout(60)
            
            logger.info("[DESCARGA] OK - Navegador Edge configurado correctamente")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error al configurar navegador Edge: {str(e)}")
            logger.error("[ERROR] SOLUCIONES POSIBLES:")
            logger.error("1. Instalar webdriver-manager: pip install webdriver-manager")
            logger.error("2. Verificar que Microsoft Edge esté instalado")
            return False
    
    def download_file(self) -> bool:
        """Descarga el archivo desde el servidor SIEMPRE (eliminando archivo anterior)"""
        try:
            # SIEMPRE eliminar archivo anterior si existe
            if os.path.exists(FileConfig.INPUT_FILE):
                os.remove(FileConfig.INPUT_FILE)
                logger.info("[DESCARGA] Archivo anterior eliminado para descarga fresca")
            else:
                logger.info("[DESCARGA] No hay archivo anterior que eliminar")
            
            logger.info("[DESCARGA] 📥 Iniciando descarga de archivo...")
            logger.info(f"[DESCARGA] URL: {FileConfig.DOWNLOAD_URL}")
            logger.info(f"[DESCARGA] Destino: {FileConfig.INPUT_FILE}")
            
            # Acceder a la URL con manejo mejorado de errores
            try:
                self.driver.get(FileConfig.DOWNLOAD_URL)
                logger.info(f"[DESCARGA] ⏳ Esperando descarga ({FileConfig.DOWNLOAD_WAIT} segundos)...")
                time.sleep(FileConfig.DOWNLOAD_WAIT/2)  # Esperar la mitad del tiempo
                logger.info("[DESCARGA] 🔄 Primer refresco de página...")
                self.driver.refresh()
                time.sleep(FileConfig.DOWNLOAD_WAIT/2)  # Esperar la otra mitad
                logger.info("[DESCARGA] 🔄 Segundo refresco de página para finalizar...")
                self.driver.refresh()
            except Exception as e:
                logger.warning(f"[ADVERTENCIA] Error al navegar: {str(e)}, reintentando...")
                time.sleep(2)
                self.driver.get(FileConfig.DOWNLOAD_URL)
                time.sleep(FileConfig.DOWNLOAD_WAIT)
            
            # Verificar que el archivo se descargó
            if os.path.exists(FileConfig.INPUT_FILE):
                file_size = os.path.getsize(FileConfig.INPUT_FILE)
                logger.info(f"[DESCARGA] OK - Archivo descargado exitosamente ({file_size:,} bytes)")
                return True
            else:
                logger.error("[ERROR] FALLO - El archivo no se descargó correctamente")
                return False
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error("="*60)
            logger.error("ERROR DURANTE LA DESCARGA")
            logger.error("-"*60)
            logger.error(f"Tipo de error: {type(e).__name__}")
            logger.error(f"Mensaje: {str(e)}")
            logger.error("\nDetalles del error:")
            logger.error(error_details)
            
            # Verificar si el driver está disponible
            if hasattr(self, 'driver') and self.driver:
                try:
                    current_url = self.driver.current_url
                    logger.error(f"URL actual del navegador: {current_url}")
                except Exception as url_error:
                    logger.error(f"No se pudo obtener la URL actual: {str(url_error)}")
            else:
                logger.error("El controlador del navegador no está disponible")
                
            logger.error("="*60)
            return False
    
    def close(self):
        """Cierra el navegador Edge"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("[DESCARGA] Navegador Edge cerrado")
        except Exception as e:
            logger.error(f"[ERROR] Error al cerrar navegador: {str(e)}")

# Método simple que funciona para cargar directamente al SQL Server sin usar to_sql
def insert_direct_to_sql_server(df, table_name, engine):
    """
    Inserta un DataFrame directamente en SQL Server usando pyodbc
    Crea la tabla dinámicamente si no existe o recrea si hay cambios en columnas
    """
    import pyodbc
    from sqlalchemy.engine import url
    
    # Obtener las credenciales desde el engine SQLAlchemy
    connection_url = engine.url
    
    # Establecer conexión directa con pyodbc
    conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={connection_url.host};DATABASE={connection_url.database};UID={connection_url.username};PWD={connection_url.password}"
    
    try:
        # Conectar directamente con pyodbc
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Verificar si la tabla existe y si necesita ser recreada
        logger.info(f"Verificando existencia y estructura de tabla {table_name}...")
        cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL SELECT 1 ELSE SELECT 0")
        table_exists = cursor.fetchone()[0] == 1
        
        # Eliminar la tabla si existe (para evitar problemas de estructura)
        if table_exists:
            logger.info(f"Eliminando tabla {table_name} para recrearla con estructura actualizada...")
            cursor.execute(f"DROP TABLE {table_name}")
            conn.commit()
        
        # Crear nueva tabla basada en las columnas del DataFrame
        logger.info(f"Creando tabla {table_name} con estructura actualizada...")
        columns = df.columns.tolist()
        create_columns = []
        
        # Mapear tipos de datos para SQL Server de forma básica
        for col in columns:
            # Detectar tipo de columna analizando todos los valores no nulos
            non_null_values = df[col].dropna()
            
            if non_null_values.empty:
                col_type = "NVARCHAR(MAX)"  # Valor por defecto para columnas completamente NULL
            else:
                # Analizar todos los valores no nulos para determinar el tipo
                is_numeric = True
                max_numeric_value = 0
                has_float = False
                
                for value in non_null_values:
                    if isinstance(value, str):
                        if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                            try:
                                numeric_val = int(value)
                                max_numeric_value = max(max_numeric_value, abs(numeric_val))
                            except (ValueError, OverflowError):
                                is_numeric = False
                                break
                        elif '.' in value and value.replace('.', '').replace('-', '').isdigit():
                            has_float = True
                        else:
                            is_numeric = False
                            break
                    elif isinstance(value, (int, pd.Int64Dtype)):
                        max_numeric_value = max(max_numeric_value, abs(int(value)))
                    elif isinstance(value, (float, pd.Float64Dtype)):
                        has_float = True
                    elif isinstance(value, datetime):
                        col_type = "DATETIME"
                        break
                    else:
                        is_numeric = False
                        break
                
                # Determinar el tipo basado en el análisis
                if 'col_type' not in locals() or col_type != "DATETIME":
                    if has_float:
                        col_type = "FLOAT"
                    elif is_numeric:
                        # Verificar rangos de datos numéricos
                        if max_numeric_value > 9223372036854775807:
                            # Si excede BIGINT, usar DECIMAL para números extremadamente grandes
                            col_type = "DECIMAL(38,0)"
                        elif max_numeric_value > 2147483647:
                            col_type = "BIGINT"
                        else:
                            col_type = "INT"
                    else:
                        col_type = "NVARCHAR(MAX)"
            
            # Usar formato de columna seguro con corchetes
            create_columns.append(f"[{col}] {col_type}")
        
        # Crear tabla con todas las columnas
        create_columns_str = ','.join(create_columns)
        create_query = f"CREATE TABLE {table_name} (\n    {create_columns_str}\n)"
        logger.info(f"Ejecutando: {create_query}")
        cursor.execute(create_query)
        conn.commit()
        
        # Preparar la sentencia INSERT
        column_str = ", ".join([f"[{col}]" for col in columns])
        placeholder_str = ", ".join(["?" for _ in columns])
        
        insert_query = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholder_str})"
        
        # Procesar por lotes para mejor rendimiento
        batch_size = 500
        total_rows = len(df)
        total_batches = (total_rows + batch_size - 1) // batch_size
        
        logger.info(f"Insertando {total_rows:,} registros en SQL Server en {total_batches} lotes...")
        
        # Procesar cada lote
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch = df.iloc[start_idx:end_idx]
            
            # Insertar cada fila del lote
            rows_inserted = 0
            for _, row in batch.iterrows():
                # Convertir NaN a None para SQL Server
                values = [None if pd.isna(val) else val for val in row.values]
                cursor.execute(insert_query, values)
                rows_inserted += 1
            
            conn.commit()
            logger.info(f"Lote {i+1}/{total_batches} completado ({rows_inserted:,} registros)")
        
        cursor.close()
        conn.close()
        logger.info(f"Total {total_rows:,} registros insertados exitosamente en {table_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error al insertar en SQL Server: {str(e)}")
        traceback.print_exc()
        return False

# Método simple que funciona para cargar directamente a MySQL sin usar to_sql
def insert_direct_to_mysql(df, table_name, engine):
    """
    Inserta un DataFrame directamente en MySQL usando mysql.connector
    Crea la tabla dinámicamente si no existe o recrea si hay cambios en columnas
    """
    import mysql.connector
    from sqlalchemy.engine import url
    
    # Obtener las credenciales desde el engine SQLAlchemy
    connection_url = engine.url
    
    try:
        # Conectar directamente con mysql.connector
        conn = mysql.connector.connect(
            host=connection_url.host,
            user=connection_url.username,
            password=connection_url.password,
            database=connection_url.database,
            port=connection_url.port or 3306
        )
        cursor = conn.cursor()
        
        # Verificar si necesitamos recrear la tabla (siempre recreamos para simplificar)
        logger.info(f"Verificando existencia de tabla {table_name}...")
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        table_exists = cursor.fetchone() is not None
        
        # Eliminar la tabla si existe (para evitar problemas de estructura)
        if table_exists:
            logger.info(f"Eliminando tabla {table_name} para recrearla con estructura actualizada...")
            cursor.execute(f"DROP TABLE {table_name}")
            conn.commit()
        
        # Crear nueva tabla basada en las columnas del DataFrame
        logger.info(f"Creando tabla {table_name} con estructura actualizada...")
        columns = df.columns.tolist()
        create_columns = []
        
        # Mapear tipos de datos para MySQL de forma básica
        for col in columns:
            # Detectar tipo de columna analizando todos los valores no nulos
            non_null_values = df[col].dropna()
            
            if non_null_values.empty:
                col_type = "TEXT"  # Valor por defecto para columnas completamente NULL
            else:
                # Analizar todos los valores no nulos para determinar el tipo
                is_numeric = True
                max_numeric_value = 0
                has_float = False
                
                for value in non_null_values:
                    if isinstance(value, str):
                        if value.isdigit() or (value.startswith('-') and value[1:].isdigit()):
                            try:
                                numeric_val = int(value)
                                max_numeric_value = max(max_numeric_value, abs(numeric_val))
                            except (ValueError, OverflowError):
                                is_numeric = False
                                break
                        elif '.' in value and value.replace('.', '').replace('-', '').isdigit():
                            has_float = True
                        else:
                            is_numeric = False
                            break
                    elif isinstance(value, (int, pd.Int64Dtype)):
                        max_numeric_value = max(max_numeric_value, abs(int(value)))
                    elif isinstance(value, (float, pd.Float64Dtype)):
                        has_float = True
                    elif isinstance(value, datetime):
                        col_type = "DATETIME"
                        break
                    else:
                        is_numeric = False
                        break
                
                # Determinar el tipo basado en el análisis
                if 'col_type' not in locals() or col_type != "DATETIME":
                    if has_float:
                        col_type = "DOUBLE"
                    elif is_numeric:
                        # Verificar rangos de datos numéricos
                        if max_numeric_value > 9223372036854775807:
                            # Si excede BIGINT, usar DECIMAL para números extremadamente grandes
                            col_type = "DECIMAL(38,0)"
                        elif max_numeric_value > 2147483647:
                            col_type = "BIGINT"
                        else:
                            col_type = "INT"
                    else:
                        col_type = "TEXT"
            
            # Usar formato de columna seguro con backticks
            create_columns.append(f"`{col}` {col_type}")
        
        # Crear tabla con todas las columnas
        create_columns_str = ','.join(create_columns)
        create_query = f"CREATE TABLE {table_name} (\n    {create_columns_str}\n)"
        logger.info(f"Ejecutando: {create_query}")
        cursor.execute(create_query)
        conn.commit()
        
        # Preparar la sentencia INSERT
        column_str = ", ".join([f"`{col}`" for col in columns])
        placeholder_str = ", ".join(["%s" for _ in columns])
        
        insert_query = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholder_str})"
        
        # Procesar por lotes para mejor rendimiento
        batch_size = 500
        total_rows = len(df)
        total_batches = (total_rows + batch_size - 1) // batch_size
        
        logger.info(f"Insertando {total_rows:,} registros en MySQL en {total_batches} lotes...")
        
        # Procesar cada lote
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch = df.iloc[start_idx:end_idx]
            
            # Preparar los datos del lote
            batch_data = []
            for _, row in batch.iterrows():
                # Convertir NaN a None para MySQL
                values = [None if pd.isna(val) else val for val in row.values]
                batch_data.append(tuple(values))
            
            # Ejecutar en modo batch
            cursor.executemany(insert_query, batch_data)
            conn.commit()
            
            logger.info(f"Lote {i+1}/{total_batches} completado ({len(batch_data):,} registros)")
        
        cursor.close()
        conn.close()
        logger.info(f"Total {total_rows:,} registros insertados exitosamente en {table_name}")
        return True
        
    except Exception as e:
        logger.error(f"Error al insertar en MySQL: {str(e)}")
        traceback.print_exc()
        return False

class DataUploader:
    """Clase simplificada para manejar la carga de datos a las bases de datos"""
    
    def __init__(self):
        self.sql_server_engine = None
        self.mysql_engine = None
        
    def initialize_connections(self):
        """Inicializa las conexiones a las bases de datos"""
        try:
            logger.info("[CONEXIÓN] Inicializando conexiones a bases de datos...")
            
            # SQL Server
            self.sql_server_engine = create_engine(
                DatabaseConfig.SQL_SERVER_CONNECTION,
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # Probar conexión SQL Server
            with self.sql_server_engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("[CONEXIÓN] OK - SQL Server establecida y probada")
            
            # MySQL
            self.mysql_engine = create_engine(
                DatabaseConfig.MYSQL_CONNECTION,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )
            
            # Probar conexión MySQL
            with self.mysql_engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("[CONEXIÓN] OK - MySQL establecida y probada")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error al inicializar conexiones: {str(e)}")
            return False
    
    def validate_file(self, file_path):
        """Valida que el archivo exista y sea accesible"""
        if not os.path.exists(file_path):
            logger.error(f"[ERROR] El archivo no existe: {file_path}")
            return False
            
        file_size = os.path.getsize(file_path)
        logger.info(f"[ARCHIVO] OK - Encontrado: {file_path} ({file_size:,} bytes)")
        return True
    
    def process_file(self, file_path):
        """Procesa el archivo Excel y lo carga en las bases de datos"""
        try:
            logger.info("[INICIO] INICIANDO PROCESO DE CARGA DE DATOS")
            
            # Validar archivo
            if not self.validate_file(file_path):
                return False
            
            # Leer archivo Excel completo
            logger.info("[CARGA] Leyendo archivo Excel...")
            try:
                df_data = pd.read_excel(file_path, sheet_name=FileConfig.SHEET_NAME, dtype=str)
            except ValueError as ve:
                if "No sheet named" in str(ve):
                    logger.warning(f"[ADVERTENCIA] Hoja '{FileConfig.SHEET_NAME}' no encontrada, intentando primera hoja")
                    df_data = pd.read_excel(file_path, dtype=str)
                else:
                    raise
            
            # Agregar fecha de carga
            df_data['fecha_carga'] = datetime.now()
            
            logger.info(f"[CARGA] OK - Archivo Excel leído exitosamente: {len(df_data):,} registros")
            
            # Cargar en SQL Server usando método directo sin to_sql
            logger.info(f"[SQL SERVER] 🔍 Cargando datos en {FileConfig.SQL_SERVER_TABLE}...")
            sql_server_success = insert_direct_to_sql_server(df_data, FileConfig.SQL_SERVER_TABLE, self.sql_server_engine)
            
            if not sql_server_success:
                logger.error(f"[SQL SERVER] ERROR - No se pudieron cargar datos en {FileConfig.SQL_SERVER_TABLE}")
                return False
                
            logger.info(f"[SQL SERVER] OK - Datos cargados exitosamente en {FileConfig.SQL_SERVER_TABLE}")
            
            # Cargar en MySQL usando método directo sin to_sql
            logger.info(f"[MYSQL] 🔍 Cargando datos en {FileConfig.MYSQL_TABLE}...")
            mysql_success = insert_direct_to_mysql(df_data, FileConfig.MYSQL_TABLE, self.mysql_engine)
            
            if not mysql_success:
                logger.error(f"[MYSQL] ERROR - No se pudieron cargar datos en {FileConfig.MYSQL_TABLE}")
                return False
                
            logger.info(f"[MYSQL] OK - Datos cargados exitosamente en {FileConfig.MYSQL_TABLE}")
            
            # Obtener resumen
            summary = self.get_upload_summary()
            
            logger.info("[EXITO] ✨ CARGA DE DATOS COMPLETADA EXITOSAMENTE")
            logger.info(f"[RESUMEN] 📋 Total procesado: {len(df_data):,} registros")
            logger.info(f"[RESUMEN] 📋 SQL Server: {summary.get('sql_server_count', 'N/A'):,} registros")
            logger.info(f"[RESUMEN] 📋 MySQL: {summary.get('mysql_count', 'N/A'):,} registros")
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error en proceso de carga: {str(e)}")
            traceback.print_exc()
            return False
    
    def get_upload_summary(self):
        """Obtiene un resumen de la carga"""
        try:
            summary = {}
            
            # Contar registros en SQL Server
            with self.sql_server_engine.connect() as conn:
                result = conn.execute(f"SELECT COUNT(*) FROM {FileConfig.SQL_SERVER_TABLE}")
                summary['sql_server_count'] = result.scalar()
            
            # Contar registros en MySQL
            with self.mysql_engine.connect() as conn:
                result = conn.execute(f"SELECT COUNT(*) FROM {FileConfig.MYSQL_TABLE}")
                summary['mysql_count'] = result.scalar()
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Error al obtener resumen: {str(e)}")
            return {}
    
    def close_connections(self):
        """Cierra todas las conexiones"""
        try:
            if self.sql_server_engine:
                self.sql_server_engine.dispose()
                logger.info("[CONEXION] SQL Server cerrada")
            
            if self.mysql_engine:
                self.mysql_engine.dispose()
                logger.info("[CONEXION] MySQL cerrada")
                
        except Exception as e:
            logger.error(f"[ERROR] Error al cerrar conexiones: {str(e)}")

def main():
    """Función principal simplificada para descarga y carga de datos"""
    start_time = time.time()
    downloader = FileDownloader()
    uploader = DataUploader()
    
    try:
        logger.info("="*50)
        logger.info("SISTEMA DE CARGA AUTOMATIZADA TOA")
        logger.info("="*50)
        
        # PASO 1: Configurar y descargar archivo
        logger.info("[PASO-1] Configurando descarga...")
        if not downloader.setup_driver():
            logger.error("[FALLO] ERROR - No se pudo configurar el navegador")
            return 1
        
        logger.info("[PASO-2] 📥 Descargando archivo fresco...")
        if not downloader.download_file():
            logger.error("[FALLO] ERROR - No se pudo descargar el archivo")
            return 1
        
        # PASO 2: Inicializar conexiones
        logger.info("[PASO-3] Inicializando conexiones...")
        if not uploader.initialize_connections():
            logger.error("[FALLO] ERROR - No se pudieron inicializar las conexiones")
            return 1
        
        # PASO 3: Procesar archivo
        logger.info("[PASO-4] Procesando archivo...")
        success = uploader.process_file(FileConfig.INPUT_FILE)
        
        if success:
            # Calcular tiempo total
            elapsed_time = time.time() - start_time
            minutes, seconds = divmod(elapsed_time, 60)
            
            logger.info("="*50)
            logger.info("PROCESO COMPLETADO EXITOSAMENTE")
            logger.info(f"Tiempo total: {int(minutes):02d}:{int(seconds):02d}")
            logger.info("="*50)
            return 0
        else:
            logger.error("="*50)
            logger.error("EL PROCESO DE CARGA FALLÓ")
            logger.error("="*50)
            return 1
            
    except Exception as e:
        logger.error(f"[ERROR] Error inesperado: {str(e)}")
        traceback.print_exc()
        return 1
    
    finally:
        # Limpiar recursos
        logger.info("[LIMPIEZA] Cerrando conexiones...")
        try:
            downloader.close()
            uploader.close_connections()
        except:
            pass

if __name__ == "__main__":
    # Guardar referencia a la función print original antes de reemplazarla
    import builtins
    original_print = builtins.print
    
    # Función segura para imprimir que maneja posibles errores de E/S y codificación
    def safe_print(*args, **kwargs):
        try:
            # Forzar la codificación UTF-8 para la salida
            if 'file' not in kwargs or kwargs['file'] in [None, sys.stdout, sys.stderr]:
                # Codificar manualmente a bytes y luego decodificar para manejar caracteres especiales
                try:
                    # Primero intentar con UTF-8
                    encoded_args = []
                    for arg in args:
                        if isinstance(arg, str):
                            # Si ya es un string, asegurarse de que esté en UTF-8
                            try:
                                arg.encode('utf-8')
                                encoded_args.append(arg)
                            except UnicodeEncodeError:
                                # Si hay caracteres no codificables, reemplazarlos
                                encoded_args.append(arg.encode('utf-8', errors='replace').decode('utf-8'))
                        else:
                            encoded_args.append(str(arg))
                    
                    # Usar la función original con los argumentos ya codificados
                    kwargs['flush'] = True
                    original_print(*encoded_args, **kwargs)
                except Exception as encode_error:
                    # Si falla la codificación, intentar con la codificación del sistema
                    try:
                        import locale
                        encoding = locale.getpreferredencoding()
                        kwargs['flush'] = True
                        original_print(*args, **kwargs)
                    except:
                        # Si todo falla, intentar con reemplazo de errores
                        kwargs['errors'] = 'replace'
                        kwargs['flush'] = True
                        original_print(*args, **kwargs)
            else:
                # Si se especifica un archivo de salida, usar la función original
                kwargs['flush'] = True
                original_print(*args, **kwargs)
        except (IOError, OSError) as e:
            # Si hay un error de E/S, intentar escribir en el archivo de log con codificación forzada
            try:
                with open('pytoa30_console.log', 'a', encoding='utf-8', errors='replace') as f:
                    for arg in args:
                        try:
                            f.write(str(arg) + ' ')
                        except:
                            f.write(repr(arg) + ' ')
                    f.write('\n')
            except Exception as log_error:
                # Si no se puede escribir en el archivo, ignorar
                pass
    
    # Reemplazar la función print estándar
    builtins.print = safe_print
    
    print("="*60)
    print("SISTEMA OPTIMIZADO DE DESCARGA Y CARGA DE DATOS")
    print("="*60)
    print(f"Iniciado desde: {sys.argv[0]}")
    print(f"PID: {os.getpid()}")
    print(f"Python: {sys.executable}")
    print(f"Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-"*60)
    print(f"URL: {FileConfig.DOWNLOAD_URL}")
    print(f"ARCHIVO: {FileConfig.INPUT_FILE}")
    print(f"TAMAÑO DE LOTE: {FileConfig.BATCH_SIZE:,} registros")
    print("-"*60)
    
    try:
        # Capturar tiempo inicial para medir rendimiento total
        start_time = time.time()
        print("Iniciando proceso principal...", flush=True)
        
        exit_code = main()
        
        # Calcular tiempo total
        total_time = time.time() - start_time
        minutes, seconds = divmod(total_time, 60)
        
        print("-"*60)
        
        if exit_code == 0:
            # Si el proceso principal fue exitoso, ejecutar test_email_report.py como subproceso
            try:
                print("\n" + "="*60)
                print("EJECUTANDO PROCESO DE REPORTE POR CORREO")
                print("="*60)
                
                # Construir la ruta completa al archivo test_email_report.py
                import os
                script_dir = os.path.dirname(os.path.abspath(__file__))
                test_script_path = os.path.join(script_dir, "test_email_report.py")
                
                # Ejecutar test_email_report.py como subproceso con codificación forzada
                logger.info(f"Ejecutando {test_script_path}...")
                try:
                    # Configurar el entorno para usar UTF-8
                    env = os.environ.copy()
                    env["PYTHONIOENCODING"] = "utf-8"
                    
                    # Ejecutar el script con manejo de codificación
                    result = subprocess.run(
                        [sys.executable, test_script_path],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        encoding='utf-8',
                        errors='replace',  # Reemplazar caracteres no decodificables
                        env=env
                    )
                except Exception as e:
                    logger.error(f"Error al ejecutar test_email_report.py: {str(e)}")
                    result = subprocess.CompletedProcess(
                        args=[sys.executable, test_script_path],
                        returncode=1,
                        stdout="",
                        stderr=f"Error al ejecutar el script: {str(e)}"
                    )
                
                if result.returncode == 0:
                    print("OK - Proceso de envío de correo completado exitosamente")
                    logger.info("test_email_report.py ejecutado exitosamente")
                else:
                    print(f"ERROR - No se pudo ejecutar test_email_report.py: código de salida {result.returncode}")
                    if result.stderr:
                        try:
                            # Intentar decodificar el stderr como UTF-8, con reemplazo de errores
                            error_output = result.stderr
                            if isinstance(error_output, bytes):
                                error_output = error_output.decode('utf-8', errors='replace')
                            logger.error(f"Error: {error_output}")
                        except Exception as decode_error:
                            logger.error(f"Error al decodificar la salida de error: {str(decode_error)}")
                
                print("="*60 + "\n")
                
            except Exception as e:
                print(f"\nERROR al ejecutar test_email_report.py: {str(e)}")
                import traceback
                traceback.print_exc()
                print("="*60 + "\n")
            print(f"TERMINADO OK: Programa ejecutado exitosamente en {int(minutes):02d}:{int(seconds):02d}")
        else:
            print(f"TERMINADO CON ERRORES: Programa terminó con errores en {int(minutes):02d}:{int(seconds):02d}")
            
    except KeyboardInterrupt:
        print("\nINTERRUMPIDO: Proceso interrumpido por el usuario")
        exit_code = 1
    except Exception as e:
        print(f"\nERROR FATAL: {str(e)}")
        print(f"Tipo: {type(e).__name__}")
        traceback.print_exc()
        exit_code = 1
    
    # No usar sys.exit() para evitar SystemExit exception
    print(f"Código de salida: {exit_code}")
    
    # Cerrar el script automáticamente
    sys.exit(exit_code)