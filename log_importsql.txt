Configurado para conectar a SQL Server en: ************
Leyendo archivo Excel: C:/Users/<USER>/Downloads/base_ftth_mar25.xlsx
Detectado: archivo con encabezados
Archivo cargado con 194 filas y 96 columnas
Conectando a SQL Server...
Conexión establecida
Analizando columna: Técnico - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Orden de Trabajo - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tipo de Actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Subtipo - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tipo de Orden - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Franja - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Inicio - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Fin - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Ciudad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Número Cliente - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Clase de Vivienda - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Código Ciudad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Código Localidad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Código Territorio - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Código Zona - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Comentarios de la actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(382)
Analizando columna: Coord X - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Coord Y - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Fecha - Tipo pandas: datetime64[ns]
  -> Tipo SQL decidido: DATETIME
Analizando columna: Ventana de Entrega - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Descripción de la actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Duración - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Estado de la actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Fecha Certificada - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag Estado Aprovisión - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag Fallas Masivas - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag Materiales - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Notas Materiales - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(274)
Analizando columna: Flag Niveles - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Grupo Socioeconomico - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Indicador Capacidad - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Area derivación - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Nodo - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: Nro Orden - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Nro Solicitud de Servicio - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Prioridad - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Razón de Cancelación - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Inicio - Fin - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Estado - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Provincia - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Subnodo - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: Territorio - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tipo de Vivienda - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tiempo de viaje - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Usuario Creador Actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Zona de trabajo - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Código postal - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Zona - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Ventana de Servicio - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Código de Cierre - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: ID de actividad - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Rut o Bucket - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Pasos - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(2502)
Analizando columna: Hora de reserva de actividad - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag Corte de Acometida - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Flag Retiro de Materiales - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Flag Televisión Análoga - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag que indica si hay Internet - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag que indica si hay Telefonía - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Flag que indica si hay Televisión - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Equipos sin retirar - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Inicio SLA - Tipo pandas: object
  -> Tipo SQL decidido: INT
Analizando columna: Activity status change by - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: MAC del MTA - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tipo Red - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Fecha (mes) - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: CODI EMPRESA - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: RUT_TECNICO - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: IPTV - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: ONT - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: EXTENSOR - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: FONO - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: REPA - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: TOTAL RGU - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: key - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Tipo_Loca - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Largo_Pasos - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Flag_escuela - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Flag_VTRCheck - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Largo - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Detalle Orden HRS Extras - Tipo pandas: float64
  -> Tipo SQL decidido: INT
Analizando columna: Flag_TELQ - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Subzona2 - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Marca - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Modelo - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: SSTT NDC#DESC_TRABAJO - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Cuenta - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Fecha (índice de meses) - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: Columna1 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Columna2 - Tipo pandas: object
  -> Tipo SQL decidido: NVARCHAR(255)
Analizando columna: Columna3 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Columna4 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Columna5 - Tipo pandas: float64
  -> Tipo SQL decidido: FLOAT
Analizando columna: Columna6 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Columna7 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Analizando columna: Columna8 - Tipo pandas: int64
  -> Tipo SQL decidido: INT
Eliminando tabla tb_paso_factura_vtr_base_FTTH si existe...
Creando tabla tb_paso_factura_vtr_base_FTTH con esquema optimizado...
Tipos de columnas: [Técnico] NVARCHAR(255), [Orden de Trabajo] NVARCHAR(255), [Tipo de Actividad] NVARCHAR(255), [Subtipo] NVARCHAR(255)
, [Tipo de Orden] NVARCHAR(255), [Franja] NVARCHAR(255), [Inicio] NVARCHAR(255), [Fin] NVARCHAR(255), [Ciudad] NVARCHAR(255), [Número Cl
iente] NVARCHAR(255), [Clase de Vivienda] INT, [Código Ciudad] NVARCHAR(255), [Código Localidad] NVARCHAR(255), [Código Territorio] NVAR
CHAR(255), [Código Zona] NVARCHAR(255), [Comentarios de la actividad] NVARCHAR(382), [Coord X] INT, [Coord Y] INT, [Fecha] DATETIME, [Ve
ntana de Entrega] NVARCHAR(255), [Descripción de la actividad] NVARCHAR(255), [Duración] NVARCHAR(255), [Estado de la actividad] NVARCHA
R(255), [Fecha Certificada] NVARCHAR(255), [Flag Estado Aprovisión] NVARCHAR(255), [Flag Fallas Masivas] NVARCHAR(255), [Flag Materiales
] NVARCHAR(255), [Notas Materiales] NVARCHAR(274), [Flag Niveles] NVARCHAR(255), [Grupo Socioeconomico] NVARCHAR(255), [Indicador Capaci
dad] INT, [Area derivación] NVARCHAR(255), [Nodo] FLOAT, [Nro Orden] NVARCHAR(255), [Nro Solicitud de Servicio] INT, [Prioridad] INT, [R
azón de Cancelación] INT, [Inicio - Fin] NVARCHAR(255), [Estado] NVARCHAR(255), [Provincia] INT, [Subnodo] FLOAT, [Territorio] NVARCHAR(
255), [Tipo de Vivienda] NVARCHAR(255), [Tiempo de viaje] NVARCHAR(255), [Usuario Creador Actividad] NVARCHAR(255), [Zona de trabajo] NV
ARCHAR(255), [Código postal] INT, [Zona] NVARCHAR(255), [Ventana de Servicio] NVARCHAR(255), [Código de Cierre] NVARCHAR(255), [ID de ac
tividad] INT, [Rut o Bucket] NVARCHAR(255), [Pasos] NVARCHAR(2502), [Hora de reserva de actividad] NVARCHAR(255), [Flag Corte de Acometi
da] INT, [Flag Retiro de Materiales] INT, [Flag Televisión Análoga] NVARCHAR(255), [Flag que indica si hay Internet] NVARCHAR(255), [Fla
g que indica si hay Telefonía] NVARCHAR(255), [Flag que indica si hay Televisión] NVARCHAR(255), [Equipos sin retirar] INT, [Inicio SLA]
 INT, [Activity status change by] NVARCHAR(255), [MAC del MTA] NVARCHAR(255), [Tipo Red] NVARCHAR(255), [Fecha (mes)] NVARCHAR(255), [CO
DI EMPRESA] NVARCHAR(255), [RUT_TECNICO] NVARCHAR(255), [IPTV] FLOAT, [ONT] INT, [EXTENSOR] FLOAT, [FONO] INT, [REPA] INT, [TOTAL RGU] F
LOAT, [key] NVARCHAR(255), [Tipo_Loca] NVARCHAR(255), [Largo_Pasos] INT, [Flag_escuela] INT, [Flag_VTRCheck] INT, [Largo] INT, [Detalle
Orden HRS Extras] INT, [Flag_TELQ] INT, [Subzona2] NVARCHAR(255), [Marca] NVARCHAR(255), [Modelo] NVARCHAR(255), [SSTT NDC#DESC_TRABAJO]
 NVARCHAR(255), [Cuenta] INT, [Fecha (índice de meses)] FLOAT, [Columna1] INT, [Columna2] NVARCHAR(255), [Columna3] INT, [Columna4] INT,
 [Columna5] FLOAT, [Columna6] INT, [Columna7] INT, [Columna8] INT
Tabla creada correctamente
Iniciando carga de datos en chunks de 100 filas...
Procesando chunk 1/2 (0-100)
Error detallado: invalid literal for int() with base 10: 'mes'