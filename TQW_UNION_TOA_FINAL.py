import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta

import sqlalchemy


data_xls = pd.read_excel(r'C:\Users\<USER>\Downloads\Consolidado Actividades Andes VTR RGU Dic-23.xlsx', 'Base', dtype=str, index_col=None)



# data_xls = pd.read_excel(r'C:\Users\<USER>\Downloads\Consolidado Actividades Andes VTR RGU Nov-23.xlsx', 'Base', dtype=str, index_col=None)



# data_xls_Oct = pd.read_excel(r'C:\Users\<USER>\Downloads\Consolidado Actividades Andes VTR RGU Oct-23.xlsx', 'Base', dtype=str, index_col=None)


# data_xls_Sep = pd.read_excel(r'C:\Users\<USER>\Downloads\Consolidado Actividades Andes VTR RGU Sep-23.xlsx', 'Base', dtype=str, index_col=None)

# # Unir los DataFrames
# frames = [data_xls, data_xls_Oct, data_xls_Sep]

# # Utilizar el método concat para realizar la unión
# resultado_union = pd.concat(frames)


# # Exportar el resultado de la unión a un archivo CSV
# resultado_union.to_csv(r'C:\Users\<USER>\Downloads\resultado_union.csv', index=False)




engine = create_engine('mssql+pyodbc://sa:N1c0l7as@************/master?driver=ODBC Driver 17 for SQL Server')

Session = sessionmaker(bind=engine)
session = Session()

df = pd.DataFrame(data_xls)




df.insert(110, 'Tipo_work',df['tipo_actividad'] )

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@*************:3306/telqwayc_db_operacion', echo=False)

# # Crear un data frame a partir de una consulta a SQL Server
# query = '''
# SELECT 
#     [Número Cliente], [Orden de Trabajo], [Código de Cierre], [Notas de Cierre],
#     [Area derivación], Estado, Dirección, [Coord X], [Coord Y], Fin, Inicio, Técnico 
# FROM 
#     toa_BI_estatico tbe
# '''
# data = pd.read_sql_query(query, engine)

# # Guarda el dataframe en MySQL
# data.to_sql('TB_TOA_ESTATICO', engineMYsql, if_exists='replace', index=False)







#print(df)

#df.to_sql('tb_andes', engine, if_exists='append', index=False)

#df = df.head(10)



df.rename(columns={'TipoWorkSkill':'tipo_actividad', 'tipo_actividad':'evalua_dpendiente', 'evalua_dpendiente':'Origen', 'Origen':'RGU_OPERACIONES'}, inplace=True)


dtype = {'Técnico': sqlalchemy.types.VARCHAR(100)
,'Orden de Trabajo': sqlalchemy.types.VARCHAR(255),
'Tipo de Actividad': sqlalchemy.types.VARCHAR(255),
'Subtipo': sqlalchemy.types.VARCHAR(255),
'Tipo de Orden': sqlalchemy.types.VARCHAR(255),
'Franja': sqlalchemy.types.VARCHAR(255),
'Inicio': sqlalchemy.types.VARCHAR(255),
'Fin': sqlalchemy.types.VARCHAR(255),
'Cliente': sqlalchemy.types.VARCHAR(255),
'Dirección': sqlalchemy.types.VARCHAR(255),
'Ciudad': sqlalchemy.types.VARCHAR(255),
'Número Cliente': sqlalchemy.types.VARCHAR(255),
'Celular': sqlalchemy.types.VARCHAR(255),
'Clase de Vivienda': sqlalchemy.types.VARCHAR(255),
'Código Ciudad': sqlalchemy.types.VARCHAR(255),
'Código Localidad': sqlalchemy.types.VARCHAR(255),
'Código Territorio': sqlalchemy.types.VARCHAR(255),
'Código Zona': sqlalchemy.types.VARCHAR(255),
'Comentarios de la actividad': sqlalchemy.types.VARCHAR(4000),
'Coord X': sqlalchemy.types.VARCHAR(255),
'Coord Y': sqlalchemy.types.VARCHAR(255),
'Fecha': sqlalchemy.types.VARCHAR(255),
'Ventana de Entrega': sqlalchemy.types.VARCHAR(255),
'Descripción de la actividad': sqlalchemy.types.VARCHAR(255),
'Duración': sqlalchemy.types.VARCHAR(255),
'email': sqlalchemy.types.VARCHAR(255),
'Estado de la actividad': sqlalchemy.types.VARCHAR(255),
'Fecha Certificada': sqlalchemy.types.VARCHAR(255),
'Flag Estado Aprovisión': sqlalchemy.types.VARCHAR(255),
'Flag Fallas Masivas': sqlalchemy.types.VARCHAR(255),
'Flag Materiales': sqlalchemy.types.VARCHAR(255),
'Notas Materiales': sqlalchemy.types.VARCHAR(4000),
'Flag Niveles': sqlalchemy.types.VARCHAR(255),
'Grupo Socioeconomico': sqlalchemy.types.VARCHAR(255),
'Indicador Capacidad': sqlalchemy.types.VARCHAR(255),
'Area derivación': sqlalchemy.types.VARCHAR(255),
'Nodo':  sqlalchemy.types.VARCHAR(255),
'Nombre Completo Persona': sqlalchemy.types.VARCHAR(255),
'Nro Orden': sqlalchemy.types.VARCHAR(255),
'Nro Solicitud de Servicio': sqlalchemy.types.VARCHAR(255),
'Teléfono': sqlalchemy.types.VARCHAR(255),
'Prioridad': sqlalchemy.types.VARCHAR(255),
'Razón de Cancelación': sqlalchemy.types.VARCHAR(255),
'Inicio - Fin': sqlalchemy.types.VARCHAR(255),
'Estado': sqlalchemy.types.VARCHAR(255),
'Provincia': sqlalchemy.types.VARCHAR(255),
'Subnodo': sqlalchemy.types.VARCHAR(255),
'Territorio': sqlalchemy.types.VARCHAR(255),
'Tipo de Vivienda': sqlalchemy.types.VARCHAR(255),
'Tiempo de viaje': sqlalchemy.types.VARCHAR(255),
'Usuario Creador Actividad': sqlalchemy.types.VARCHAR(255),
'Zona de trabajo': sqlalchemy.types.VARCHAR(255),
'Código postal': sqlalchemy.types.VARCHAR(255),
'Zona': sqlalchemy.types.VARCHAR(255),
'Ventana de Servicio': sqlalchemy.types.VARCHAR(255),
'Código de Cierre': sqlalchemy.types.VARCHAR(255),
'Notas de Cierre (Largo máx 400)': sqlalchemy.types.VARCHAR(4000),
'ID de actividad': sqlalchemy.types.VARCHAR(255),
'Tipo de Recurso Destino': sqlalchemy.types.VARCHAR(255),
'Pasos': sqlalchemy.types.VARCHAR(4000),#
'Hora de reserva de actividad': sqlalchemy.types.VARCHAR(255),
'Flag Corte de Acometida': sqlalchemy.types.VARCHAR(255),
'Flag Retiro de Materiales': sqlalchemy.types.VARCHAR(255),
'Flag Televisión Análoga': sqlalchemy.types.VARCHAR(255),
'Flag que indica si hay Internet': sqlalchemy.types.VARCHAR(255),
'Flag que indica si hay Telefonía': sqlalchemy.types.VARCHAR(255),
'Flag que indica si hay Televisión': sqlalchemy.types.VARCHAR(255),
'Equipos sin retirar': sqlalchemy.types.VARCHAR(255),
'Inicio SLA': sqlalchemy.types.VARCHAR(255),
'Activity status change by': sqlalchemy.types.VARCHAR(255),
'Tipo_Red': sqlalchemy.types.VARCHAR(255),
'Qact': sqlalchemy.types.VARCHAR(255),
'Código GIS': sqlalchemy.types.VARCHAR(255),
'Categorización del número de derivaciones': sqlalchemy.types.VARCHAR(255),
'Agenda confirmada': sqlalchemy.types.VARCHAR(255),
'Criterios de priorización': sqlalchemy.types.VARCHAR(255),
'Cantidad de derivaciones NO terreno': sqlalchemy.types.VARCHAR(255),
'Cantidad de derivaciones terreno': sqlalchemy.types.VARCHAR(255),
'downgrade': sqlalchemy.types.VARCHAR(255),
'Equivalencia RGU': sqlalchemy.types.VARCHAR(255),
'oldrgu': sqlalchemy.types.VARCHAR(255),
'Zona 2': sqlalchemy.types.VARCHAR(255),
'EPS': sqlalchemy.types.VARCHAR(255),
'EPS_produc': sqlalchemy.types.VARCHAR(255),
'cod_bucket': sqlalchemy.types.VARCHAR(255),
'Bucket': sqlalchemy.types.VARCHAR(255),
'Tramo': sqlalchemy.types.VARCHAR(255),
'AG': sqlalchemy.types.VARCHAR(255),
'Dia': sqlalchemy.types.VARCHAR(255),
'semana': sqlalchemy.types.VARCHAR(255),
'Cantidad_Reag': sqlalchemy.types.VARCHAR(255),
'Fecha_Ingreso': sqlalchemy.types.VARCHAR(255),
'EOS': sqlalchemy.types.VARCHAR(255),
'Reconexión': sqlalchemy.types.VARCHAR(255),
'cumple_franja': sqlalchemy.types.VARCHAR(255),
'Orden': sqlalchemy.types.VARCHAR(255),
'Rut': sqlalchemy.types.VARCHAR(255),
'hora': sqlalchemy.types.VARCHAR(255),
'Visita': sqlalchemy.types.VARCHAR(255),
'PxDIa': sqlalchemy.types.VARCHAR(255),
'dia_semana': sqlalchemy.types.VARCHAR(255),
'Tipo_EPS': sqlalchemy.types.VARCHAR(255),
'Cumple_Inicio': sqlalchemy.types.VARCHAR(255),
'Condicion_inclumplimiento': sqlalchemy.types.VARCHAR(255),
'Q_reit': sqlalchemy.types.VARCHAR(255),
'Mac': sqlalchemy.types.VARCHAR(4000),
'Tipo de work skill Siebel': sqlalchemy.types.VARCHAR(255),
'evalua_dpendiente': sqlalchemy.types.VARCHAR(255),
'Origen': sqlalchemy.types.VARCHAR(255),
'RGU_OPERACIONES': sqlalchemy.types.VARCHAR(255),
'Tipo_work': sqlalchemy.types.VARCHAR(255)
}



#df2 = pd.DataFrame(df,dtype=dtype)
#df = df[['Notas Materiales','Pasos','Notas de Cierre (Largo máx 400)','Comentarios de la actividad','Notas Materiales']]
#maximos = df.max(axis=0)

# Imprimir el resultado
#print(maximos)
#df.to_excel('C:/Users/<USER>/Downloads/TOA_FLUJO.xlsx', index = False) 


# specify the data types for the columns
#dtypes = {'Subtipo': sqlalchemy.types.VARCHAR(length=100), 'Franja': sqlalchemy.types.VARCHAR(length=100)}


#df_1000 = df.head(1000)



df.to_sql('tb_andes_python', engine, if_exists='replace',index=False, dtype=dtype)



session.execute("EXEC SP_INSERT_TOA_FIN '202312'")
session.commit()

## Close the session
session.close()
