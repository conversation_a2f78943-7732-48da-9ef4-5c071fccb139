import pandas as pd
import datetime as date
import requests
import json
import mysql.connector
# import pyodbc  # No se requiere SQL Server
import numpy as np
from datetime import datetime, timedelta
import sys
import logging

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('PyAuditoriaTerreno.log'),
        logging.StreamHandler()
    ]
)

# Función para convertir fechas con múltiples formatos (definida a nivel global)
def parse_date(date_str):
    if pd.isna(date_str) or date_str is None:
        return None
        
    date_str = str(date_str).strip()
    
    # Lista de formatos posibles
    formats = [
        '%d/%m/%Y',    # 29/5/2023
        '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
        '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
        '%m/%d/%Y',    # 5/29/2023
        '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
        '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
        '%Y-%m-%d',    # 2023-05-29
        '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
        '%d-%m-%Y',    # 29-05-2023
        '%m-%d-%Y',    # 05-29-2023
    ]
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            # Determinar si es una fecha con hora o solo fecha
            if '%H' in fmt:
                # Si tiene hora, preservar el formato datetime completo para MySQL
                return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                # Si es solo fecha, usar formato de fecha para MySQL
                return parsed_date.strftime('%Y-%m-%d')
        except ValueError:
            continue
    
    logging.warning(f"No se pudo parsear la fecha: {date_str}")
    return None

# Función para convertir timestamps (definida a nivel global)
def parse_timestamp(ts_str):
    if pd.isna(ts_str) or ts_str is None:
        return None
            
    ts_str = str(ts_str).strip()
    
    # Formatos específicos para timestamps
    timestamp_formats = [
        '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
        '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
        '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
        '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
        '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
        '%Y-%m-%d %H:%M',     # 2023-05-29 13:05
    ]
    
    for fmt in timestamp_formats:
        try:
            parsed_date = datetime.strptime(ts_str, fmt)
            # Formato para MySQL
            return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
        except ValueError:
            continue
    
    # Si no funciona ningún formato timestamp, intentar como fecha simple y agregar hora 00:00:00
    for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y']:
        try:
            parsed_date = datetime.strptime(ts_str, fmt)
            # Formato para MySQL con hora 00:00:00
            return parsed_date.strftime('%Y-%m-%d 00:00:00')
        except ValueError:
            continue
    
    logging.warning(f"No se pudo parsear la marca temporal: {ts_str}")
    return None

def standardize_date_format(df, date_column='Fecha', is_timestamp=False):
    """
    Standardiza el formato de fechas en el DataFrame
    """
    if date_column not in df.columns:
        logging.warning(f"Columna {date_column} no encontrada en el DataFrame")
        return df
    
    logging.info(f"Standardizando formato de fechas en columna: {date_column}")
    
    # Aplicar la función de conversión
    original_count = len(df)
    if is_timestamp:
        # Para marca_temporal, usamos un formato específico
        logging.info(f"Procesando columna {date_column} como marca temporal (fecha+hora)")
        
        def parse_timestamp(ts_str):
            if pd.isna(ts_str) or ts_str is None:
                return None
                
            ts_str = str(ts_str).strip()
            
            # Formatos específicos para timestamps
            timestamp_formats = [
                '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
                '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
                '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
                '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
                '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
                '%Y-%m-%d %H:%M',     # 2023-05-29 13:05
            ]
            
            for fmt in timestamp_formats:
                try:
                    parsed_date = datetime.strptime(ts_str, fmt)
                    # Formato para MySQL
                    return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            
            # Si no funciona ningún formato timestamp, intentar como fecha simple y agregar hora 00:00:00
            for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y']:
                try:
                    parsed_date = datetime.strptime(ts_str, fmt)
                    # Formato para MySQL con hora 00:00:00
                    return parsed_date.strftime('%Y-%m-%d 00:00:00')
                except ValueError:
                    continue
            
            logging.warning(f"No se pudo parsear la marca temporal: {ts_str}")
            return None
        
        df[date_column] = df[date_column].apply(parse_timestamp)
    else:
        # Para fechas normales
        df[date_column] = df[date_column].apply(parse_date)
    
    # Contar fechas válidas vs inválidas
    valid_dates = df[date_column].notna().sum()
    invalid_dates = original_count - valid_dates
    
    logging.info(f"Fechas procesadas: {valid_dates} válidas, {invalid_dates} inválidas")
    
    if invalid_dates > 0:
        logging.warning("Registros con fechas inválidas:")
        invalid_samples = df[df[date_column].isna()].head()
        for idx, row in invalid_samples.iterrows():
            logging.warning(f"  Fila {idx}: {row.get('RUT', 'N/A')} - {row.get('NOMBRE', 'N/A')}")
    
    return df

def validate_data_quality(df):
    """
    Valida la calidad de los datos
    """
    logging.info("Validando calidad de datos...")
    
    issues = []
    
    # Verificar las columnas presentes en el DataFrame y definir columnas críticas
    available_columns = df.columns.tolist()
    logging.info(f"Columnas disponibles en el CSV: {available_columns}")
    
    # Intentar determinar columnas críticas basado en nombres comunes con variaciones en mayúsculas/minúsculas
    critical_columns = []
    possible_date_columns = ['Fecha', 'FECHA', 'fecha']
    possible_rut_columns = ['Rut', 'RUT', 'rut']
    possible_name_columns = ['Nombre', 'NOMBRE', 'nombre']
    possible_zone_columns = ['Zona', 'ZONA', 'zona']
    
    # Buscar columnas de fecha
    date_column_found = False
    for col in possible_date_columns:
        if col in available_columns:
            critical_columns.append(col)
            date_column_found = True
            logging.info(f"Columna de fecha encontrada: {col}")
            break
    
    # Buscar columnas de RUT
    for col in possible_rut_columns:
        if col in available_columns:
            critical_columns.append(col)
            logging.info(f"Columna de RUT encontrada: {col}")
            break
    
    # Buscar columnas de nombre
    for col in possible_name_columns:
        if col in available_columns:
            critical_columns.append(col)
            logging.info(f"Columna de nombre encontrada: {col}")
            break
    
    # Buscar columnas de zona
    for col in possible_zone_columns:
        if col in available_columns:
            critical_columns.append(col)
            logging.info(f"Columna de zona encontrada: {col}")
            break
    
    if not date_column_found:
        logging.warning("No se encontró una columna de fecha reconocible en el CSV")
        # Verificar si existe alguna columna que contenga la palabra 'fecha'
        date_related_columns = [col for col in available_columns if 'fecha' in col.lower()]
        if date_related_columns:
            logging.info(f"Columnas posiblemente relacionadas con fechas: {date_related_columns}")
    for col in critical_columns:
        if col in df.columns:
            null_count = df[col].isna().sum()
            if null_count > 0:
                issues.append(f"Columna {col}: {null_count} valores nulos")
    
    # Verificar rango de fechas si se encontró una columna de fecha
    date_col = None
    for col in possible_date_columns:
        if col in df.columns:
            date_col = col
            break
    
    if date_col:
        logging.info(f"Verificando rango de fechas para columna: {date_col}")
        valid_dates = pd.to_datetime(df[date_col], format='%m/%d/%Y', errors='coerce')
        min_date = valid_dates.min()
        max_date = valid_dates.max()
        
        if min_date and max_date:
            logging.info(f"Rango de fechas: {min_date.date()} a {max_date.date()}")
            
            # Verificar si hay fechas muy antiguas o muy futuras
            today = datetime.now()
            if min_date < today - timedelta(days=365):
                issues.append(f"Hay fechas muy antiguas (anterior a {(today - timedelta(days=365)).date()})")
            if max_date > today + timedelta(days=365):
                issues.append(f"Hay fechas muy futuras (posterior a {(today + timedelta(days=365)).date()})")
    
    if issues:
        logging.warning("Problemas de calidad detectados:")
        for issue in issues:
            logging.warning(f"  - {issue}")
    else:
        logging.info("Validación de calidad: OK")
    
    return issues

try:
    logging.info("Iniciando proceso de obtención y carga de auditorías de terreno...")
    url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vQuinsjEhd5s4UCYXZKS4JDR68WnHtkSpnk6tty4m6ORZCnDf2oyv6pyb-ntmz0xK4U7Ss-04i4i6zi/pub?output=csv'

    logging.info("Descargando datos CSV desde Google Sheets...")
    df = pd.read_csv(url, low_memory=False)
    logging.info(f"Datos descargados correctamente. {len(df)} filas obtenidas.")
    
    # Mostrar información de las columnas
    logging.info(f"Columnas encontradas: {list(df.columns)}")
    
    # Mostrar ejemplo de las primeras filas para identificar el formato de los datos
    logging.info("Muestra de datos:")
    sample_rows = df.head(5).to_string()
    logging.info(sample_rows)
    
    # Procesar columnas de marca_temporal y fecha
    timestamp_col = None
    timestamp_variants = ['Marca temporal', 'marca_temporal', 'MARCA TEMPORAL', 'Marca Temporal']
    
    # Buscar columna de marca_temporal
    for variant in timestamp_variants:
        if variant in df.columns:
            timestamp_col = variant
            break
    
    if timestamp_col:
        logging.info(f"Encontrada columna de marca temporal: {timestamp_col}")
        logging.info(f"Ejemplos de valores en columna '{timestamp_col}':")
        logging.info(df[timestamp_col].head(10).tolist())
        
        # Standardizar formato de marca_temporal como timestamp
        logging.info(f"Procesando columna de marca temporal: {timestamp_col}")
        df = standardize_date_format(df, timestamp_col, is_timestamp=True)
    else:
        logging.warning("No se encontró columna de marca temporal.")
    
    # Procesar columna de fecha (sin hora)
    date_column = None
    for col in ['Fecha', 'FECHA', 'fecha']:
        if col in df.columns:
            date_column = col
            break
    
    if date_column:
        logging.info(f"Usando columna de fecha: {date_column}")
        # Mostrar ejemplos
        logging.info(f"Ejemplos de valores en columna '{date_column}':")
        logging.info(df[date_column].head(10).tolist())
        
        # Standardizar formato de fechas
        df = standardize_date_format(df, date_column, is_timestamp=False)
    else:
        logging.warning("No se encontró columna de fecha. Continuando sin standardización de fechas.")
    
    # Validar calidad de datos
    quality_issues = validate_data_quality(df)
    
    # Reemplazar NaN con None para que SQL los maneje como NULL
    logging.info("Limpiando datos...")
    df = df.replace({np.nan: None})
    
    # Filtrar solo registros con fechas válidas si existe columna de fecha
    original_count = len(df)
    filtered_count = original_count
    
    if date_column and date_column in df.columns:
        df = df[df[date_column].notna()]
        filtered_count = len(df)
        if original_count != filtered_count:
            logging.warning(f"Se filtraron {original_count - filtered_count} registros con fechas inválidas")
    else:
        logging.info("No se aplicó filtro de fechas ya que no se encontró columna de fecha")
    
    if original_count != filtered_count:
        logging.warning(f"Se filtraron {original_count - filtered_count} registros con fechas inválidas")
    
    logging.info(f"Registros a procesar: {filtered_count}")
    
    # MySQL connection directa sin SQLAlchemy
    logging.info("Conectando a MySQL...")
    mysql_conn = mysql.connector.connect(
        host="**************",
        user="ncornejo",
        password="N1c0l7as17",
        database="operaciones_tqw"
    )
    mysql_cursor = mysql_conn.cursor()
    
    # Obtener columnas de la tabla MySQL excluyendo la primera columna (id autoincremental)
    logging.info("Obteniendo esquema de la tabla MySQL tb_auditorias_terreno...")
    mysql_cursor.execute("SHOW COLUMNS FROM tb_auditorias_terreno")
    table_columns = [row[0] for row in mysql_cursor.fetchall()]
    
    # Excluir la columna ID que es autoincremental
    if 'id' in table_columns:
        table_columns.remove('id')
    
    logging.info(f"Columnas en MySQL (sin ID): {table_columns}")
    logging.info(f"Total de columnas MySQL: {len(table_columns)}")
    logging.info(f"Total de columnas CSV: {len(df.columns)}")
    
    # Verificar si el número de columnas coincide aproximadamente
    if abs(len(df.columns) - len(table_columns)) > 5:  # Permitimos una pequeña diferencia
        logging.warning("El número de columnas entre el CSV y la tabla MySQL difiere significativamente")
        logging.warning(f"CSV: {len(df.columns)} columnas, MySQL: {len(table_columns)} columnas")
        logging.warning("Continuando de todos modos con inserción directa...")
    
    # Preparamos directamente el DataFrame para inserción, asumiendo que las columnas coinciden en orden
    # Utilizamos un máximo de columnas igual al número de columnas en la tabla MySQL
    max_cols = min(len(df.columns), len(table_columns))
    
    # Crear un nuevo DataFrame con las primeras max_cols columnas del CSV
    logging.info("Preparando datos para inserción directa por posición...")
    df_mysql = df.iloc[:, :max_cols].copy()
    
    # Asignar nombres de columnas de la tabla MySQL al DataFrame
    df_mysql.columns = table_columns[:max_cols]
    
    logging.info(f"Columnas mapeadas por posición: {list(df_mysql.columns)}")
    
    # Verificar la columna fecha para asegurarnos de que está correctamente formateada
    if 'fecha' in df_mysql.columns:
        df_mysql['fecha'] = df_mysql['fecha'].apply(lambda x: parse_date(x) if not pd.isna(x) else None)
        logging.info("Columna 'fecha' formateada correctamente")
    
    # Verificar la columna marca_temporal para asegurarnos de que está correctamente formateada
    if 'marca_temporal' in df_mysql.columns:
        # Definir función de parseo de timestamp
        def parse_timestamp(ts_str):
            if pd.isna(ts_str) or ts_str is None:
                return None
                
            ts_str = str(ts_str).strip()
            
            # Formatos específicos para timestamps
            timestamp_formats = [
                '%d/%m/%Y %H:%M:%S',  # 29/5/2023 13:05:47
                '%d/%m/%Y %H:%M',     # 29/5/2023 13:05
                '%m/%d/%Y %H:%M:%S',  # 5/29/2023 13:05:47
                '%m/%d/%Y %H:%M',     # 5/29/2023 13:05
                '%Y-%m-%d %H:%M:%S',  # 2023-05-29 13:05:47
                '%Y-%m-%d %H:%M',     # 2023-05-29 13:05
            ]
            
            for fmt in timestamp_formats:
                try:
                    parsed_date = datetime.strptime(ts_str, fmt)
                    # Formato para MySQL
                    return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            
            # Si no funciona ningún formato timestamp, intentar como fecha simple y agregar hora 00:00:00
            for fmt in ['%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y']:
                try:
                    parsed_date = datetime.strptime(ts_str, fmt)
                    # Formato para MySQL con hora 00:00:00
                    return parsed_date.strftime('%Y-%m-%d 00:00:00')
                except ValueError:
                    continue
            
            logging.warning(f"No se pudo parsear la marca temporal: {ts_str}")
            return None
        
        df_mysql['marca_temporal'] = df_mysql['marca_temporal'].apply(parse_timestamp)
        logging.info("Columna 'marca_temporal' formateada correctamente")
        
    # Mostrar muestra de los datos procesados
    logging.info("Muestra de datos procesados:")
    sample_data = df_mysql.head(3).to_string()
    logging.info(sample_data)

    logging.info("Truncando tabla MySQL tb_auditorias_terreno...")
    mysql_cursor.execute("TRUNCATE TABLE tb_auditorias_terreno")
    mysql_conn.commit()
    
    logging.info("Insertando datos en tabla MySQL tb_auditorias_terreno...")
    # Convertir DataFrame a lista de tuplas para inserción
    cols = df_mysql.columns.tolist()
    placeholders = ", ".join(["%s"] * len(cols))
    columns = ", ".join([f"`{col}`" for col in cols])
    
    # Preparar consulta INSERT
    mysql_insert_query = f"INSERT INTO tb_auditorias_terreno ({columns}) VALUES ({placeholders})"
    
    # Insertar por lotes para mejorar rendimiento
    batch_size = 1000
    total_records = len(df_mysql)
    
    try:
        for i in range(0, total_records, batch_size):
            batch_end = min(i + batch_size, total_records)
            # Convertir los valores a lista de tuplas, reemplazando NaN por None
            batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df_mysql.iloc[i:batch_end].values]
            mysql_cursor.executemany(mysql_insert_query, batch_data)
            mysql_conn.commit()
            logging.info(f"Insertados {batch_end}/{total_records} registros en MySQL...")
    except Exception as e:
        logging.error(f"Error al insertar en MySQL: {str(e)}")
        # Mostrar una muestra de los datos para diagnóstico
        logging.error("Muestra de datos que causaron el error:")
        sample = df_mysql.iloc[i:i+5] if i < len(df_mysql) else df_mysql.iloc[-5:]
        logging.error(str(sample))
        logging.error(f"Columnas en df_mysql: {list(df_mysql.columns)}")
        logging.error(f"Columnas en consulta SQL: {columns}")
        raise
    
    mysql_cursor.close()
    mysql_conn.close()
    logging.info("Datos insertados correctamente en MySQL.")

    # Nota: Solo se implementa la conexión a MySQL, no se requiere SQL Server
    logging.info("Proceso completado con éxito en MySQL.")
    
    logging.info("Proceso completado con éxito.")
    
    # Resumen final
    logging.info("=== RESUMEN DEL PROCESO ===")
    logging.info(f"Registros procesados: {filtered_count}")
    logging.info(f"Problemas de calidad: {len(quality_issues)}")
    if quality_issues:
        for issue in quality_issues:
            logging.info(f"  - {issue}")

except requests.exceptions.RequestException as e:
    logging.error(f"Error al obtener datos del CSV: {str(e)}")
    sys.exit(1)
except mysql.connector.Error as e:
    logging.error(f"Error en MySQL: {str(e)}")
    sys.exit(1)
# Se omite la excepción de SQL Server ya que no se usa
except Exception as e:
    logging.error(f"Error inesperado: {str(e)}")
    import traceback
    logging.error(f"Detalle del error: {traceback.format_exc()}")
    sys.exit(1)
finally:
    logging.info("Proceso finalizado.")