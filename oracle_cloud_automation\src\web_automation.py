"""
Web automation module for Oracle Cloud interactions using Selenium.
Handles browser setup, navigation, authentication, and data extraction.
"""

import time
import logging
import os
from typing import Optional, <PERSON>ple
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException

import sys
import os

# Añadir el directorio src al path de Python
src_dir = os.path.dirname(os.path.dirname(__file__))
if src_dir not in sys.path:
    sys.path.append(src_dir)

from src.config import (
    WEBDRIVER_CONFIG, ORACLE_CONFIG, TIMING_CONFIG,
    ELEMENT_SELECTORS, ERROR_MESSAGES
)

logger = logging.getLogger(__name__)

class OracleWebAutomation:
    """Handles Oracle Cloud web automation using Selenium WebDriver."""

    def __init__(self):
        self.driver = None
        self.wait = None
        self.config = WEBDRIVER_CONFIG
        self.oracle_config = ORACLE_CONFIG
        self.timing = TIMING_CONFIG
        self.selectors = ELEMENT_SELECTORS

    def setup_driver(self) -> bool:
        """
        Set up the Edge WebDriver with proper configuration.

        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            logger.info("🚗 Setting up Edge WebDriver...")

            # Check if driver exists
            if not os.path.exists(self.config['driver_path']):
                logger.error(f"❌ {ERROR_MESSAGES['driver_not_found']}: {self.config['driver_path']}")
                return False

            # Configure Edge options
            edge_options = Options()

            # Basic options
            for option in self.config['options']:
                edge_options.add_argument(option)

            # Experimental options
            for key, value in self.config['experimental_options'].items():
                if key == 'excludeSwitches':
                    edge_options.add_experimental_option(key, value)
                elif key == 'useAutomationExtension':
                    edge_options.add_experimental_option(key, value)
                elif key == 'prefs':
                    edge_options.add_experimental_option('prefs', value)

            # Create service and driver
            service = Service(self.config['driver_path'])
            self.driver = webdriver.Edge(service=service, options=edge_options)

            # Set up wait
            self.wait = WebDriverWait(self.driver, self.timing['explicit_wait'])

            # Set timeouts
            self.driver.implicitly_wait(self.timing['implicit_wait'])
            self.driver.set_page_load_timeout(self.timing['page_load_timeout'])

            logger.info("✅ Edge WebDriver setup completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error setting up WebDriver: {str(e)}")
            return False

    def navigate_to_oracle(self) -> bool:
        """
        Navigate to Oracle Cloud login page.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            logger.info("🌐 Navigating to Oracle Cloud...")
            logger.info(f"   📍 URL: {self.oracle_config['url']}")

            self.driver.get(self.oracle_config['url'])

            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            title = self.driver.title
            logger.info(f"✅ Oracle Cloud page loaded. Title: {title}")

            return True

        except Exception as e:
            logger.error(f"❌ {ERROR_MESSAGES['element_not_found']}: {str(e)}")
            return False

    def check_existing_session(self) -> bool:
        """
        Check if there's already an active Oracle Cloud session.

        Returns:
            bool: True if active session detected, False otherwise
        """
        try:
            logger.info("🔍 Checking for existing Oracle Cloud session...")

            # Try to find the elId50 element which indicates active session
            try:
                element = WebDriverWait(self.driver, 10).until(
                    EC.visibility_of_element_located((By.ID, self.selectors['elid50']))
                )
                logger.info("✅ Active session detected (elId50 found)")
                return True
            except TimeoutException:
                pass

            # Try alternative indicators
            try:
                element = WebDriverWait(self.driver, 5).until(
                    EC.visibility_of_element_located((By.ID, self.selectors['elid1540']))
                )
                logger.info("⚠️ Alternative session indicator found (elId1540)")
                return False  # Not the main indicator
            except TimeoutException:
                pass

            # Check page title
            if "Oracle Field Service" in self.driver.title:
                logger.info("⚠️ Oracle Field Service title detected but no specific element")
                return False

            # Check for UI elements
            try:
                ui_element = WebDriverWait(self.driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, ".oj-web-applayout-header-title"))
                )
                logger.info("⚠️ UI element detected but not specific session indicator")
                return False
            except TimeoutException:
                pass

            logger.info("❌ No active session detected")
            return False

        except Exception as e:
            logger.error(f"❌ Error checking session: {str(e)}")
            return False

    def perform_sso_login(self) -> bool:
        """
        Perform SSO login process.

        Returns:
            bool: True if login successful, False otherwise
        """
        try:
            logger.info("🔐 Starting SSO login process...")

            # Click SSO button
            if not self._click_element(self.selectors['sso_button'], "SSO button"):
                return False

            # Wait for SSO page
            time.sleep(3)
            self._wait_for_url_change()

            # Enter username
            if not self._enter_username():
                return False

            # Click continue
            if not self._click_continue_button():
                return False

            # Handle Microsoft login
            if not self._handle_microsoft_login():
                return False

            logger.info("✅ SSO login process completed")
            return True

        except Exception as e:
            logger.error(f"❌ SSO login failed: {str(e)}")
            return False

    def _click_element(self, selector: str, element_name: str, by: str = By.ID) -> bool:
        """
        Click an element with error handling.

        Args:
            selector: Element selector
            element_name: Human-readable element name
            by: Selenium By method

        Returns:
            bool: True if click successful, False otherwise
        """
        try:
            logger.info(f"🖱️ Clicking {element_name}...")
            element = self.wait.until(EC.element_to_be_clickable((by, selector)))
            element.click()
            logger.info(f"✅ {element_name} clicked successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to click {element_name}: {str(e)}")
            return False

    def _click_element_with_strategies(self, selector_key: str, element_name: str) -> bool:
        """
        Click an element using multiple selector strategies (matching original working script).

        Args:
            selector_key: Key in ELEMENT_SELECTORS containing list of strategies
            element_name: Human-readable element name

        Returns:
            bool: True if click successful, False otherwise
        """
        try:
            logger.info(f"🖱️ Clicking {element_name} with multiple strategies...")

            # Use the exact strategies from the working original script
            acciones_button = None

            # Estrategia 1: XPath por texto (original)
            try:
                logger.info("   🔍 Trying XPath with text 'Acciones'...")
                acciones_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Acciones')]"))
                )
                logger.info("   ✅ Element found by XPath with text")
            except Exception as e:
                logger.warning(f"   ⚠️ XPath text failed: {str(e)}")

                # Estrategia 2: Buscar por clase o atributos específicos
                try:
                    logger.info("   🔍 Trying CSS selectors...")
                    # Buscar botones que contengan "Acciones" en cualquier atributo
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for btn in all_buttons:
                        try:
                            btn_text = btn.text.strip()
                            if "Acciones" in btn_text or "acciones" in btn_text.lower():
                                if btn.is_displayed() and btn.is_enabled():
                                    acciones_button = btn
                                    logger.info(f"   ✅ Element found by text search: '{btn_text}'")
                                    break
                        except:
                            continue
                except Exception as e:
                    logger.warning(f"   ⚠️ CSS selectors failed: {str(e)}")

                    # Estrategia 3: Buscar por data attributes o aria labels
                    if not acciones_button:
                        try:
                            logger.info("   🔍 Trying data attributes...")
                            acciones_button = WebDriverWait(self.driver, 5).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, "[aria-label*='Acciones'], [data-tooltip*='Acciones'], button[title*='Acciones']"))
                            )
                            logger.info("   ✅ Element found by data attributes")
                        except Exception as e:
                            logger.warning(f"   ⚠️ Data attributes failed: {str(e)}")

            # Verificar si se encontró el elemento
            if not acciones_button:
                logger.error(f"❌ Could not find {element_name} with any strategy")
                return False

            # Hacer clic en el elemento encontrado
            logger.info(f"   🖱️ Clicking {element_name}...")
            try:
                # Scroll al elemento antes de hacer clic
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", acciones_button)
                time.sleep(1)  # Pequeña pausa después del scroll

                acciones_button.click()
                logger.info(f"✅ {element_name} clicked successfully")
                return True
            except Exception as e:
                logger.error(f"❌ Failed to click {element_name}: {str(e)}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to click {element_name} with strategies: {str(e)}")
            return False

    def _wait_for_url_change(self, timeout: int = 10):
        """Wait for URL to change indicating page navigation."""
        try:
            old_url = self.driver.current_url
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.current_url != old_url
            )
            logger.info(f"📄 URL changed: {old_url} → {self.driver.current_url}")
        except Exception as e:
            logger.warning(f"⚠️ URL change not detected: {str(e)}")

    def _enter_username(self) -> bool:
        """Enter username in SSO form."""
        try:
            logger.info("👤 Entering username...")

            # Try different username field selectors
            username_selectors = [
                (By.ID, self.selectors['username_field']),
                (By.CSS_SELECTOR, f"input#{self.selectors['username_field']}"),
                (By.NAME, self.selectors['username_field']),
                (By.XPATH, "//input[@placeholder='Nombre de usuario']"),
                (By.TAG_NAME, "input")
            ]

            username_input = None
            for by, selector in username_selectors:
                try:
                    username_input = WebDriverWait(self.driver, 10).until(
                        EC.visibility_of_element_located((by, selector))
                    )
                    logger.info(f"✅ Username field found using {by} {selector}")
                    break
                except:
                    continue

            if not username_input:
                logger.error("❌ Username field not found")
                return False

            # Clear and enter username
            username_input.clear()
            username_input.send_keys(self.oracle_config['username'])
            logger.info("✅ Username entered")

            return True

        except Exception as e:
            logger.error(f"❌ Error entering username: {str(e)}")
            return False

    def _click_continue_button(self) -> bool:
        """Click the continue button after entering username."""
        try:
            logger.info("▶️ Clicking continue button...")

            # Try different continue button selectors
            continue_selectors = [
                (By.ID, self.selectors['continue_button']),
                (By.ID, "continue-with-sso"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//button[contains(text(), 'Continuar con SSO')]"),
                (By.XPATH, "//button[@type='submit']")
            ]

            continue_button = None
            for by, selector in continue_selectors:
                try:
                    continue_button = WebDriverWait(self.driver, 20).until(
                        EC.element_to_be_clickable((by, selector))
                    )
                    logger.info(f"✅ Continue button found using {by} {selector}")
                    break
                except:
                    continue

            if not continue_button:
                logger.error("❌ Continue button not found")
                return False

            # Try multiple click methods
            try:
                continue_button.click()
            except:
                try:
                    self.driver.execute_script("arguments[0].click();", continue_button)
                except:
                    try:
                        actions = ActionChains(self.driver)
                        actions.move_to_element(continue_button).click().perform()
                    except:
                        # Last resort: send Enter to username field
                        username_input = self.driver.find_element(By.TAG_NAME, "input")
                        username_input.send_keys(Keys.ENTER)

            logger.info("✅ Continue button processed")
            return True

        except Exception as e:
            logger.error(f"❌ Error clicking continue button: {str(e)}")
            return False

    def _handle_microsoft_login(self) -> bool:
        """Handle Microsoft login process."""
        try:
            logger.info("🔷 Processing Microsoft login...")

            # Wait for Microsoft page
            time.sleep(8)
            WebDriverWait(self.driver, 30).until(
                lambda d: "microsoftonline.com" in d.current_url
            )

            # Enter email
            if not self._enter_email():
                return False

            # Enter password
            if not self._enter_password():
                return False

            # Handle stay signed in dialog
            self._handle_stay_signed_in()

            # Wait for Oracle Cloud to load
            self._wait_for_oracle_load()

            logger.info("✅ Microsoft login completed")
            return True

        except Exception as e:
            logger.error(f"❌ Microsoft login failed: {str(e)}")
            return False

    def _enter_email(self) -> bool:
        """Enter email address in Microsoft login form."""
        try:
            logger.info("📧 Entering email address...")

            email_selectors = [
                (By.ID, "i0116"),
                (By.NAME, "loginfmt"),
                (By.XPATH, "//input[@placeholder='Email, teléfono o Skype']")
            ]

            email_input = None
            for by, selector in email_selectors:
                try:
                    email_input = WebDriverWait(self.driver, 25).until(
                        EC.visibility_of_element_located((by, selector))
                    )
                    break
                except:
                    continue

            if not email_input:
                # Fallback to any visible input
                inputs = self.driver.find_elements(By.TAG_NAME, "input")
                for inp in inputs:
                    if inp.get_attribute("type") in ["email", "text"] and inp.is_displayed():
                        email_input = inp
                        break

            if not email_input:
                logger.error("❌ Email input field not found")
                return False

            email_input.clear()
            email_input.send_keys(self.oracle_config['email'])
            logger.info("✅ Email entered")

            # Click Next
            self._click_next_button()

            return True

        except Exception as e:
            logger.error(f"❌ Error entering email: {str(e)}")
            return False

    def _click_next_button(self):
        """Click the Next button after entering email."""
        try:
            next_selectors = [
                (By.ID, "idSIButton9"),
                (By.XPATH, "//input[@value='Siguiente']"),
                (By.XPATH, "//input[@value='Next']")
            ]

            for by, selector in next_selectors:
                try:
                    next_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((by, selector))
                    )
                    next_button.click()
                    logger.info("✅ Next button clicked")
                    return
                except:
                    continue

            # Fallback: send Enter to email field
            email_input = self.driver.find_element(By.TAG_NAME, "input")
            email_input.send_keys(Keys.ENTER)
            logger.info("✅ Enter sent to email field")

        except Exception as e:
            logger.warning(f"⚠️ Error clicking Next button: {str(e)}")

    def _enter_password(self) -> bool:
        """Enter password in Microsoft login form."""
        try:
            logger.info("🔒 Entering password...")

            time.sleep(1)  # Reduced from 3 to 1 second

            password_selectors = [
                (By.ID, "i0118"),
                (By.XPATH, "//input[@type='password']"),
                (By.NAME, "passwd")
            ]

            password_input = None
            for by, selector in password_selectors:
                try:
                    password_input = WebDriverWait(self.driver, 20).until(
                        EC.visibility_of_element_located((by, selector))
                    )
                    break
                except:
                    continue

            if not password_input:
                logger.error("❌ Password input field not found")
                return False

            password_input.clear()
            password_input.send_keys(self.oracle_config['password'])
            logger.info("✅ Password entered")

            # Click Sign In
            self._click_signin_button()

            return True

        except Exception as e:
            logger.error(f"❌ Error entering password: {str(e)}")
            return False

    def _click_signin_button(self):
        """Click the Sign In button after entering password."""
        try:
            signin_selectors = [
                (By.XPATH, "//input[@type='submit']"),
                (By.ID, "idSIButton9"),
                (By.XPATH, "//button[contains(text(), 'Sign in')]")
            ]

            for by, selector in signin_selectors:
                try:
                    signin_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((by, selector))
                    )
                    signin_button.click()
                    logger.info("✅ Sign In button clicked")
                    return
                except:
                    continue

            # Fallback: send Enter to password field
            password_input = self.driver.find_element(By.XPATH, "//input[@type='password']")
            password_input.send_keys(Keys.ENTER)
            logger.info("✅ Enter sent to password field")

        except Exception as e:
            logger.warning(f"⚠️ Error clicking Sign In button: {str(e)}")

    def _handle_stay_signed_in(self):
        """Handle the 'Stay signed in?' dialog."""
        try:
            time.sleep(5)
            yes_button = self.driver.find_element(By.XPATH, "//input[@value='Yes']")
            yes_button.click()
            logger.info("✅ Stay signed in dialog handled")
        except:
            logger.info("ℹ️ Stay signed in dialog not found or already handled")

    def _wait_for_oracle_load(self):
        """Wait for Oracle Cloud to fully load."""
        try:
            WebDriverWait(self.driver, 60).until(
                EC.title_contains("Oracle Field Service")
            )
            time.sleep(5)
            logger.info("✅ Oracle Cloud fully loaded")
        except Exception as e:
            logger.warning(f"⚠️ Oracle Cloud load timeout: {str(e)}")

    def interact_with_elid50(self) -> bool:
        """
        Interact with the elId50 element.

        Returns:
            bool: True if interaction successful, False otherwise
        """
        try:
            logger.info("🎯 Interacting with elId50 element...")

            element = self.wait.until(
                EC.visibility_of_element_located((By.ID, self.selectors['elid50']))
            )

            # Try multiple click methods
            try:
                element.click()
                logger.info("✅ elId50 clicked successfully")
            except:
                try:
                    self.driver.execute_script("arguments[0].click();", element)
                    logger.info("✅ elId50 clicked with JavaScript")
                except:
                    logger.error("❌ Failed to click elId50")
                    return False

            return True

        except Exception as e:
            logger.error(f"❌ Error interacting with elId50: {str(e)}")
            return False

    def click_vtr_button(self, is_first_cycle: bool = False) -> bool:
        """
        Click the VTR unified button that includes all zones.
        Optionally handles Vista dropdown configuration on first cycle.

        Args:
            is_first_cycle: If True, configures Vista dropdown before clicking VTR

        Returns:
            bool: True if VTR button click successful, False otherwise
        """
        try:
            logger.info("🎯 Clicking VTR unified button (all zones) - Text search only...")

            # Handle Vista dropdown configuration on first cycle only
            if is_first_cycle:
                logger.info("🔧 First cycle detected - configuring Vista dropdown...")
                if not self._configure_vista_dropdown():
                    logger.warning("⚠️ Vista dropdown configuration failed, continuing with VTR click...")

            vtr_button = None

            # Try each VTR text-based selector in order of priority
            for i, selector in enumerate(self.selectors['vtr_button']):
                try:
                    logger.info(f"   🔍 Trying VTR text selector {i+1}: {selector}")

                    # All selectors are XPath-based for text search
                    vtr_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"   ✅ VTR button found by text selector {i+1}")
                    break

                except Exception as e:
                    logger.warning(f"   ⚠️ VTR text selector {i+1} failed: {str(e)}")
                    continue

            # If no selector worked, try ultra-simple text search as fallback
            if not vtr_button:
                logger.info("   🔍 Trying ultra-simple VTR text search as final fallback...")
                try:
                    # Find any element containing VTR text
                    vtr_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'VTR')]")

                    for elem in vtr_elements:
                        try:
                            text = elem.text.strip()
                            if text == 'VTR' or 'VTR' in text:
                                # Try the element itself first
                                if elem.is_displayed() and elem.is_enabled():
                                    vtr_button = elem
                                    logger.info(f"   ✅ VTR element found directly: '{text}'")
                                    break
                                # Try parent button
                                parent = elem.find_element(By.XPATH, "./parent::button")
                                if parent.is_displayed() and parent.is_enabled():
                                    vtr_button = parent
                                    logger.info(f"   ✅ VTR parent button found: '{text}'")
                                    break
                        except:
                            continue

                except Exception as e:
                    logger.warning(f"   ⚠️ Ultra-simple VTR search failed: {str(e)}")

            # Verify if VTR button was found
            if not vtr_button:
                logger.error("❌ VTR button not found with any text-based strategy")
                return False

            # Click the VTR button
            try:
                # Scroll to element before clicking
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", vtr_button)
                time.sleep(1)

                vtr_button.click()
                logger.info("   ✅ VTR button clicked successfully")
            except:
                try:
                    self.driver.execute_script("arguments[0].click();", vtr_button)
                    logger.info("   ✅ VTR button clicked with JavaScript")
                except:
                    actions = ActionChains(self.driver)
                    actions.move_to_element(vtr_button).pause(1).click().perform()
                    logger.info("   ✅ VTR button clicked with Actions")

            # Wait for page response
            time.sleep(3)
            logger.info("✅ VTR unified button click completed")
            return True

        except Exception as e:
            logger.error(f"❌ Error clicking VTR button: {str(e)}")
            return False

    def _configure_vista_dropdown(self) -> bool:
        """
        Configure Vista dropdown settings on first cycle only.
        Clicks Vista button, ensures "Todos los datos de hijos" is checked, and applies.

        Returns:
            bool: True if configuration successful, False otherwise
        """
        try:
            logger.info("🔧 Configuring Vista dropdown settings...")

            # Step 1: Click Vista button to open dropdown
            vista_button = None
            vista_selectors = [
                "//button[contains(text(), 'Vista')]",
                "//button[contains(@aria-label, 'Vista')]",
                "//*[contains(text(), 'Vista')]/parent::button",
                "//button[contains(., 'Vista')]"
            ]

            for i, selector in enumerate(vista_selectors):
                try:
                    logger.info(f"   🔍 Trying Vista selector {i+1}: {selector}")
                    vista_button = WebDriverWait(self.driver, 8).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"   ✅ Vista button found by selector {i+1}")
                    break
                except Exception as e:
                    logger.warning(f"   ⚠️ Vista selector {i+1} failed: {str(e)}")
                    continue

            if not vista_button:
                logger.error("❌ Vista button not found")
                return False

            # Click Vista button
            try:
                vista_button.click()
                logger.info("   ✅ Vista button clicked successfully")
                # Wait for the dropdown/menu to appear
                try:
                    WebDriverWait(self.driver, 6).until(
                        EC.presence_of_element_located((By.XPATH, "//*[@role='menu'] | //*[contains(@class,'oj-menu')] | //*[contains(@class,'oj-popup')]"))
                    )
                except Exception:
                    logger.warning("   ⚠️ Vista menu container not explicitly detected, continuing...")
                time.sleep(0.5)
            except Exception as e:
                logger.error(f"❌ Failed to click Vista button: {str(e)}")
                return False

            # Step 2: Ensure "Todos los datos de hijos" checkbox is checked
            logger.info("   🔍 Looking for 'Todos los datos de hijos' checkbox...")

            # First, let's find all checkboxes in the dropdown and log them for debugging
            try:
                all_checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
                logger.info(f"   📋 Found {len(all_checkboxes)} total checkboxes in the page")

                for i, cb in enumerate(all_checkboxes):
                    try:
                        # Get the text near this checkbox
                        parent_text = cb.find_element(By.XPATH, "./..").text.strip()
                        logger.info(f"   📋 Checkbox {i+1}: '{parent_text}' - Checked: {cb.is_selected()}")
                    except Exception:
                        logger.info(f"   📋 Checkbox {i+1}: (no text found) - Checked: {cb.is_selected()}")
            except Exception as e:
                logger.warning(f"   ⚠️ Could not enumerate checkboxes: {str(e)}")

            # Now try specific selectors for "Todos los datos de hijos"
            checkbox_selectors = [
                "//input[@type='checkbox'][following-sibling::text()[contains(., 'Todos los datos de hijos')] or following-sibling::*[contains(text(), 'Todos los datos de hijos')]]",
                "//input[@type='checkbox'][..//*[contains(text(), 'Todos los datos de hijos')]]",
                "//*[contains(text(), 'Todos los datos de hijos')]/preceding-sibling::input[@type='checkbox']",
                "//*[contains(text(), 'Todos los datos de hijos')]/../input[@type='checkbox']",
                "//input[@type='checkbox'][ancestor::*[contains(., 'Todos los datos de hijos')]]"
            ]

            checkbox_element = None
            for i, selector in enumerate(checkbox_selectors):
                try:
                    logger.info(f"   🔍 Trying checkbox selector {i+1}: {selector}")
                    checkbox_element = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    logger.info(f"   ✅ Checkbox found by selector {i+1}")
                    break
                except Exception as e:
                    logger.warning(f"   ⚠️ Checkbox selector {i+1} failed: {str(e)}")
                    continue

            # If specific selectors failed, try to find by position (first checkbox in the dropdown)
            if not checkbox_element:
                logger.info("   🔍 Trying to find first checkbox in dropdown...")
                try:
                    # Look for checkboxes within the dropdown/popup area
                    dropdown_checkboxes = self.driver.find_elements(By.XPATH,
                        "//*[contains(@class, 'oj-popup') or contains(@class, 'oj-menu') or @role='menu']//input[@type='checkbox']")

                    if dropdown_checkboxes:
                        checkbox_element = dropdown_checkboxes[0]  # Take the first one
                        logger.info(f"   ✅ Using first checkbox in dropdown (assuming it's 'Todos los datos de hijos')")
                    else:
                        # Fallback: just get the first checkbox on the page that's visible
                        all_visible_checkboxes = [cb for cb in self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
                                                if cb.is_displayed()]
                        if all_visible_checkboxes:
                            checkbox_element = all_visible_checkboxes[0]
                            logger.info(f"   ✅ Using first visible checkbox on page")
                except Exception as e:
                    logger.warning(f"   ⚠️ Fallback checkbox search failed: {str(e)}")

            if checkbox_element:
                # Check current state
                is_checked = checkbox_element.is_selected()
                logger.info(f"   📋 Checkbox current state: {'checked' if is_checked else 'unchecked'}")

                if not is_checked:
                    logger.info("   🖱️ Attempting to check the checkbox...")
                    success = False

                    # Method 1: Direct click
                    try:
                        checkbox_element.click()
                        time.sleep(0.5)
                        if checkbox_element.is_selected():
                            logger.info("   ✅ Checkbox checked successfully with direct click")
                            success = True
                    except Exception as e:
                        logger.warning(f"   ⚠️ Direct click failed: {str(e)}")

                    # Method 2: JavaScript click
                    if not success:
                        try:
                            self.driver.execute_script("arguments[0].click();", checkbox_element)
                            time.sleep(0.5)
                            if checkbox_element.is_selected():
                                logger.info("   ✅ Checkbox checked successfully with JS click")
                                success = True
                        except Exception as e:
                            logger.warning(f"   ⚠️ JS click failed: {str(e)}")

                    # Method 3: Set checked property directly
                    if not success:
                        try:
                            self.driver.execute_script("arguments[0].checked = true; arguments[0].dispatchEvent(new Event('change'));", checkbox_element)
                            time.sleep(0.5)
                            if checkbox_element.is_selected():
                                logger.info("   ✅ Checkbox checked successfully by setting property")
                                success = True
                        except Exception as e:
                            logger.warning(f"   ⚠️ Property setting failed: {str(e)}")

                    # Method 4: Click on label if checkbox has one
                    if not success:
                        try:
                            label = self.driver.find_element(By.XPATH, f"//label[@for='{checkbox_element.get_attribute('id')}']")
                            label.click()
                            time.sleep(0.5)
                            if checkbox_element.is_selected():
                                logger.info("   ✅ Checkbox checked successfully by clicking label")
                                success = True
                        except Exception as e:
                            logger.warning(f"   ⚠️ Label click failed: {str(e)}")

                    if not success:
                        logger.error("   ❌ All checkbox checking methods failed")
                else:
                    logger.info("   ✅ Checkbox already checked")
            else:
                logger.warning("⚠️ Checkbox not found with any method, continuing anyway...")

            # Step 3: Click Aplicar button
            logger.info("   🔍 Looking for 'Aplicar' button...")

            # First, let's find all buttons and log them for debugging
            try:
                all_buttons = self.driver.find_elements(By.XPATH, "//button")
                logger.info(f"   🔘 Found {len(all_buttons)} total buttons in the page")

                for i, btn in enumerate(all_buttons):
                    try:
                        btn_text = btn.text.strip()
                        if btn_text and btn.is_displayed():
                            logger.info(f"   🔘 Button {i+1}: '{btn_text}' - Visible: {btn.is_displayed()}")
                    except Exception:
                        pass
            except Exception as e:
                logger.warning(f"   ⚠️ Could not enumerate buttons: {str(e)}")

            aplicar_selectors = [
                "//button[text()='Aplicar']",
                "//button[contains(text(), 'Aplicar')]",
                "//button[contains(., 'Aplicar')]",
                "//*[@role='button' and contains(text(), 'Aplicar')]",
                "//*[contains(@class, 'oj-button') and contains(., 'Aplicar')]",
                "//input[@type='button' and @value='Aplicar']",
                "//button[contains(@aria-label, 'Aplicar')]"
            ]

            aplicar_button = None
            for i, selector in enumerate(aplicar_selectors):
                try:
                    logger.info(f"   🔍 Trying Aplicar selector {i+1}: {selector}")
                    aplicar_button = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    logger.info(f"   ✅ Aplicar button found by selector {i+1}")
                    break
                except Exception as e:
                    logger.warning(f"   ⚠️ Aplicar selector {i+1} failed: {str(e)}")
                    continue

            # If specific selectors failed, try to find any button that looks like "Apply"
            if not aplicar_button:
                logger.info("   🔍 Trying to find any button with 'Aplicar' text...")
                try:
                    # Look for any clickable element containing "Aplicar"
                    all_aplicar_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Aplicar') and (@role='button' or name()='button' or @type='button')]")

                    for elem in all_aplicar_elements:
                        if elem.is_displayed() and elem.is_enabled():
                            aplicar_button = elem
                            logger.info(f"   ✅ Found Aplicar element: {elem.tag_name}")
                            break
                except Exception as e:
                    logger.warning(f"   ⚠️ Fallback Aplicar search failed: {str(e)}")

            # Try alternative approach: Find Aplicar button by position (last button in the list)
            if not aplicar_button:
                logger.info("   🔍 Trying to find Aplicar button by position (last visible button)...")
                try:
                    # Get all visible buttons and find the one with "Aplicar" text
                    all_buttons = self.driver.find_elements(By.XPATH, "//button")
                    for btn in reversed(all_buttons):  # Start from the end
                        try:
                            if btn.is_displayed() and btn.is_enabled():
                                btn_text = btn.text.strip()
                                if "Aplicar" in btn_text:
                                    aplicar_button = btn
                                    logger.info(f"   ✅ Found Aplicar button by position: '{btn_text}'")
                                    break
                        except Exception:
                            continue
                except Exception as e:
                    logger.warning(f"   ⚠️ Position-based search failed: {str(e)}")

            if aplicar_button:
                # Click Aplicar button with multiple strategies
                logger.info("   🖱️ Attempting to click Aplicar button...")
                success = False

                # Method 1: Scroll to element and direct click
                try:
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", aplicar_button)
                    time.sleep(0.5)
                    aplicar_button.click()
                    logger.info("   ✅ Aplicar button clicked successfully")
                    success = True
                    time.sleep(1.5)  # Wait for dropdown to close and settings to apply
                except Exception as e:
                    logger.warning(f"   ⚠️ Direct click on Aplicar failed: {str(e)}")

                # Method 2: JavaScript click
                if not success:
                    try:
                        self.driver.execute_script("arguments[0].click();", aplicar_button)
                        logger.info("   ✅ Aplicar button clicked successfully with JS")
                        success = True
                        time.sleep(1.5)
                    except Exception as e:
                        logger.warning(f"   ⚠️ JS click on Aplicar failed: {str(e)}")

                # Method 3: ActionChains click
                if not success:
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(self.driver)
                        actions.move_to_element(aplicar_button).pause(0.5).click().perform()
                        logger.info("   ✅ Aplicar button clicked successfully with ActionChains")
                        success = True
                        time.sleep(1.5)
                    except Exception as e:
                        logger.warning(f"   ⚠️ ActionChains click on Aplicar failed: {str(e)}")

                # Method 4: Try clicking coordinates
                if not success:
                    try:
                        location = aplicar_button.location
                        size = aplicar_button.size
                        x = location['x'] + size['width'] // 2
                        y = location['y'] + size['height'] // 2

                        actions = ActionChains(self.driver)
                        actions.move_by_offset(x, y).click().perform()
                        logger.info("   ✅ Aplicar button clicked successfully with coordinates")
                        success = True
                        time.sleep(1.5)
                    except Exception as e:
                        logger.warning(f"   ⚠️ Coordinate click on Aplicar failed: {str(e)}")

                # Method 5: Try Enter key on the button
                if not success:
                    try:
                        aplicar_button.send_keys(Keys.ENTER)
                        logger.info("   ✅ Aplicar button activated successfully with ENTER key")
                        success = True
                        time.sleep(1.5)
                    except Exception as e:
                        logger.warning(f"   ⚠️ ENTER key on Aplicar failed: {str(e)}")

                if not success:
                    logger.warning("   ⚠️ All Aplicar button click methods failed, closing with ESC")
                    try:
                        self.driver.switch_to.active_element.send_keys(Keys.ESCAPE)
                        time.sleep(1)
                    except Exception:
                        logger.warning("   ⚠️ ESC fallback also failed")
            else:
                logger.warning("⚠️ Aplicar button not found - attempting to close menu with ESC and proceed")
                try:
                    # Try closing the menu with ESC
                    try:
                        self.driver.switch_to.active_element.send_keys(Keys.ESCAPE)
                    except Exception:
                        self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                    time.sleep(1)
                except Exception:
                    logger.warning("   ⚠️ Could not close menu via ESC. Proceeding anyway.")

            logger.info("✅ Vista dropdown configuration completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error configuring Vista dropdown: {str(e)}")
            return False

    def perform_export_actions(self) -> bool:
        """
        Perform the export actions (Acciones → Exportar).

        Returns:
            bool: True if export actions successful, False otherwise
        """
        try:
            logger.info("📊 Performing export actions...")

            # Click Acciones button with multiple strategies
            if not self._click_element_with_strategies('acciones_button', "Acciones button"):
                return False

            time.sleep(3)

            # Click Exportar button with multiple strategies
            if not self._click_exportar_button():
                return False

            logger.info("✅ Export actions completed")
            return True

        except Exception as e:
            logger.error(f"❌ Export actions failed: {str(e)}")
            return False

    def _click_exportar_button(self) -> bool:
        """Click the Exportar button with multiple strategies (matching original working script)."""
        try:
            logger.info("📤 Clicking Exportar button with multiple strategies...")

            # Use the exact strategies from the working original script
            exportar_button = None

            # Estrategia 0: Búsqueda rápida inmediata (nuevo)
            try:
                logger.info("   🔍 Trying immediate fast search...")
                # Búsqueda muy rápida justo después de que aparezca el modal
                exportar_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                )
                logger.info("   ✅ Exportar button found immediately")
            except Exception as e:
                logger.warning(f"   ⚠️ Immediate search failed: {str(e)}")

                # Estrategia 1: XPath por texto (original)
                try:
                    logger.info("   🔍 Trying XPath with text 'Exportar'...")
                    exportar_button = WebDriverWait(self.driver, 8).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                    )
                    logger.info("   ✅ Exportar button found by XPath with text")
                except Exception as e:
                    logger.warning(f"   ⚠️ XPath text failed: {str(e)}")

                # Estrategia 2: Buscar por texto en todos los botones dentro del modal (optimized)
                try:
                    logger.info("   🔍 Trying modal button search...")
                    # Buscar específicamente en elementos que podrían estar dentro de un modal/dialog
                    modal_selectors = [
                        ".modal button", ".dialog button", ".popup button",
                        "[role='dialog'] button", "[role='menu'] button",
                        ".dropdown-menu button", ".menu button"
                    ]

                    for selector in modal_selectors:
                        try:
                            # Use shorter timeout for each selector
                            modal_buttons = WebDriverWait(self.driver, 2).until(
                                lambda d: d.find_elements(By.CSS_SELECTOR, selector)
                            )
                            for btn in modal_buttons:
                                try:
                                    btn_text = btn.text.strip()
                                    if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                        if btn.is_displayed() and btn.is_enabled():
                                            exportar_button = btn
                                            logger.info(f"   ✅ Exportar button found in modal: '{btn_text}'")
                                            break
                                except:
                                    continue
                            if exportar_button:
                                break
                        except:
                            continue
                except Exception as e:
                    logger.warning(f"   ⚠️ Modal search failed: {str(e)}")

                    # Estrategia 3: Búsqueda general en todos los botones visibles (optimized)
                    if not exportar_button:
                        try:
                            logger.info("   🔍 Trying global button search...")
                            # Use WebDriverWait with shorter timeout for faster search
                            all_buttons = WebDriverWait(self.driver, 3).until(
                                lambda d: d.find_elements(By.TAG_NAME, "button")
                            )
                            for btn in all_buttons:
                                try:
                                    if btn.is_displayed() and btn.is_enabled():
                                        btn_text = btn.text.strip()
                                        if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                            exportar_button = btn
                                            logger.info(f"   ✅ Exportar button found globally: '{btn_text}'")
                                            break
                                except:
                                    continue
                        except Exception as e:
                            logger.warning(f"   ⚠️ Global search failed: {str(e)}")

            # Verificar si se encontró el elemento
            if not exportar_button:
                # Último intento: esperar menos tiempo y buscar de nuevo
                logger.info("   🔍 Last attempt: quick search...")
                time.sleep(2)
                try:
                    exportar_button = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                    )
                    logger.info("   ✅ Exportar button found in last attempt")
                except Exception:
                    logger.error("❌ Exportar button not found with any strategy")
                    return False

            # Scroll and click
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", exportar_button)
            time.sleep(1)

            try:
                exportar_button.click()
            except:
                try:
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'}); arguments[0].click();", exportar_button)
                except:
                    actions = ActionChains(self.driver)
                    actions.move_to_element(exportar_button).pause(1).click().perform()

            logger.info("✅ Exportar button clicked")
            return True

        except Exception as e:
            logger.error(f"❌ Error clicking Exportar button: {str(e)}")
            return False

    def navigate_to_metropolitana(self) -> bool:
        """
        Navigate to Metropolitana area using optimized selectors.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            logger.info("🏙️ Navigating to Metropolitana area...")

            # Use the updated selectors from config
            area_metropolitana = None

            # Try each selector in order of priority
            for i, selector in enumerate(self.selectors['metropolitana_area']):
                try:
                    logger.info(f"   🔍 Trying selector {i+1}: {selector}")

                    if selector.startswith("#"):
                        # CSS ID selector
                        element_id = selector[1:]  # Remove the # prefix
                        area_metropolitana = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.ID, element_id))
                        )
                        logger.info(f"   ✅ Element found by ID '{element_id}'")
                        break
                    elif selector.startswith("//"):
                        # XPath selector
                        area_metropolitana = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        logger.info("   ✅ Element found by XPath")
                        break
                    elif selector.startswith(".") or "[" in selector:
                        # CSS selector
                        area_metropolitana = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        logger.info("   ✅ Element found by CSS selector")
                        break
                    else:
                        # Text-based selector
                        if "text()" in selector:
                            area_metropolitana = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                            logger.info("   ✅ Element found by text selector")
                            break

                except Exception as e:
                    logger.warning(f"   ⚠️ Selector {i+1} failed: {str(e)}")
                    continue

            # If no selector worked, try general search as fallback
            if not area_metropolitana:
                logger.info("   🔍 Trying general clickable search as fallback...")
                try:
                    all_clickable = self.driver.find_elements(By.XPATH, "//button | //a | //div[@role='button'] | //span[@role='button']")

                    for elem in all_clickable:
                        try:
                            text = elem.text.strip()
                            if 'metropolitana' in text.lower() or 'metropolitana' in elem.get_attribute('title').lower() if elem.get_attribute('title') else False:
                                if elem.is_displayed() and elem.is_enabled():
                                    area_metropolitana = elem
                                    logger.info(f"   ✅ Element found in general search: '{text}'")
                                    break
                        except:
                            continue
                except Exception as e:
                    logger.warning(f"   ⚠️ General search failed: {str(e)}")

            # Verificar si se encontró el elemento
            if not area_metropolitana:
                logger.error("❌ Metropolitana area element not found with any strategy")
                return False

            # Click the element
            try:
                area_metropolitana.click()
                logger.info("   ✅ Click successful")
            except:
                try:
                    self.driver.execute_script("arguments[0].click();", area_metropolitana)
                    logger.info("   ✅ Click successful with JavaScript")
                except:
                    actions = ActionChains(self.driver)
                    actions.move_to_element(area_metropolitana).pause(1).click().perform()
                    logger.info("   ✅ Click successful with Actions")

            # Wait for page to load
            time.sleep(10)
            logger.info("✅ Navigation to Metropolitana completed")

            return True

        except Exception as e:
            logger.error(f"❌ Error navigating to Metropolitana: {str(e)}")
            return False

    def navigate_to_centro(self) -> bool:
        """
        Navigate to Centro area using optimized selectors.

        Returns:
            bool: True if navigation successful, False otherwise
        """
        try:
            logger.info("🏢 Navigating to Centro area...")

            # Use the updated selectors from config
            area_centro = None

            # Try each selector in order of priority
            for i, selector in enumerate(self.selectors['centro_area']):
                try:
                    logger.info(f"   🔍 Trying selector {i+1}: {selector}")

                    if selector.startswith("#"):
                        # CSS ID selector
                        element_id = selector[1:]  # Remove the # prefix
                        area_centro = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.ID, element_id))
                        )
                        logger.info(f"   ✅ Element found by ID '{element_id}'")
                        break
                    elif selector.startswith("//"):
                        # XPath selector
                        area_centro = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        logger.info("   ✅ Element found by XPath")
                        break
                    elif selector.startswith(".") or "[" in selector:
                        # CSS selector
                        area_centro = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        logger.info("   ✅ Element found by CSS selector")
                        break
                    else:
                        # Text-based selector
                        if "text()" in selector:
                            area_centro = WebDriverWait(self.driver, 10).until(
                                EC.element_to_be_clickable((By.XPATH, selector))
                            )
                            logger.info("   ✅ Element found by text selector")
                            break

                except Exception as e:
                    logger.warning(f"   ⚠️ Selector {i+1} failed: {str(e)}")
                    continue

            # If no selector worked, try general search as fallback
            if not area_centro:
                logger.info("   🔍 Trying general clickable search as fallback...")
                try:
                    all_clickable = self.driver.find_elements(By.XPATH, "//button | //a | //div[@role='button'] | //span[@role='button']")

                    for elem in all_clickable:
                        try:
                            text = elem.text.strip()
                            if 'centro' in text.lower() or 'centro' in elem.get_attribute('title').lower() if elem.get_attribute('title') else False:
                                if elem.is_displayed() and elem.is_enabled():
                                    area_centro = elem
                                    logger.info(f"   ✅ Element found in general search: '{text}'")
                                    break
                        except:
                            continue
                except Exception as e:
                    logger.warning(f"   ⚠️ General search failed: {str(e)}")

            # Verificar si se encontró el elemento
            if not area_centro:
                logger.error("❌ Centro area element not found with any strategy")
                return False

            # Click the element
            try:
                area_centro.click()
                logger.info("   ✅ Click successful")
            except:
                try:
                    self.driver.execute_script("arguments[0].click();", area_centro)
                    logger.info("   ✅ Click successful with JavaScript")
                except:
                    actions = ActionChains(self.driver)
                    actions.move_to_element(area_centro).pause(1).click().perform()
                    logger.info("   ✅ Click successful with Actions")

            # Wait for page to load
            time.sleep(10)
            logger.info("✅ Navigation to Centro completed")

            return True

        except Exception as e:
            logger.error(f"❌ Error navigating to Centro: {str(e)}")
            return False

    def take_screenshot(self, filename: str) -> bool:
        """
        Take a screenshot and save it.

        Args:
            filename: Screenshot filename

        Returns:
            bool: True if screenshot saved successfully, False otherwise
        """
        try:
            self.driver.save_screenshot(filename)
            logger.info(f"📸 Screenshot saved: {filename}")
            return True
        except Exception as e:
            logger.error(f"❌ Screenshot failed: {str(e)}")
            return False

    def debug_page_elements(self, search_text: str = None) -> None:
        """
        Debug method to find and log page elements for troubleshooting.

        Args:
            search_text: Optional text to search for in elements
        """
        try:
            logger.info("🔍 Debugging page elements...")

            # Get all buttons
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"📊 Found {len(buttons)} buttons on page")

            for i, btn in enumerate(buttons[:20]):  # Show first 20 buttons
                try:
                    text = btn.text.strip()
                    if search_text and search_text.lower() in text.lower():
                        logger.info(f"   🎯 Button {i+1}: '{text}' (ID: {btn.get_attribute('id')}, Class: {btn.get_attribute('class')})")
                    elif not search_text:
                        logger.info(f"   🔘 Button {i+1}: '{text}' (ID: {btn.get_attribute('id')})")
                except:
                    continue

            # Get all links
            links = self.driver.find_elements(By.TAG_NAME, "a")
            logger.info(f"📊 Found {len(links)} links on page")

            for i, link in enumerate(links[:10]):  # Show first 10 links
                try:
                    text = link.text.strip()
                    if search_text and search_text.lower() in text.lower():
                        logger.info(f"   🔗 Link {i+1}: '{text}' (href: {link.get_attribute('href')})")
                except:
                    continue

            # Get all divs with click handlers
            clickable_divs = self.driver.find_elements(By.XPATH, "//div[@onclick or @role='button']")
            logger.info(f"📊 Found {len(clickable_divs)} clickable divs")

            for i, div in enumerate(clickable_divs[:10]):
                try:
                    text = div.text.strip()
                    if search_text and search_text.lower() in text.lower():
                        logger.info(f"   📦 Div {i+1}: '{text}' (ID: {div.get_attribute('id')})")
                except:
                    continue

            logger.info("✅ Page element debugging completed")

        except Exception as e:
            logger.error(f"❌ Error debugging page elements: {str(e)}")

    def close(self):
        """Close the browser gracefully with cleanup."""
        try:
            if self.driver:
                logger.info("🧹 Starting graceful browser shutdown...")

                # Step 1: Navigate to blank page to clear current session
                try:
                    logger.info("   📄 Navigating to blank page...")
                    self.driver.get("about:blank")
                    time.sleep(2)  # Allow page to load
                except Exception as e:
                    logger.warning(f"   ⚠️ Could not navigate to blank page: {str(e)}")

                # Step 2: Clear browser data
                try:
                    logger.info("   🗑️ Clearing browser data...")
                    # Clear cookies
                    self.driver.delete_all_cookies()
                    logger.info("   ✅ Cookies cleared")

                    # Clear local storage and session storage
                    self.driver.execute_script("window.localStorage.clear();")
                    self.driver.execute_script("window.sessionStorage.clear();")
                    logger.info("   ✅ Storage cleared")

                except Exception as e:
                    logger.warning(f"   ⚠️ Could not clear browser data: {str(e)}")

                # Step 3: Close current window first (graceful)
                try:
                    logger.info("   🚪 Closing current window...")
                    self.driver.close()
                    time.sleep(1)  # Brief pause
                    logger.info("   ✅ Window closed")
                except Exception as e:
                    logger.warning(f"   ⚠️ Could not close window gracefully: {str(e)}")

                # Step 4: Quit the driver (final cleanup)
                try:
                    logger.info("   🛑 Quitting WebDriver...")
                    self.driver.quit()
                    logger.info("   ✅ WebDriver quit successfully")
                except Exception as e:
                    logger.warning(f"   ⚠️ Could not quit WebDriver: {str(e)}")

                # Step 5: Clear the driver reference
                self.driver = None
                self.wait = None

                # Step 6: Add delay before allowing new instance
                logger.info("   ⏳ Waiting for complete cleanup...")
                time.sleep(3)

                logger.info("✅ Browser shutdown completed gracefully")

        except Exception as e:
            logger.error(f"❌ Error during graceful browser shutdown: {str(e)}")
            # Fallback: try force quit
            try:
                if self.driver:
                    self.driver.quit()
                    self.driver = None
                    self.wait = None
                logger.info("🛑 Browser force-closed as fallback")
            except Exception as fallback_error:
                logger.error(f"❌ Force close also failed: {str(fallback_error)}")

# Convenience functions
def create_oracle_automation() -> OracleWebAutomation:
    """Create and return an OracleWebAutomation instance."""
    return OracleWebAutomation()

def setup_and_navigate() -> Optional[OracleWebAutomation]:
    """
    Set up WebDriver and navigate to Oracle Cloud.

    Returns:
        OracleWebAutomation instance if successful, None otherwise
    """
    automation = create_oracle_automation()

    if not automation.setup_driver():
        return None

    if not automation.navigate_to_oracle():
        automation.close()
        return None

    return automation