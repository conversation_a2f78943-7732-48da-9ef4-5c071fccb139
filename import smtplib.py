import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from typing import Union, List, Optional, Dict

def enviar_correo(
    destinatario: Union[str, List[str]],
    asunto: str,
    cuerpo: str,
    remitente: str = '<EMAIL>',
    cc: Optional[Union[str, List[str]]] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    archivos_adjuntos: Optional[List[str]] = None,
    html: bool = False,
    smtp_config: Optional[Dict] = None
) -> Dict:
    print(f"Iniciando envío de correo a: {destinatario}")
    
    config = {
        'server': 'smtp.example.com',
        'port': 587,
        'username': '<EMAIL>',
        'password': 'password',
        'use_ssl': False,
        'use_tls': True
    }
    
    if smtp_config:
        config.update(smtp_config)
    
    try:
        print(f"Conectando al servidor SMTP: {config['server']}:{config['port']}")
        if config['use_ssl']:
            server = smtplib.SMTP_SSL(config['server'], config['port'])
            print("Usando conexión SSL")
        else:
            server = smtplib.SMTP(config['server'], config['port'])
            if config['use_tls']:
                print("Iniciando TLS")
                server.starttls()
        
        print("Iniciando sesión SMTP...")
        server.login(config['username'], config['password'])
        print("Sesión SMTP iniciada exitosamente")
        
        mensaje = MIMEMultipart()
        mensaje['From'] = remitente
        mensaje['To'] = destinatario if isinstance(destinatario, str) else ', '.join(destinatario)
        mensaje['Subject'] = asunto
        
        if cc:
            mensaje['Cc'] = cc if isinstance(cc, str) else ', '.join(cc)
        
        if bcc:
            mensaje['Bcc'] = bcc if isinstance(bcc, str) else ', '.join(bcc)
        
        if html:
            mensaje.attach(MIMEText(cuerpo, 'html'))
        else:
            mensaje.attach(MIMEText(cuerpo, 'plain'))
        
        if archivos_adjuntos:
            for archivo in archivos_adjuntos:
                adjunto = MIMEBase('application', 'octet-stream')
                with open(archivo, 'rb') as f:
                    adjunto.set_payload(f.read())
                encoders.encode_base64(adjunto)
                adjunto.add_header('Content-Disposition', f'attachment; filename={archivo}')
                mensaje.attach(adjunto)
        
        todos_destinatarios = [destinatario] if isinstance(destinatario, str) else destinatario
        if cc:
            todos_destinatarios += [cc] if isinstance(cc, str) else cc
        if bcc:
            todos_destinatarios += [bcc] if isinstance(bcc, str) else bcc
        
        print(f"Enviando correo a {len(todos_destinatarios)} destinatarios...")
        server.send_message(mensaje, from_addr=remitente, to_addrs=todos_destinatarios)
        server.quit()
        print("Servidor SMTP cerrado")
        
        return {"exito": True, "mensaje": f"Correo enviado con éxito a {mensaje['To']}"}
    
    except Exception as e:
        error_msg = f"Error al enviar correo: {str(e)}"
        print(error_msg)
        return {"exito": False, "mensaje": error_msg}
