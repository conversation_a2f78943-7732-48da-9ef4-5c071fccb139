#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script simple para sincronizar datos desde SQL Server a MySQL
Usando pandas como intermediario para simplificar el proceso.
"""

import pandas as pd
import mysql.connector
import pyodbc
import numpy as np
from datetime import datetime
import logging
import sys

# Variable global para determinar la tabla a migrar
TABLA_ORIGEN = "tb_bonos_produccion_sup"  # Tabla/vista en SQL Server
TABLA_DESTINO = "tb_bonos_produccion_sup"  # Tabla en MySQL

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("sync_vtr_resumen.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

def sincronizar_vtr_resumen(limit=1000):
    """Sincroniza la vista definida en TABLA_ORIGEN desde SQL Server a MySQL"""
    
    start_time = datetime.now()
    logging.info(f"Iniciando sincronización de {TABLA_ORIGEN} a {TABLA_DESTINO} (límite: {limit} registros)")
    print(f"Iniciando sincronización... ({datetime.now().strftime('%H:%M:%S')})")
    
    # Configuración SQL Server
    sql_server_config = {
        'driver': 'ODBC Driver 17 for SQL Server',
        'server': '************',
        'database': 'telqway',
        'uid': 'ncornejo',
        'pwd': 'N1c0l7as17'
    }
    
    # Configuración MySQL
    mysql_config = {
        'host': '**************',
        'user': 'ncornejo',
        'password': 'N1c0l7as17',
        'database': 'operaciones_tqw',
        'port': 3306
    }
    
    try:
        # 1. Conexión a SQL Server y extracción de datos
        logging.info("Conectando a SQL Server...")
        print("Conectando a SQL Server...")
        
        conn_str = f"DRIVER={{{sql_server_config['driver']}}};SERVER={sql_server_config['server']};DATABASE={sql_server_config['database']};UID={sql_server_config['uid']};PWD={sql_server_config['pwd']}"
        sql_conn = pyodbc.connect(conn_str)
        
        # Consulta con límite para pruebas
        query = f"SELECT TOP {limit} * FROM {TABLA_ORIGEN}"
        logging.info(f"Ejecutando consulta: {query}")
        print(f"Ejecutando consulta en SQL Server...")
        
        # Usar pandas para leer directamente a un DataFrame
        df = pd.read_sql(query, sql_conn)
        
        # Cerrar conexión SQL Server
        sql_conn.close()
        
        # Información del DataFrame
        row_count = len(df)
        logging.info(f"Datos obtenidos: {row_count} filas")
        print(f"Datos obtenidos: {row_count} filas")
        
        if row_count == 0:
            logging.warning("No se encontraron datos en SQL Server")
            print("No se encontraron datos en SQL Server")
            return
        
        # Mostrar información para diagnóstico
        print(f"\nColumnas obtenidas: {df.columns.tolist()}")
        print(f"\nTipos de datos: {df.dtypes.to_dict()}")
        print(f"\nPrimeras 5 filas:")
        print(df.head(5))
        
        # 2. Reemplazar NaN por None para compatibilidad con MySQL
        print("\nPreparando datos para MySQL...")
        df_clean = df.replace({np.nan: None})
        
        # 3. Conexión a MySQL
        logging.info("Conectando a MySQL...")
        print("Conectando a MySQL...")
        mysql_conn = mysql.connector.connect(**mysql_config)
        mysql_cursor = mysql_conn.cursor()
        
        # 4. Verificar si la tabla existe en MySQL
        table_name = TABLA_DESTINO
        mysql_cursor.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = '{table_name}'
        """)
        table_exists = mysql_cursor.fetchone()[0] > 0
        
        if table_exists:
            # Si la tabla existe, truncarla
            logging.info(f"La tabla {table_name} existe, truncando...")
            print(f"La tabla {table_name} existe, truncando...")
            mysql_cursor.execute(f"TRUNCATE TABLE {table_name}")
            mysql_conn.commit()
        else:
            # Si la tabla no existe, crearla automáticamente
            logging.info(f"La tabla {table_name} no existe, creándola...")
            print(f"La tabla {table_name} no existe, creándola...")
            
            # Mapear tipos de datos de pandas a MySQL
            type_mapping = {
                'int64': 'BIGINT',
                'float64': 'DOUBLE',
                'object': 'VARCHAR(255)',
                'bool': 'BOOLEAN',
                'datetime64[ns]': 'DATETIME'
            }
            
            # Crear SQL para la tabla
            columns = []
            for col, dtype in df.dtypes.items():
                mysql_type = type_mapping.get(str(dtype), 'VARCHAR(255)')
                columns.append(f"`{col}` {mysql_type}")
            
            create_table_sql = f"CREATE TABLE {table_name} (\n"
            create_table_sql += ",\n".join(columns)
            create_table_sql += "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
            
            print(f"SQL para crear tabla:\n{create_table_sql}")
            
            # Ejecutar creación de tabla
            mysql_cursor.execute(create_table_sql)
            mysql_conn.commit()
            logging.info(f"Tabla {table_name} creada exitosamente")
            print(f"Tabla {table_name} creada exitosamente")
        
        # 5. Insertar datos en MySQL
        logging.info(f"Insertando {row_count} filas en MySQL...")
        print(f"Insertando {row_count} filas en MySQL...")
        
        # Preparar la sentencia INSERT
        columns = df.columns.tolist()
        placeholders = ", ".join(["%s"] * len(columns))
        columns_str = ", ".join([f"`{col}`" for col in columns])
        
        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # Convertir DataFrame a lista de tuplas para inserción
        values = [tuple(x) for x in df_clean.to_numpy()]
        
        # Insertar por lotes para mejor rendimiento
        batch_size = 100
        total_records = len(values)
        
        for i in range(0, total_records, batch_size):
            batch_end = min(i + batch_size, total_records)
            batch = values[i:batch_end]
            
            try:
                mysql_cursor.executemany(insert_query, batch)
                mysql_conn.commit()
                print(f"Progreso: {batch_end}/{total_records} registros")
            except Exception as e:
                logging.error(f"Error insertando lote {i//batch_size + 1}: {str(e)}")
                print(f"ERROR insertando lote {i//batch_size + 1}: {str(e)}")
                
                # Mostrar datos de muestra que causaron el error
                error_sample = df.iloc[i:i+5] if i+5 < len(df) else df.iloc[i:]
                print(f"Muestra de datos con error:\n{error_sample}")
                
                # No levantar la excepción, continuar con el siguiente lote
                continue
        
        # Cerrar conexiones
        mysql_cursor.close()
        mysql_conn.close()
        
        # Reporte final
        duration = datetime.now() - start_time
        logging.info(f"Sincronización completada. Tiempo total: {duration}")
        print(f"\nSincronización completada con éxito!")
        print(f"Tiempo total: {duration}")
        print(f"Registros sincronizados: {total_records}")
        
    except Exception as e:
        logging.error(f"Error en sincronización: {str(e)}")
        print(f"ERROR en sincronización: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        # Permitir especificar un límite como argumento
        limit = 1000  # valor predeterminado
        if len(sys.argv) > 1:
            limit = int(sys.argv[1])
        
        sincronizar_vtr_resumen(limit)
    except Exception as e:
        logging.error(f"Error fatal: {str(e)}")
        print(f"ERROR FATAL: {str(e)}")
        import traceback
        traceback.print_exc()