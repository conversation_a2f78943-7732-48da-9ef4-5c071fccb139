from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import pandas as pd
import time


from sqlalchemy import create_engine, Column, Integer, String, Float , text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base



engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)


query = text("""
    SELECT DISTINCT tut.rut
    FROM TB_LOGIS_CIERRE_INVENTARIO2 tlci
    LEFT JOIN tb_user_tqw tut ON tut.Nombre_short = tlci.id_tecnico
    WHERE ID_CIERRE = '661705b354'
        AND tut.rut IS NOT NULL
        AND id_tecnico NOT IN ('INGRESO MANUAL', '<PERSON><PERSON><PERSON> ', 'ANALISIS', '<PERSON><PERSON><PERSON>')
""")

with engineMYsql.connect() as connection:
    result = connection.execute(query)
    ruts = [row[0] for row in result]


driver = webdriver.Edge(executable_path="C:/Users/<USER>/Desktop/Bot Python/edgedriver_win64/msedgedriver.exe")

# driver.get("https://operaciones.telqway.cl/APP_TQW/dist/uploadCSV_PDF.php?rut=22013209-9")

for rut in ruts:
    url = f"https://operaciones.telqway.cl/APP_TQW/dist/uploadCSV_PDF.php?rut={rut}"
    driver.get(url)
    # Realiza las acciones necesarias en la página web para cada RUT


time.sleep(4)