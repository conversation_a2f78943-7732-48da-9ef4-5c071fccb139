import os
import json
import sys
import subprocess
import time
import shutil
from datetime import datetime

# Ruta del script del scheduler
SCHEDULER_PATH = r"C:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py"
CONFIG_PATH = os.path.join(os.path.dirname(SCHEDULER_PATH), "scheduler_config.json")
BACKUP_PATH = os.path.join(os.path.dirname(SCHEDULER_PATH), f"scheduler_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

def kill_scheduler_processes():
    """Mata los procesos del scheduler"""
    try:
        # Usar taskkill para terminar todos los procesos python que estén ejecutando el scheduler
        os.system('taskkill /f /im python.exe /fi "WINDOWTITLE eq ETL TQW Scheduler*"')
        print("Procesos del scheduler terminados exitosamente.")
    except Exception as e:
        print(f"Error al terminar procesos: {str(e)}")

def backup_config():
    """Crea una copia de seguridad del archivo de configuración"""
    if os.path.exists(CONFIG_PATH):
        try:
            shutil.copy2(CONFIG_PATH, BACKUP_PATH)
            print(f"Copia de seguridad creada en: {BACKUP_PATH}")
            return True
        except Exception as e:
            print(f"Error al crear copia de seguridad: {str(e)}")
    else:
        print("No se encontró archivo de configuración para respaldar.")
    return False

def fix_config_file():
    """Corrige el archivo de configuración"""
    if os.path.exists(CONFIG_PATH):
        try:
            # Leer la configuración actual
            with open(CONFIG_PATH, 'r') as f:
                config = json.load(f)
            
            # Verificar la estructura de la configuración
            if "tasks" not in config:
                print("Error: El archivo de configuración no tiene la estructura esperada.")
                return False
            
            # Asegurarse de que FlujoMysql_i esté en la sección correcta con intervalo 3
            flujo_mysql_task = None
            
            # Buscar la tarea en la sección de interval
            if "interval" in config["tasks"]:
                if "FlujoMysql_i" in config["tasks"]["interval"]:
                    flujo_mysql_task = config["tasks"]["interval"]["FlujoMysql_i"]
                    # Asegurarse de que el intervalo sea 3
                    flujo_mysql_task["interval"] = 3
                    # Asegurarse de que la ruta sea correcta
                    flujo_mysql_task["path"] = r"C:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py"
                    print("Tarea FlujoMysql_i actualizada con intervalo de 3 minutos.")
            
            # Si no se encontró, crearla
            if flujo_mysql_task is None:
                if "interval" not in config["tasks"]:
                    config["tasks"]["interval"] = {}
                
                config["tasks"]["interval"]["FlujoMysql_i"] = {
                    "name": "FlujoMysql_i",
                    "path": r"C:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py",
                    "max_runtime