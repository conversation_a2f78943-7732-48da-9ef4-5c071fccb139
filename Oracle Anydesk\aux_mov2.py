from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

import psutil
from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np
import sys
from datetime import datetime

import signal

import win32gui
import win32com.client
from aux_mov import DatabaseManager


def ejecutar_sincronizacion():
    db_manager = DatabaseManager()
    try:
        db_manager.process_oracle_data()
    finally:
        db_manager.cleanup()

if __name__ == "__main__":
    ejecutar_sincronizacion()
