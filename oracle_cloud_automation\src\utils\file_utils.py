"""
File utilities module for handling Excel files and downloads.
Provides functions for finding, validating, and managing files.
"""

import os
import glob
import time
import logging
from typing import Optional, List, Tuple
from config import DEFAULT_DOWNLOAD_DIR, DEFAULT_PATTERN, DEFAULT_MAX_AGE_MINUTES

logger = logging.getLogger(__name__)

class FileManager:
    """Manages file operations including Excel file discovery and validation."""

    def __init__(self):
        self.download_dir = DEFAULT_DOWNLOAD_DIR
        self.default_pattern = DEFAULT_PATTERN
        self.default_max_age = DEFAULT_MAX_AGE_MINUTES

    def find_latest_excel_file(
        self,
        download_dir: Optional[str] = None,
        pattern: str = DEFAULT_PATTERN,
        max_age_minutes: int = 3,  # Reduced to 3 minutes for more recent files
        min_timestamp: Optional[float] = None
    ) -> Optional[str]:
        """
        Find the most recent Excel file in the download directory.
        Optimized to find only very recent downloads.

        Args:
            download_dir: Directory to search (uses default if None)
            pattern: File pattern to match (e.g., "*.xlsx")
            max_age_minutes: Maximum age of files to consider (in minutes) - default 3 min
            min_timestamp: Minimum timestamp for files to consider

        Returns:
            Path to the latest valid Excel file, or None if not found
        """
        try:
            if download_dir is None:
                download_dir = self.download_dir

            logger.info(f"🔍 Searching for RECENT Excel files in: {download_dir}")
            logger.info(f"   📋 Pattern: {pattern}")
            logger.info(f"   ⏰ Max age: {max_age_minutes} minutes (optimized for recent downloads)")

            # Find all files matching the pattern
            excel_files = glob.glob(os.path.join(download_dir, pattern))

            if not excel_files:
                logger.info("   📭 No Excel files found")
                return None

            # Filter files by VERY RECENT age (last 3 minutes by default)
            current_time = time.time()
            max_age_seconds = max_age_minutes * 60
            very_recent_files = []

            logger.info(f"   🔍 Reviewing {len(excel_files)} Excel files for very recent ones...")

            for file_path in excel_files:
                try:
                    file_mtime = os.path.getmtime(file_path)
                    file_age_seconds = current_time - file_mtime
                    file_age_minutes = file_age_seconds / 60

                    # Only consider files from the last 3 minutes (very recent)
                    if file_age_seconds <= max_age_seconds:
                        # Additional check: file must be newer than min_timestamp if provided
                        if min_timestamp is None or file_mtime > min_timestamp:
                            very_recent_files.append((file_path, file_age_seconds))
                            logger.info(f"   ✅ Very recent file: {os.path.basename(file_path)} (age: {file_age_minutes:.1f} min)")
                        else:
                            logger.info(f"   📅 File too old for timestamp filter: {os.path.basename(file_path)}")
                    else:
                        logger.debug(f"   ⏰ File too old: {os.path.basename(file_path)} (age: {file_age_minutes:.1f} min)")

                except Exception as e:
                    logger.warning(f"   ⚠️ Error checking file {os.path.basename(file_path)}: {str(e)}")
                    continue

            if not very_recent_files:
                logger.warning(f"   ❌ No very recent Excel files found (last {max_age_minutes} minutes)")
                logger.info("   💡 Suggestions:")
                logger.info("      • Download may still be in progress")
                logger.info("      • File may be saved in different location")
                logger.info("      • Check file permissions")
                logger.info("      • Try increasing max_age_minutes if needed")
                return None

            # Return the MOST RECENT file (the one with smallest age)
            latest_file, file_age_seconds = min(very_recent_files, key=lambda x: x[1])
            file_time = time.ctime(os.path.getmtime(latest_file))
            file_size = os.path.getsize(latest_file)
            file_age_minutes = file_age_seconds / 60

            logger.info(f"   🎯 SELECTED MOST RECENT FILE: {os.path.basename(latest_file)}")
            logger.info(f"   📅 Modified: {file_time}")
            logger.info(f"   📏 Size: {file_size} bytes")
            logger.info(f"   ⏱️  Age: {file_age_minutes:.1f} minutes")
            logger.info(f"   📂 Location: {os.path.dirname(latest_file)}")

            return latest_file

        except Exception as e:
            logger.error(f"❌ Error finding latest Excel file: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def wait_for_download(
        self,
        expected_filename: Optional[str] = None,
        timeout_seconds: int = 180,
        check_interval: int = 10
    ) -> Optional[str]:
        """
        Wait for a file download to complete.

        Args:
            expected_filename: Expected filename (optional)
            timeout_seconds: Maximum time to wait
            check_interval: Time between checks

        Returns:
            Path to downloaded file, or None if timeout
        """
        try:
            logger.info(f"⏳ Waiting for download completion (timeout: {timeout_seconds}s)...")

            start_time = time.time()
            last_file_count = 0

            while time.time() - start_time < timeout_seconds:
                # Get current files in download directory
                current_files = glob.glob(os.path.join(self.download_dir, "*.xlsx"))
                current_count = len(current_files)

                # Check if new file appeared
                if current_count > last_file_count:
                    logger.info(f"   📄 New file detected! ({current_count} total files)")

                    # If expected filename provided, wait for it specifically
                    if expected_filename:
                        expected_path = os.path.join(self.download_dir, expected_filename)
                        if os.path.exists(expected_path):
                            # Wait a bit more to ensure download is complete
                            time.sleep(2)
                            file_size = os.path.getsize(expected_path)
                            logger.info(f"   ✅ Expected file found: {expected_filename} ({file_size} bytes)")
                            return expected_path
                    else:
                        # Return the most recent file
                        latest_file = self.find_latest_excel_file(max_age_minutes=5)
                        if latest_file:
                            return latest_file

                last_file_count = current_count
                time.sleep(check_interval)

            logger.warning(f"   ⏰ Download timeout after {timeout_seconds} seconds")
            return None

        except Exception as e:
            logger.error(f"❌ Error waiting for download: {str(e)}")
            return None

    def validate_excel_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate an Excel file for processing.

        Args:
            file_path: Path to the Excel file

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not os.path.exists(file_path):
                return False, f"File does not exist: {file_path}"

            if not os.path.isfile(file_path):
                return False, f"Path is not a file: {file_path}"

            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "File is empty"

            # Check file extension
            if not file_path.lower().endswith(('.xlsx', '.xls')):
                return False, "File is not an Excel file"

            # Try to read the file
            try:
                import pandas as pd
                df = pd.read_excel(file_path, nrows=1)  # Just read header
                if df.empty:
                    return False, "Excel file has no data"
            except Exception as e:
                return False, f"Cannot read Excel file: {str(e)}"

            logger.info(f"✅ File validation passed: {os.path.basename(file_path)} ({file_size} bytes)")
            return True, ""

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        Get detailed information about a file.

        Args:
            file_path: Path to the file

        Returns:
            Dictionary with file information, or None if error
        """
        try:
            if not os.path.exists(file_path):
                return None

            stat = os.stat(file_path)
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'directory': os.path.dirname(file_path),
                'size': stat.st_size,
                'created': time.ctime(stat.st_ctime),
                'modified': time.ctime(stat.st_mtime),
                'age_seconds': time.time() - stat.st_mtime,
                'age_minutes': (time.time() - stat.st_mtime) / 60
            }

            return file_info

        except Exception as e:
            logger.error(f"Error getting file info: {str(e)}")
            return None

    def cleanup_old_files(self, directory: str, max_age_days: int = 7, pattern: str = "*.xlsx") -> int:
        """
        Clean up old files from a directory.

        Args:
            directory: Directory to clean
            max_age_days: Maximum age in days for files to keep
            pattern: File pattern to match

        Returns:
            Number of files deleted
        """
        try:
            files_deleted = 0
            max_age_seconds = max_age_days * 24 * 60 * 60
            current_time = time.time()

            # Find all matching files
            file_pattern = os.path.join(directory, pattern)
            files = glob.glob(file_pattern)

            for file_path in files:
                try:
                    file_mtime = os.path.getmtime(file_path)
                    file_age = current_time - file_mtime

                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        files_deleted += 1
                        logger.info(f"   🗑️ Deleted old file: {os.path.basename(file_path)}")

                except Exception as e:
                    logger.warning(f"   ⚠️ Error deleting {os.path.basename(file_path)}: {str(e)}")
                    continue

            logger.info(f"✅ Cleanup completed: {files_deleted} files deleted")
            return files_deleted

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {str(e)}")
            return 0

# Convenience functions for backward compatibility
def find_latest_excel_file(
    download_dir: Optional[str] = None,
    pattern: str = DEFAULT_PATTERN,
    max_age_minutes: int = DEFAULT_MAX_AGE_MINUTES,
    min_timestamp: Optional[float] = None
) -> Optional[str]:
    """
    Convenience function to find the latest Excel file.
    """
    manager = FileManager()
    return manager.find_latest_excel_file(download_dir, pattern, max_age_minutes, min_timestamp)

def validate_excel_file(file_path: str) -> Tuple[bool, str]:
    """
    Convenience function to validate an Excel file.
    """
    manager = FileManager()
    return manager.validate_excel_file(file_path)

def wait_for_download(
    expected_filename: Optional[str] = None,
    timeout_seconds: int = 25,  # Reduced to 25 seconds for VTR unified downloads
    check_interval: int = 5     # Reduced check interval for faster detection
) -> Optional[str]:
    """
    Convenience function to wait for download completion.
    """
    manager = FileManager()
    return manager.wait_for_download(expected_filename, timeout_seconds, check_interval)