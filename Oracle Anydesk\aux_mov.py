from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
import pandas as pd
from datetime import datetime
import time
from sqlalchemy.exc import OperationalError, PendingRollbackError

class DatabaseError(Exception):
    """Excepción personalizada para errores de base de datos después de reintentos"""
    pass

class DatabaseManager:
    def __init__(self):
        self.mysql_engine = create_engine(
            'mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw',
            pool_size=10,
            max_overflow=20,
            pool_recycle=60,
            echo=False,
            isolation_level='READ COMMITTED'  # Nivel de aislamiento válido para MySQL
        )
        
        self.mssql_engine = create_engine(
            'mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server',
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=280
        )
        
        self.Session = sessionmaker(bind=self.mssql_engine)

    def read_sql_with_retry(self, query, connection, max_retries=3, wait_time=2):
        """Leer SQL con reintentos"""
        current_connection = connection
        for attempt in range(max_retries):
            try:
                if isinstance(query, str):
                    query = text(query)
                return pd.read_sql_query(query, current_connection), current_connection
            except (OperationalError, PendingRollbackError, Exception) as e:
                error_message = str(e)
                if attempt == max_retries - 1:
                    raise DatabaseError(f"Error después de {max_retries} intentos: {error_message}")
                
                print(f"Reintentando lectura... (intento {attempt + 2} de {max_retries})")
                
                # Si es un error de conexión, intentar reconectar
                if "Connection is closed" in error_message or "MySQL Connection not available" in error_message:
                    try:
                        # Cerrar conexión actual si está abierta
                        try:
                            if current_connection and not current_connection.closed:
                                current_connection.close()
                        except:
                            pass
                            
                        # Crear nueva conexión del mismo tipo
                        if "mysql" in str(connection.engine.url):
                            current_connection = self.mysql_engine.connect()
                            current_connection.execute(text("SET autocommit=1"))
                        else:
                            current_connection = self.mssql_engine.connect()
                        print("Reconexión exitosa para lectura SQL")
                    except Exception as conn_err:
                        print(f"Error al reconectar: {str(conn_err)}")
                
                time.sleep(wait_time)

    def to_sql_with_retry(self, df, table_name, connection, if_exists='replace', max_retries=3, wait_time=120):
        """Ejecutar to_sql con reintentos"""
        current_connection = connection
        
        for attempt in range(max_retries):
            try:
                df.to_sql(table_name, current_connection, if_exists=if_exists, index=False)
                print(f"Datos transferidos exitosamente a {table_name}")
                return current_connection  # Devolver la conexión actual (que podría ser nueva)
            except Exception as e:
                error_message = str(e)
                print(f"Error al transferir datos a {table_name}: {error_message}")
                
                # Si es el último intento, lanzar excepción
                if attempt == max_retries - 1:
                    raise DatabaseError(f"Error después de {max_retries} intentos al transferir a {table_name}: {error_message}")
                
                # Si es un error de conexión MySQL, esperar y reintentar
                if "MySQL Connection not available" in error_message or "This Connection is closed" in error_message or "Connection is closed" in error_message:
                    wait_seconds = wait_time
                    print(f"Conexión no disponible. Esperando {wait_seconds} segundos antes de reintentar...")
                    time.sleep(wait_seconds)
                    
                    # Intentar reconectar
                    try:
                        # Cerrar la conexión actual si está abierta
                        try:
                            if current_connection and not current_connection.closed:
                                current_connection.close()
                        except:
                            pass
                            
                        # Crear nueva conexión del tipo correcto
                        if "mysql" in str(connection.engine.url):
                            current_connection = self.mysql_engine.connect()
                            current_connection.execute(text("SET autocommit=1"))
                        else:
                            current_connection = self.mssql_engine.connect()
                        print("Reconexión exitosa")
                    except Exception as conn_err:
                        print(f"Error al reconectar: {str(conn_err)}")
                else:
                    # Para otros errores, esperar menos tiempo
                    print(f"Reintentando transferencia... (intento {attempt + 2} de {max_retries})")
                    time.sleep(5)

    def execute_with_retry(self, sql_statement, connection, params=None, max_retries=3, wait_time=30):
        """Ejecutar una sentencia SQL con reintentos"""
        current_connection = connection
        
        for attempt in range(max_retries):
            try:
                if isinstance(sql_statement, str):
                    sql_statement = text(sql_statement)
                
                if params:
                    result = current_connection.execute(sql_statement, params)
                else:
                    result = current_connection.execute(sql_statement)
                    
                print(f"Sentencia SQL ejecutada exitosamente")
                return result, current_connection
            except Exception as e:
                error_message = str(e)
                print(f"Error al ejecutar SQL: {error_message}")
                
                # Si es el último intento, lanzar excepción
                if attempt == max_retries - 1:
                    raise DatabaseError(f"Error después de {max_retries} intentos al ejecutar SQL: {error_message}")
                
                # Si es un error de conexión, esperar y reintentar
                if "Connection is closed" in error_message or "MySQL Connection not available" in error_message:
                    print(f"Conexión no disponible. Esperando {wait_time} segundos antes de reintentar...")
                    time.sleep(wait_time)
                    
                    # Intentar reconectar
                    try:
                        # Cerrar conexión actual si está abierta
                        try:
                            if current_connection and not current_connection.closed:
                                current_connection.close()
                        except:
                            pass
                            
                        # Crear nueva conexión del tipo correcto
                        if "mysql" in str(connection.engine.url):
                            current_connection = self.mysql_engine.connect()
                            current_connection.execute(text("SET autocommit=1"))
                        else:
                            current_connection = self.mssql_engine.connect()
                        print("Reconexión exitosa para ejecutar SQL")
                    except Exception as conn_err:
                        print(f"Error al reconectar: {str(conn_err)}")
                else:
                    # Para otros errores, esperar menos tiempo
                    print(f"Reintentando ejecución SQL... (intento {attempt + 2} de {max_retries})")
                    time.sleep(5)

    def process_oracle_data(self):
        session = None
        mysql_conn = None
        
        try:
            session = self.Session()
            mysql_conn = self.mysql_engine.connect()
            
            # Configurar la sesión MySQL para autocommit
            _, mysql_conn = self.execute_with_retry("SET autocommit=1", mysql_conn)
            
            # Movimientos logísticos
            TB_ORACLE_MOVI, mysql_conn = self.read_sql_with_retry(
                "SELECT * FROM TB_LOGIS_MOVIMIENTOS", 
                mysql_conn
            )
            
            mssql_conn = self.mssql_engine.connect()
            mssql_conn = self.to_sql_with_retry(TB_ORACLE_MOVI, 'TB_LOGIS_MOVIMIENTOS', mssql_conn)
            print("Datos de movimientos logísticos transferidos")
            
            # Ejecutar procedimiento almacenado con captura de valor de retorno
            sp_result = session.execute(text("""
                DECLARE @return_value int;
                EXEC @return_value = SP_INSERT_ORACLE_DIRECTA;
                SELECT @return_value as return_value;
            """))
            return_value = sp_result.scalar()

            if return_value < 0:
                print("Error: SP_INSERT_ORACLE_DIRECTA devolvió un valor negativo ({}). Deteniendo la ejecución.".format(return_value))
                session.rollback()
                raise DatabaseError("Ejecución detenida debido a error en el SP (retorno = {}).".format(return_value))
            else:
                session.commit()
                print("Procedimiento SP_INSERT_ORACLE_DIRECTA ejecutado correctamente")

            # Leer datos de SQL Server
            try:
                Data, mssql_conn = self.read_sql_with_retry("SELECT * FROM TB_PASO_ORACLE_DIRECTAX", mssql_conn)
                print(f"Datos leídos de SQL Server: {len(Data)} registros")
            
                # Transferir datos principales con reintento
                mysql_conn = self.to_sql_with_retry(Data, 'tb_ferret_directa1', mysql_conn)

                # Proceso de usabilidad
                for table in ['tb_logistica_usabilidad_mensual', 
                            'tb_logistica_usabilidad_mensual_super']:
                    dato_carga, mysql_conn = self.read_sql_with_retry(
                        f"SELECT * FROM {table}", 
                        mysql_conn
                    )
                    mssql_conn = self.to_sql_with_retry(dato_carga, table.upper(), 
                                         mssql_conn)
                    print(f"Datos de {table} procesados")

                # Registrar en log
                _, mysql_conn = self.execute_with_retry("""
                    INSERT INTO tb_log_etl_procesos 
                    (fecha_hora, texto, total_de_datos)
                    VALUES (:fecha_hora, :texto, :total_de_datos)
                """, mysql_conn, {
                    'fecha_hora': datetime.now(),
                    'texto': 'logistica directa',
                    'total_de_datos': len(Data)
                })

                # Ejecutar procedimiento final
                _, mysql_conn = self.execute_with_retry("CALL UpdateLogisticaDirecta()", mysql_conn)
                print("Procedimiento UpdateLogisticaDirecta ejecutado")

            except Exception as e:
                print(f"Error en operaciones de base de datos: {str(e)}")
                raise

        except Exception as e:
            print(f"Error general en el proceso: {str(e)}")
            raise
        finally:
            if session:
                try:
                    session.close()
                    print("Sesión cerrada")
                except:
                    pass
            if mysql_conn:
                try:
                    if not mysql_conn.closed:
                        mysql_conn.close()
                    print("Conexión MySQL cerrada")
                except:
                    pass

    def cleanup(self):
        """Limpieza final de recursos"""
        try:
            self.mysql_engine.dispose()
            self.mssql_engine.dispose()
            print("Conexiones cerradas y recursos liberados")
        except Exception as e:
            print(f"Error durante la limpieza: {str(e)}")

def ejecutar_sincronizacion():
    db_manager = None
    try:
        db_manager = DatabaseManager()
        db_manager.process_oracle_data()
        return True, "Sincronización completada exitosamente"
    except DatabaseError as e:
        print(f"Error de base de datos después de reintentos: {str(e)}")
        return False, str(e)
    except Exception as e:
        print(f"Error general: {str(e)}")
        return False, str(e)
    finally:
        if db_manager:
            db_manager.cleanup()

if __name__ == "__main__":
    success, message = ejecutar_sincronizacion()
    if not success:
        print(f"La sincronización falló: {message}")
    else:
        print(message)