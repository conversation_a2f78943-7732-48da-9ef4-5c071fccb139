#!/usr/bin/env python3
"""
Test script for SQL Server view creation functionality.
Tests the creation and querying of latest data views.
"""

import logging
from database import DatabaseManager, create_and_query_latest_data_view
from config import SQL_SERVER_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_view_creation():
    """Test the view creation functionality."""
    logger.info("🧪 Testing SQL Server view creation functionality...")

    # Initialize database manager
    db_manager = DatabaseManager()

    try:
        # Connect to database
        if not db_manager.connect():
            logger.error("❌ Failed to connect to database")
            return False

        table_name = 'tb_toa_reporte_diario'
        view_name = f"{table_name}_latest"

        # Check if table exists
        if not db_manager.table_exists(table_name):
            logger.warning(f"⚠️ Table {table_name} does not exist, cannot test view creation")
            return False

        logger.info(f"✅ Table {table_name} exists")

        # Get latest fecha_integracion
        latest_fecha = db_manager.get_latest_fecha_integracion(table_name)
        if not latest_fecha:
            logger.warning("⚠️ No fecha_integracion found, cannot test view")
            return False

        logger.info(f"📅 Latest fecha_integracion: {latest_fecha}")

        # Create view
        logger.info(f"📊 Creating view {view_name}...")
        if not db_manager.create_view_latest_data(table_name, view_name):
            logger.error("❌ Failed to create view")
            return False

        # Query view
        logger.info(f"🔍 Querying view {view_name}...")
        results = db_manager.query_view(view_name, limit=5)  # Get first 5 rows

        if results:
            logger.info(f"✅ View query successful - Retrieved {len(results)} rows")
            logger.info("📋 Sample data from view:")
            for i, row in enumerate(results[:3]):  # Show first 3 rows
                logger.info(f"   Row {i+1}: {row[:5]}...")  # Show first 5 columns
            return True
        else:
            logger.error("❌ View query returned no results")
            return False

    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        return False

    finally:
        db_manager.disconnect()

def test_create_and_query_function():
    """Test the create_and_query_latest_data_view function."""
    logger.info("🧪 Testing create_and_query_latest_data_view function...")

    try:
        results = create_and_query_latest_data_view()

        if results:
            logger.info(f"✅ Function test successful - Retrieved {len(results)} rows")
            return True
        else:
            logger.error("❌ Function test failed - No results returned")
            return False

    except Exception as e:
        logger.error(f"❌ Function test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting SQL Server view creation tests...")

    # Run tests
    test1_success = test_view_creation()
    test2_success = test_create_and_query_function()

    # Summary
    logger.info("=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"View Creation Test: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    logger.info(f"Function Test: {'✅ PASSED' if test2_success else '❌ FAILED'}")

    if test1_success and test2_success:
        logger.info("🎉 ALL TESTS PASSED - View creation functionality is working!")
    else:
        logger.error("❌ SOME TESTS FAILED - Check logs for details")
        exit(1)

    logger.info("=" * 60)