import pandas as pd
import numpy as np
import glob
import os
from datetime import datetime
import mysql.connector
import pyodbc
from contextlib import contextmanager

class DatabaseManager:
    def __init__(self):
        # Configuración de conexiones
        self.mysql_config = {
            'user': 'ncornejo',
            'password': 'N1c0l7as17',
            'host': '**************',
            'database': 'operaciones_tqw',
            'port': 3306
        }
        
        self.mssql_config = {
            'driver': 'ODBC Driver 17 for SQL Server',
            'server': '************',
            'database': 'telqway',
            'user': 'ncornejo',
            'password': 'N1c0l7as17'
        }
    
    @contextmanager
    def mysql_connection(self):
        """Contexto para manejo seguro de conexiones MySQL"""
        conn = None
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            conn.autocommit = True
            yield conn
        except Exception as e:
            print(f"Error de conexión MySQL: {str(e)}")
            if conn and conn.is_connected():
                conn.rollback()
            raise
        finally:
            if conn and conn.is_connected():
                conn.close()
                print("Conexión MySQL cerrada")
    
    @contextmanager
    def mssql_connection(self):
        """Contexto para manejo seguro de conexiones SQL Server"""
        conn = None
        try:
            conn_str = (
                f"DRIVER={{{self.mssql_config['driver']}}};"
                f"SERVER={self.mssql_config['server']};"
                f"DATABASE={self.mssql_config['database']};"
                f"UID={self.mssql_config['user']};"
                f"PWD={self.mssql_config['password']}"
            )
            conn = pyodbc.connect(conn_str)
            yield conn
        except Exception as e:
            print(f"Error de conexión SQL Server: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
                print("Conexión SQL Server cerrada")
    
    def handle_nan_values(self, df):
        """Convierte valores NaN a None para evitar errores en SQL"""
        return df.applymap(lambda x: None if pd.isna(x) else x)
    
    def process_data_in_batches(self, df, table_name, conn, batch_size=1000):
        """Procesa datos en lotes para mejorar rendimiento y mostrar progreso"""
        total_rows = len(df)
        num_batches = (total_rows + batch_size - 1) // batch_size
        
        print(f"Procesando {total_rows} filas en {num_batches} lotes...")
        
        for i in range(0, total_rows, batch_size):
            batch = df.iloc[i:i+batch_size]
            batch = self.handle_nan_values(batch)
            
            # Preparar columnas y valores para inserción manual
            columns = batch.columns.tolist()
            placeholders = ", ".join(["?" for _ in columns])
            columns_str = ", ".join(columns)
            
            insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            
            cursor = conn.cursor()
            try:
                for _, row in batch.iterrows():
                    cursor.execute(insert_query, tuple(row))
                conn.commit()
                print(f"Lote {i//batch_size + 1}/{num_batches} completado.")
            except Exception as e:
                conn.rollback()
                print(f"Error en lote {i//batch_size + 1}: {str(e)}")
                raise
            finally:
                cursor.close()
    
    def truncate_table(self, table_name, conn):
        """Vacía una tabla de forma segura"""
        cursor = conn.cursor()
        try:
            cursor.execute(f"TRUNCATE TABLE {table_name}")
            conn.commit()
            print(f"Tabla {table_name} truncada exitosamente")
        except Exception as e:
            conn.rollback()
            print(f"Error al truncar tabla {table_name}: {str(e)}")
            raise
        finally:
            cursor.close()
    
    def execute_stored_procedure(self, sp_name, conn, params=None):
        """Ejecuta un procedimiento almacenado con manejo de errores"""
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(f"EXEC {sp_name} {params}")
            else:
                cursor.execute(f"EXEC {sp_name}")
            
            # Capturar el valor de retorno si existe
            try:
                return_value = cursor.fetchone()
                conn.commit()
                return return_value
            except:
                conn.commit()
                return None
            
        except Exception as e:
            conn.rollback()
            print(f"Error al ejecutar procedimiento {sp_name}: {str(e)}")
            raise
        finally:
            cursor.close()
    
    def process_oracle_data(self, csv_files):
        """Procesa los datos de Oracle desde archivos CSV y realiza todas las operaciones de BD"""
        try:
            # 1. Truncar tabla destino en SQL Server
            with self.mssql_connection() as mssql_conn:
                self.truncate_table("TB_PASO_0_ORACLE_DIRECTAX", mssql_conn)
                
                # 2. Procesar cada archivo CSV
                for csv_file in csv_files:
                    print(f"Procesando archivo: {csv_file}")
                    df = pd.read_csv(csv_file, sep='\t')
                    print(f"Total de registros en el DataFrame: {len(df)}")
                    
                    # Procesar en lotes
                    self.process_data_in_batches(df, "TB_PASO_0_ORACLE_DIRECTAX", mssql_conn)
                
                # 3. Ejecutar procedimiento almacenado para transformar datos
                result = self.execute_stored_procedure("SP_INSERT_ORACLE_DIRECTA", mssql_conn)
                if result and result[0] < 0:
                    print(f"Error: SP_INSERT_ORACLE_DIRECTA devolvió {result[0]}")
                    return False
                
                # 4. Leer datos transformados
                cursor = mssql_conn.cursor()
                cursor.execute("SELECT * FROM TB_PASO_ORACLE_DIRECTAX")
                rows = cursor.fetchall()
                columns = [column[0] for column in cursor.description]
                transformed_data = pd.DataFrame.from_records(rows, columns=columns)
                cursor.close()
                
                print(f"Datos transformados: {len(transformed_data)} registros")
            
            # 5. Transferir datos a MySQL
            with self.mysql_connection() as mysql_conn:
                # 5.1 Insertar en tabla principal
                cursor = mysql_conn.cursor()
                cursor.execute("SET autocommit=1")
                cursor.close()
                
                self.process_data_in_batches(transformed_data, "tb_ferret_directa1", mysql_conn)
                
                # 5.2 Procesar tablas de usabilidad
                for table in ['tb_logistica_usabilidad_mensual', 'tb_logistica_usabilidad_mensual_super']:
                    cursor = mysql_conn.cursor()
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()
                    columns = [column[0] for column in cursor.description]
                    usability_data = pd.DataFrame.from_records(rows, columns=columns)
                    cursor.close()
                    
                    print(f"Datos de {table}: {len(usability_data)} registros")
                    
                    # Transferir a SQL Server
                    with self.mssql_connection() as mssql_usability_conn:
                        self.process_data_in_batches(usability_data, table.upper(), mssql_usability_conn)
                
                # 5.3 Registrar en log
                cursor = mysql_conn.cursor()
                log_query = """
                    INSERT INTO tb_log_etl_procesos 
                    (fecha_hora, texto, total_de_datos)
                    VALUES (%s, %s, %s)
                """
                cursor.execute(log_query, (datetime.now(), 'logistica directa', len(transformed_data)))
                cursor.close()
                
                # 5.4 Ejecutar procedimiento final
                cursor = mysql_conn.cursor()
                cursor.execute("CALL UpdateLogisticaDirecta()")
                cursor.close()
                print("Procedimiento UpdateLogisticaDirecta ejecutado")
            
            return True
            
        except Exception as e:
            print(f"Error general en el proceso: {str(e)}")
            return False

def main():
    """Función principal para ejecutar el procesamiento de datos"""
    # Ruta donde se encuentran los archivos descargados
    download_folder = 'C:/Users/<USER>/Downloads/'
    
    # Buscar todos los archivos CSV en la carpeta
    csv_files = glob.glob(f"{download_folder}*.csv")
    
    if not csv_files:
        print("No se encontraron archivos CSV para procesar")
        return
    
    print(f"Se encontraron {len(csv_files)} archivos para procesar")
    
    # Iniciar el procesamiento
    db_manager = DatabaseManager()
    success = db_manager.process_oracle_data(csv_files)
    
    if success:
        print("Procesamiento de datos completado exitosamente")
    else:
        print("Hubo errores durante el procesamiento de datos")

if __name__ == "__main__":
    main()