
import pyperclip

# from selenium import webdriver
# from selenium.webdriver.common.by import By  # Importa la clase By
# from selenium.webdriver.common.keys import Keys
import time
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriveFr.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

import psutil
from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np
import sys

def cerrar_proceso(nombre_proceso):
    procesos_cerrados = 0
    for proc in psutil.process_iter():
        try:
            if proc.name().lower() == nombre_proceso.lower():
                proc.terminate()
                procesos_cerrados += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if procesos_cerrados > 0:
        print(f"Se cerraron {procesos_cerrados} procesos de {nombre_proceso}.")
    else:
        print(f"No se encontraron procesos de {nombre_proceso}.")

class ImageNotFoundError(Exception):
    pass

def buscar_imagen_en_pantalla(imagen_a_buscar):
    try:
        # Imprime el nombre del archivo
        print(f"Buscando la imagen: {imagen_a_buscar}")

        # Toma una captura de pantalla
        screenshot = pyautogui.screenshot()
        screenshot_np = np.array(screenshot)
        screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

        # Lee la imagen a buscar y conviértela a escala de grises
        template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

        w, h = template.shape[::-1]

        # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
        result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
        loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

        # Si encontró la imagen, devuelve las coordenadas del centro
        for pt in zip(*loc[::-1]):
            centro_x = pt[0] + w // 2
            centro_y = pt[1] + h // 2
            return (centro_x, centro_y)

        # Si no encontró la imagen, verifica si es una de las excepciones
        if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\SecurityBox.png",
                               "C:\\Users\\<USER>\\Desktop\\SecurityRun.png",
                               "C:\\Users\\<USER>\\Desktop\\UsuarioChrome.png",
                               "C:\\Users\\<USER>\\Desktop\\UsuarioOracleJcepeda.png"]:
            print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
            return None

        cerrar_proceso("jp2launcher.exe")
        cerrar_proceso("chrome.exe")
        return "IMAGE_NOT_FOUND"

    except Exception as e:
        print(f"Ocurrió un error durante la búsqueda de la imagen: {str(e)}")
        return "IMAGE_NOT_FOUND"


# Ejemplo de uso



# ConsultaTransaccionSeries
# ConsultaTransaccion
# OkBoton
# BotonCerrarF
# CierreXF

time.sleep(8)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Chrome.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(15)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\UrlChrome.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(3)

paralabra = "ebs.andes.vtr.cl:8000/OA_HTML/AppsLocalLogin.jsp?requestUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FOA.jsp%3Fpage%3D%2Foracle%2Fapps%2Ffnd%2Fframework%2Fnavigate%2Fwebui%2FNewHomePG%26homePage%3DY%26OAPB%3DFWK_HOMEPAGE_BRAND%26oapc%3D2%26transactionid%3D146981584%26oas%3DCfzXvWhkd4wc5PbN_4wdyQ..&cancelUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FAppsLogin&langCode=US" # Escribe la palabra en el campo de texto
pyautogui.write(paralabra)


time.sleep(5)

# Presiona la tecla Enter para confirmar la entrada (opcional)
pyautogui.press('enter')

time.sleep(4)


coordenadasX = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\UsuarioOracleJcepeda.png')


if coordenadasX:
        print("Resultado de coordenadas:", coordenadasX)
        clave = "jcepeda"
        pyautogui.write(clave)

        time.sleep(3)

        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\UsuarioChrome.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])

        time.sleep(3)

     
else:

    
     clave = "peru2024"
     pyautogui.write(clave)
     time.sleep(3)

# coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ClaveOracle.png')
# if coordenadas:
#     pyautogui.click(coordenadas[0], coordenadas[1])



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\LoginOracle.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(6)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OracleNivel1.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(8)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\StockMano.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(6)



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CantidadManoII.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(4)



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\archivoDescarga.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(2)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\abrirArchivo.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(5)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\SecurityBox.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(4)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\SecurityRun.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(20)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\G40Stgo_directo.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])





time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Organization.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\G40_ferret.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CeldaVacia.png')
if coordenadas:
       pyautogui.rightClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CopyAll.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CierreVentana.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])






####### Obtener el contenido del portapapeles
clipboard_content = pyperclip.paste()

# Crear un DataFrame a partir del contenido del portapapeles
data = pd.read_clipboard()

# Imprimir el DataFrame en pantalla
print(data)

# Obtener la cantidad de registros (filas) en el DataFrame
num_registros = len(data)
print(f"Número de registros: {num_registros}")


time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Vina.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\StockPanel.png')
if coordenadas:
       pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CantidadPanel.png')
if coordenadas:
       pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])





time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])





time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Organization.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\D31_ferret.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CeldaVacia.png')
if coordenadas:
       pyautogui.rightClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CopyAll.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CierreVentana.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(3)



####### Obtener el contenido del portapapeles
clipboard_content = pyperclip.paste()

# Crear un DataFrame a partir del contenido del portapapeles
data = pd.read_clipboard()

# Imprimir el DataFrame en pantalla
print(data)

# Obtener la cantidad de registros (filas) en el DataFrame
num_registros = len(data)
print(f"Número de registros: {num_registros}")



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])