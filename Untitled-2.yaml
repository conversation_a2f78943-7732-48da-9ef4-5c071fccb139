2025-05-04 21:27:04,226 - INFO - Actualización de la interfaz completada con éxito
2025-05-04 21:27:17,134 - INFO - DIAGNÓSTICO: Bucle principal activo - 30 iteraciones
2025-05-04 21:27:47,600 - INFO - DIAGNÓSTICO: Bucle principal activo - 60 iteraciones
2025-05-04 21:27:47,600 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:28:18,004 - INFO - DIAGNÓSTICO: Bucle principal activo - 90 iteraciones
2025-05-04 21:28:47,391 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:28:47,391 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:28:48,400 - INFO - DIAGNÓSTICO: Bucle principal activo - 120 iteraciones
2025-05-04 21:29:18,796 - INFO - DIAGNÓSTICO: Bucle principal activo - 150 iteraciones
2025-05-04 21:29:47,200 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:29:48,209 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:29:48,209 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:29:48,209 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:29:48,209 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:29:48,209 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea FlujoMysql_i: local variable 'os' referenced before assignment
2025-05-04 21:29:48,209 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:29:48,209 - INFO - Tarea FlujoMysql_i añadida a la cola de ejecución
2025-05-04 21:29:48,209 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:29:48,209 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:29:49,234 - INFO - DIAGNÓSTICO: Bucle principal activo - 180 iteraciones
2025-05-04 21:30:19,702 - INFO - DIAGNÓSTICO: Bucle principal activo - 210 iteraciones
2025-05-04 21:30:47,073 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:30:47,073 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:30:50,114 - INFO - DIAGNÓSTICO: Bucle principal activo - 240 iteraciones
2025-05-04 21:31:20,564 - INFO - DIAGNÓSTICO: Bucle principal activo - 270 iteraciones
2025-05-04 21:31:47,968 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:31:47,968 - INFO - DIAGNÓSTICO: Estado del programador: 20 trabajos programados. Uptime: 300 segundos
2025-05-04 21:31:47,968 - INFO - DIAGNÓSTICO: Verificando cambios en la configuración...
2025-05-04 21:31:51,006 - INFO - DIAGNÓSTICO: Bucle principal activo - 300 iteraciones
2025-05-04 21:32:21,474 - INFO - DIAGNÓSTICO: Bucle principal activo - 330 iteraciones
2025-05-04 21:32:47,846 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:32:47,846 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:32:48,389 - WARNING - Corrigiendo estado de tarea NdcBot: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:32:48,389 - WARNING - Corrigiendo estado de tarea Py_INSERT_SQL_SERVER: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:32:48,389 - WARNING - Corrigiendo estado de tarea PyTOA30: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:32:48,389 - WARNING - Corrigiendo estado de tarea Turnos_Python: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:32:48,389 - WARNING - Corrigiendo estado de tarea PyLogisticaMat: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:32:48,852 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:32:48,852 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:32:48,852 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:32:48,852 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:32:48,852 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea FlujoMysql_i: local variable 'os' referenced before assignment
2025-05-04 21:32:48,852 - ERROR - Traceback (most recent call last):
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '56875' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:35:38,846 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:35:38,846 - INFO - Archivo de configuración encontrado
2025-05-04 21:35:38,846 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:35:38,846 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:35:38,846 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:35:38,846 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:35:38,861 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:35:38,861 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:35:38,861 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 1922954749040)
2025-05-04 21:35:38,861 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:35:38,861 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:35:38,861 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:35:38,861 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:35:38,861 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea NdcBot: local variable 'os' referenced before assignment
2025-05-04 21:35:38,861 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,861 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:35:38,861 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:35:38,861 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:35:38,861 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Py_INSERT_SQL_SERVER: local variable 'os' referenced before assignment
2025-05-04 21:35:38,861 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,861 - INFO - Tarea Py_INSERT_SQL_SERVER añadida a la cola de ejecución
2025-05-04 21:35:38,861 - INFO - Tareas cada 30 minutos programadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 1922954747456)
2025-05-04 21:35:38,861 - INFO - Total de trabajos programados ahora: 13
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Ejecutando tarea PyTOA30 directamente sin usar cola
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyTOA30
2025-05-04 21:35:38,861 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyTOA30 =====
2025-05-04 21:35:38,861 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:35:38,861 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea PyTOA30: local variable 'os' referenced before assignment
2025-05-04 21:35:38,861 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,861 - INFO - Tarea PyTOA30 añadida a la cola de ejecución
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:35:38,861 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:35:38,861 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:35:38,861 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:35:38,861 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Turnos_Python: local variable 'os' referenced before assignment
2025-05-04 21:35:38,878 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,878 - INFO - Tarea Turnos_Python añadida a la cola de ejecución
2025-05-04 21:35:38,878 - INFO - DIAGNÓSTICO: Ejecutando tarea PyLogisticaMat directamente sin usar cola
2025-05-04 21:35:38,878 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyLogisticaMat
2025-05-04 21:35:38,879 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyLogisticaMat =====
2025-05-04 21:35:38,879 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:35:38,879 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea PyLogisticaMat: local variable 'os' referenced before assignment
2025-05-04 21:35:38,880 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,880 - INFO - Tarea PyLogisticaMat añadida a la cola de ejecución
2025-05-04 21:35:38,880 - INFO - Tareas cada 3 minutos programadas: FlujoMysql_i (Job ID: 1922954746880)
2025-05-04 21:35:38,880 - INFO - Total de trabajos programados ahora: 14
2025-05-04 21:35:38,881 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:35:38,881 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:35:38,881 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:35:38,881 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:35:38,882 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea FlujoMysql_i: local variable 'os' referenced before assignment
2025-05-04 21:35:38,882 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:38,883 - INFO - Tarea FlujoMysql_i añadida a la cola de ejecución
2025-05-04 21:35:38,883 - INFO - Todas las tareas han sido programadas
2025-05-04 21:35:38,883 - INFO - DIAGNÓSTICO: Se han programado 14 trabajos en total
2025-05-04 21:35:38,883 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,883 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,884 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,884 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,885 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,885 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,885 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,885 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,885 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,886 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,886 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:35:38,886 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:35:38,887 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:35:38,887 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:35:38,887 - INFO - DIAGNÓSTICO: Configurando bucle de eventos asyncio
2025-05-04 21:35:38,888 - INFO - DIAGNÓSTICO: Bucle de eventos configurado correctamente
2025-05-04 21:35:38,888 - INFO - DIAGNÓSTICO: Creando worker_task
2025-05-04 21:35:38,889 - INFO - DIAGNÓSTICO: Worker_task creado correctamente
2025-05-04 21:35:38,889 - INFO - DIAGNÓSTICO: Callback de worker configurado correctamente
2025-05-04 21:35:38,889 - INFO - DIAGNÓSTICO: Iniciando hilo del schedule
2025-05-04 21:35:38,891 - INFO - DIAGNÓSTICO: Hilo del schedule iniciado correctamente
2025-05-04 21:35:38,891 - INFO - Programador de tareas iniciado correctamente
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Bucle principal del programador iniciado
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Ejecutando 14 jobs pendientes
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Estado del programador: 14 trabajos programados. Uptime: 0 segundos
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Verificando cambios en la configuración...
2025-05-04 21:35:38,944 - INFO - DIAGNÓSTICO: Verificando estado de tareas por intervalo...
2025-05-04 21:35:38,944 - WARNING - No se encontró trabajo programado para intervalo de 120 minutos. Reprogramando.
2025-05-04 21:35:38,944 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 1922954744864)
2025-05-04 21:35:38,944 - INFO - Tarea NdcBot omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:35:38,944 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:35:38,944 - WARNING - No se encontró trabajo programado para intervalo de 30 minutos. Reprogramando.
2025-05-04 21:35:38,944 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 1922954743904)
2025-05-04 21:35:38,944 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,944 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,944 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,944 - WARNING - No se encontró trabajo programado para intervalo de 3 minutos. Reprogramando.
2025-05-04 21:35:38,944 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 1922954743712)
2025-05-04 21:35:38,960 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:35:38,960 - WARNING - Los siguientes intervalos no tienen trabajos programados: {120, 3, 30}
2025-05-04 21:35:38,960 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 1922954743328)
2025-05-04 21:35:38,960 - INFO - Tarea NdcBot omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:35:38,960 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:35:38,960 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 1922954743136)
2025-05-04 21:35:38,960 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:35:38,960 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 1922954742800)
2025-05-04 21:35:38,960 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,960 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,960 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:35:38,960 - INFO - Verificación de tareas por intervalo completada
2025-05-04 21:35:47,455 - INFO - Pestaña actual: Ejecuciones por Intervalo
2025-05-04 21:35:47,456 - INFO - Seleccionado en Intervalo 30: Turnos_Python
2025-05-04 21:35:50,502 - INFO - Log limpiado por el usuario
2025-05-04 21:35:50,503 - INFO - EJECUTANDO MANUALMENTE: Turnos_Python
2025-05-04 21:35:50,503 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:35:50,503 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:35:50,503 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:35:50,504 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:35:50,504 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Turnos_Python: local variable 'os' referenced before assignment
2025-05-04 21:35:50,504 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:35:50,504 - INFO - Iniciando actualización de la interfaz gráfica
2025-05-04 21:35:50,643 - INFO - Actualización de la interfaz completada con éxito
2025-05-04 21:36:08,335 - INFO - DIAGNÓSTICO: Bucle principal activo - 30 iteraciones
2025-05-04 21:36:38,041 - INFO - Aplicación cerrando por solicitud del usuario
Presiona Enter para salir...invalid command name "1922966173056_safe_update"
    while executing
"1922966173056_safe_update"
    ("after" script)
2025-05-04 21:36:38,720 - INFO - DIAGNÓSTICO: Bucle principal activo - 60 iteraciones
2025-05-04 21:36:38,720 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '56930' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:36:56,035 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:36:56,035 - INFO - Archivo de configuración encontrado
2025-05-04 21:36:56,036 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:36:56,036 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:36:56,036 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:36:56,037 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:36:56,037 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:36:56,037 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:36:56,037 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:36:56,038 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:36:56,038 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:36:56,039 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:36:56,039 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:36:56,039 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:36:56,039 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:36:56,040 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:36:56,040 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:36:56,040 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:36:56,041 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:36:56,041 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:36:56,041 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:36:56,041 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:36:56,041 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:36:56,042 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:36:56,042 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:36:56,042 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:36:56,043 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:36:56,043 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:36:56,043 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:36:56,043 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:36:56,043 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:36:56,044 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:36:56,045 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:36:56,045 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:36:56,045 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:36:56,045 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:36:56,046 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:36:56,046 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:36:56,046 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:36:56,046 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:36:56,046 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:36:56,047 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:36:56,047 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:36:56,047 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:36:56,047 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2683047306352)
2025-05-04 21:36:56,048 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:36:56,048 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:36:56,048 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:36:56,048 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:36:56,048 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:36:56,049 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:36:56,049 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea NdcBot: local variable 'os' referenced before assignment
2025-05-04 21:36:56,051 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,051 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:36:56,052 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:36:56,052 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:36:56,052 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:36:56,052 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:36:56,052 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Py_INSERT_SQL_SERVER: local variable 'os' referenced before assignment
2025-05-04 21:36:56,053 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,053 - INFO - Tarea Py_INSERT_SQL_SERVER añadida a la cola de ejecución
2025-05-04 21:36:56,053 - INFO - Tareas cada 30 minutos programadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2683047304768)
2025-05-04 21:36:56,053 - INFO - Total de trabajos programados ahora: 13
2025-05-04 21:36:56,054 - INFO - DIAGNÓSTICO: Ejecutando tarea PyTOA30 directamente sin usar cola
2025-05-04 21:36:56,054 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyTOA30
2025-05-04 21:36:56,054 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyTOA30 =====
2025-05-04 21:36:56,054 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:36:56,054 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea PyTOA30: local variable 'os' referenced before assignment
2025-05-04 21:36:56,055 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,055 - INFO - Tarea PyTOA30 añadida a la cola de ejecución
2025-05-04 21:36:56,055 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:36:56,055 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:36:56,056 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:36:56,056 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:36:56,056 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Turnos_Python: local variable 'os' referenced before assignment
2025-05-04 21:36:56,056 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,056 - INFO - Tarea Turnos_Python añadida a la cola de ejecución
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Ejecutando tarea PyLogisticaMat directamente sin usar cola
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyLogisticaMat
2025-05-04 21:36:56,056 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyLogisticaMat =====
2025-05-04 21:36:56,056 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:36:56,056 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea PyLogisticaMat: local variable 'os' referenced before assignment
2025-05-04 21:36:56,056 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,056 - INFO - Tarea PyLogisticaMat añadida a la cola de ejecución
2025-05-04 21:36:56,056 - INFO - Tareas cada 3 minutos programadas: FlujoMysql_i (Job ID: 2683047304192)
2025-05-04 21:36:56,056 - INFO - Total de trabajos programados ahora: 14
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:36:56,056 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:36:56,056 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:36:56,056 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea FlujoMysql_i: local variable 'os' referenced before assignment
2025-05-04 21:36:56,056 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:36:56,056 - INFO - Tarea FlujoMysql_i añadida a la cola de ejecución
2025-05-04 21:36:56,056 - INFO - Todas las tareas han sido programadas
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Se han programado 14 trabajos en total
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Configurando bucle de eventos asyncio
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Bucle de eventos configurado correctamente
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Creando worker_task
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Worker_task creado correctamente
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Callback de worker configurado correctamente
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Iniciando hilo del schedule
2025-05-04 21:36:56,056 - INFO - DIAGNÓSTICO: Hilo del schedule iniciado correctamente
2025-05-04 21:36:56,056 - INFO - Programador de tareas iniciado correctamente
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Bucle principal del programador iniciado
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Ejecutando 14 jobs pendientes
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Estado del programador: 14 trabajos programados. Uptime: 0 segundos
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Verificando cambios en la configuración...
2025-05-04 21:36:56,110 - INFO - DIAGNÓSTICO: Verificando estado de tareas por intervalo...
2025-05-04 21:36:56,110 - WARNING - No se encontró trabajo programado para intervalo de 120 minutos. Reprogramando.
2025-05-04 21:36:56,110 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2683047301552)
2025-05-04 21:36:56,110 - INFO - Tarea NdcBot omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:36:56,110 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:36:56,126 - WARNING - No se encontró trabajo programado para intervalo de 30 minutos. Reprogramando.
2025-05-04 21:36:56,126 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2683047301696)
2025-05-04 21:36:56,126 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - WARNING - No se encontró trabajo programado para intervalo de 3 minutos. Reprogramando.
2025-05-04 21:36:56,127 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 2683047301168)
2025-05-04 21:36:56,127 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:36:56,127 - WARNING - Los siguientes intervalos no tienen trabajos programados: {120, 3, 30}
2025-05-04 21:36:56,127 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2683047300640)
2025-05-04 21:36:56,127 - INFO - Tarea NdcBot omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:36:56,127 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 60.0 minutos
2025-05-04 21:36:56,127 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 2683047300976)
2025-05-04 21:36:56,127 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:36:56,127 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2683047300112)
2025-05-04 21:36:56,127 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:36:56,127 - INFO - Verificación de tareas por intervalo completada
2025-05-04 21:37:02,047 - INFO - Pestaña actual: Ejecuciones por Intervalo
2025-05-04 21:37:02,047 - INFO - Seleccionado en Intervalo 30: Turnos_Python
2025-05-04 21:37:03,378 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:37:03,379 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:37:03,380 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:37:03,381 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:37:03,382 - ERROR - DIAGNÓSTICO: Error general al ejecutar tarea Turnos_Python: local variable 'os' referenced before assignment
2025-05-04 21:37:03,383 - ERROR - Traceback (most recent call last):
  File "c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py", line 930, in execute_task_directly
    file_name = os.path.basename(task.path)
UnboundLocalError: local variable 'os' referenced before assignment

2025-05-04 21:37:25,466 - INFO - DIAGNÓSTICO: Bucle principal activo - 30 iteraciones
2025-05-04 21:37:55,903 - INFO - DIAGNÓSTICO: Bucle principal activo - 60 iteraciones
2025-05-04 21:37:56,909 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:38:26,303 - INFO - DIAGNÓSTICO: Bucle principal activo - 90 iteraciones
2025-05-04 21:38:56,739 - INFO - DIAGNÓSTICO: Bucle principal activo - 120 iteraciones
2025-05-04 21:38:56,739 - INFO - DIAGNÓSTICO: Ejecutando 20 jobs pendientes
2025-05-04 21:38:56,739 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:39:27,146 - INFO - DIAGNÓSTICO: Bucle principal activo - 150 iteraciones
2025-05-04 21:39:29,738 - INFO - Aplicación cerrando por solicitud del usuario
Presiona Enter para salir...invalid command name "2683058745728_safe_update"
    while executing
"2683058745728_safe_update"
    ("after" script)
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '57042' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:39:54,496 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:39:54,497 - INFO - Archivo de configuración encontrado
2025-05-04 21:39:54,498 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:39:54,498 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:39:54,498 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:39:54,499 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:39:54,499 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:39:54,499 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2781220955248)
2025-05-04 21:39:54,499 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:39:54,499 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:39:54,499 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:39:54,499 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:39:54,499 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py"
2025-05-04 21:39:54,515 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 35912
2025-05-04 21:39:55,526 - INFO - ===== RESULTADO: NdcBot - INICIADO CORRECTAMENTE (1.03 segundos) =====
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Tarea NdcBot eliminada de running_tasks
2025-05-04 21:39:55,526 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:39:55,526 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:39:55,526 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:39:55,526 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
"
2025-05-04 21:39:55,541 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 30900
2025-05-04 21:39:56,550 - INFO - ===== RESULTADO: Py_INSERT_SQL_SERVER - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:39:56,565 - INFO - DIAGNÓSTICO: Tarea Py_INSERT_SQL_SERVER eliminada de running_tasks
2025-05-04 21:39:56,596 - INFO - Tarea Py_INSERT_SQL_SERVER añadida a la cola de ejecución
2025-05-04 21:39:56,596 - INFO - Tareas cada 30 minutos programadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2781220953664)
2025-05-04 21:39:56,596 - INFO - Total de trabajos programados ahora: 13
2025-05-04 21:39:56,596 - INFO - DIAGNÓSTICO: Ejecutando tarea PyTOA30 directamente sin usar cola
2025-05-04 21:39:56,596 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyTOA30
2025-05-04 21:39:56,596 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyTOA30 =====
2025-05-04 21:39:56,596 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:39:56,596 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:39:56,596 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:39:56,596 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py"
2025-05-04 21:39:56,612 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 27620
2025-05-04 21:39:57,614 - INFO - ===== RESULTADO: PyTOA30 - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:39:57,614 - INFO - DIAGNÓSTICO: Tarea PyTOA30 eliminada de running_tasks
2025-05-04 21:39:57,614 - INFO - Tarea PyTOA30 añadida a la cola de ejecución
2025-05-04 21:39:57,614 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:39:57,616 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:39:57,617 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:39:57,617 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:39:57,617 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:39:57,617 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:39:57,617 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py"
2025-05-04 21:39:57,617 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 3188
2025-05-04 21:39:58,621 - INFO - ===== RESULTADO: Turnos_Python - INICIADO CORRECTAMENTE (1.00 segundos) =====
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Tarea Turnos_Python eliminada de running_tasks
2025-05-04 21:39:58,621 - INFO - Tarea Turnos_Python añadida a la cola de ejecución
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Ejecutando tarea PyLogisticaMat directamente sin usar cola
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyLogisticaMat
2025-05-04 21:39:58,621 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyLogisticaMat =====
2025-05-04 21:39:58,621 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py"
2025-05-04 21:39:58,621 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 31988
2025-05-04 21:39:59,644 - INFO - ===== RESULTADO: PyLogisticaMat - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Tarea PyLogisticaMat eliminada de running_tasks
2025-05-04 21:39:59,644 - INFO - Tarea PyLogisticaMat añadida a la cola de ejecución
2025-05-04 21:39:59,644 - INFO - Tareas cada 3 minutos programadas: FlujoMysql_i (Job ID: 2781220953232)
2025-05-04 21:39:59,644 - INFO - Total de trabajos programados ahora: 14
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:39:59,644 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:39:59,644 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py"
2025-05-04 21:39:59,644 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 36632
2025-05-04 21:40:00,645 - INFO - ===== RESULTADO: FlujoMysql_i - INICIADO CORRECTAMENTE (1.00 segundos) =====
2025-05-04 21:40:00,645 - INFO - DIAGNÓSTICO: Tarea FlujoMysql_i eliminada de running_tasks
2025-05-04 21:40:00,646 - INFO - Tarea FlujoMysql_i añadida a la cola de ejecución
2025-05-04 21:40:00,647 - INFO - Todas las tareas han sido programadas
2025-05-04 21:40:00,647 - INFO - DIAGNÓSTICO: Se han programado 14 trabajos en total
2025-05-04 21:40:00,648 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,649 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,650 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,650 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,651 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,651 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,652 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,652 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,653 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,654 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,655 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:40:00,655 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:40:00,656 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:40:00,656 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:40:00,657 - INFO - DIAGNÓSTICO: Configurando bucle de eventos asyncio
2025-05-04 21:40:00,661 - INFO - DIAGNÓSTICO: Bucle de eventos configurado correctamente
2025-05-04 21:40:00,661 - INFO - DIAGNÓSTICO: Creando worker_task
2025-05-04 21:40:00,662 - INFO - DIAGNÓSTICO: Worker_task creado correctamente
2025-05-04 21:40:00,664 - INFO - DIAGNÓSTICO: Callback de worker configurado correctamente
2025-05-04 21:40:00,664 - INFO - DIAGNÓSTICO: Iniciando hilo del schedule
2025-05-04 21:40:00,669 - INFO - DIAGNÓSTICO: Hilo del schedule iniciado correctamente
2025-05-04 21:40:00,670 - INFO - Programador de tareas iniciado correctamente
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Bucle principal del programador iniciado
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Ejecutando 14 jobs pendientes
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Estado de la cola: 0 tareas en cola, 0 en ejecución
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Estado del programador: 14 trabajos programados. Uptime: 0 segundos
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Verificando cambios en la configuración...
2025-05-04 21:40:00,734 - INFO - DIAGNÓSTICO: Verificando estado de tareas por intervalo...
2025-05-04 21:40:00,734 - WARNING - No se encontró trabajo programado para intervalo de 120 minutos. Reprogramando.
2025-05-04 21:40:00,734 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2781220950496)
2025-05-04 21:40:00,734 - INFO - Tarea NdcBot omitida, próxima ejecución en 59.9 minutos
2025-05-04 21:40:00,734 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 59.9 minutos
2025-05-04 21:40:00,734 - WARNING - No se encontró trabajo programado para intervalo de 30 minutos. Reprogramando.
2025-05-04 21:40:00,750 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2781220950544)
2025-05-04 21:40:00,750 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 14.9 minutos
2025-05-04 21:40:00,751 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 14.9 minutos
2025-05-04 21:40:00,751 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:40:00,752 - WARNING - No se encontró trabajo programado para intervalo de 3 minutos. Reprogramando.
2025-05-04 21:40:00,752 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 2781220950112)
2025-05-04 21:40:00,752 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:40:00,752 - WARNING - Los siguientes intervalos no tienen trabajos programados: {120, 3, 30}
2025-05-04 21:40:00,752 - INFO - Tareas cada 120 minutos reprogramadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2781220949728)
2025-05-04 21:40:00,752 - INFO - Tarea NdcBot omitida, próxima ejecución en 59.9 minutos
2025-05-04 21:40:00,752 - INFO - Tarea Py_INSERT_SQL_SERVER omitida, próxima ejecución en 59.9 minutos
2025-05-04 21:40:00,752 - INFO - Tareas cada 3 minutos reprogramadas: FlujoMysql_i (Job ID: 2781220949680)
2025-05-04 21:40:00,752 - INFO - Tarea FlujoMysql_i omitida, próxima ejecución en 1.5 minutos
2025-05-04 21:40:00,752 - INFO - Tareas cada 30 minutos reprogramadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2781220949152)
2025-05-04 21:40:00,752 - INFO - Tarea PyTOA30 omitida, próxima ejecución en 14.9 minutos
2025-05-04 21:40:00,752 - INFO - Tarea Turnos_Python omitida, próxima ejecución en 14.9 minutos
2025-05-04 21:40:00,752 - INFO - Tarea PyLogisticaMat omitida, próxima ejecución en 15.0 minutos
2025-05-04 21:40:00,752 - INFO - Verificación de tareas por intervalo completada
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '57313' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:45:17,523 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:45:17,523 - INFO - Archivo de configuración encontrado
2025-05-04 21:45:17,523 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:45:17,523 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:45:17,523 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:45:17,523 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:45:17,523 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:45:17,523 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:45:17,523 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:45:17,523 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:45:17,539 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:45:17,539 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:45:17,539 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2981960195744)
2025-05-04 21:45:17,539 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:45:17,539 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:45:17,539 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:45:17,539 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:17,539 - INFO - Actualización de panel: Tarea=NdcBot, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:17.539631, Info=
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:17,539 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py"
2025-05-04 21:45:17,555 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 4724
2025-05-04 21:45:18,565 - INFO - ===== RESULTADO: NdcBot - INICIADO CORRECTAMENTE (1.03 segundos) =====
2025-05-04 21:45:18,565 - INFO - Actualización de panel: Tarea=NdcBot, Estado=completed, Duración=1.03s, Hora=2025-05-04 21:45:18.565341, Info=
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Tarea NdcBot eliminada de running_tasks
2025-05-04 21:45:18,565 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:45:18,565 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:45:18,565 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:18,565 - INFO - Actualización de panel: Tarea=Py_INSERT_SQL_SERVER, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:18.565341, Info=
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
"
2025-05-04 21:45:18,565 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 10452
2025-05-04 21:45:19,705 - INFO - ===== RESULTADO: Py_INSERT_SQL_SERVER - INICIADO CORRECTAMENTE (1.14 segundos) =====
2025-05-04 21:45:19,715 - INFO - Actualización de panel: Tarea=Py_INSERT_SQL_SERVER, Estado=completed, Duración=1.14s, Hora=2025-05-04 21:45:19.705728, Info=
2025-05-04 21:45:19,715 - INFO - DIAGNÓSTICO: Tarea Py_INSERT_SQL_SERVER eliminada de running_tasks
2025-05-04 21:45:19,715 - INFO - Tarea Py_INSERT_SQL_SERVER añadida a la cola de ejecución
2025-05-04 21:45:19,716 - INFO - Tareas cada 30 minutos programadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2981960194112)
2025-05-04 21:45:19,716 - INFO - Total de trabajos programados ahora: 13
2025-05-04 21:45:19,716 - INFO - DIAGNÓSTICO: Ejecutando tarea PyTOA30 directamente sin usar cola
2025-05-04 21:45:19,716 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyTOA30
2025-05-04 21:45:19,717 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyTOA30 =====
2025-05-04 21:45:19,717 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:45:19,717 - INFO - Actualización de panel: Tarea=PyTOA30, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:19.717760, Info=
2025-05-04 21:45:19,718 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:45:19,718 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:45:19,719 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py"
2025-05-04 21:45:19,796 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 43692
2025-05-04 21:45:20,813 - INFO - ===== RESULTADO: PyTOA30 - INICIADO CORRECTAMENTE (1.10 segundos) =====
2025-05-04 21:45:20,813 - INFO - Actualización de panel: Tarea=PyTOA30, Estado=completed, Duración=1.10s, Hora=2025-05-04 21:45:20.813911, Info=
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Tarea PyTOA30 eliminada de running_tasks
2025-05-04 21:45:20,813 - INFO - Tarea PyTOA30 añadida a la cola de ejecución
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Ejecutando tarea Turnos_Python directamente sin usar cola
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Turnos_Python
2025-05-04 21:45:20,813 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Turnos_Python =====
2025-05-04 21:45:20,813 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:45:20,813 - INFO - Actualización de panel: Tarea=Turnos_Python, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:20.813911, Info=
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py"
2025-05-04 21:45:20,813 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 32956
2025-05-04 21:45:21,824 - INFO - ===== RESULTADO: Turnos_Python - INICIADO CORRECTAMENTE (1.01 segundos) =====
2025-05-04 21:45:21,827 - INFO - Actualización de panel: Tarea=Turnos_Python, Estado=completed, Duración=1.01s, Hora=2025-05-04 21:45:21.824371, Info=
2025-05-04 21:45:21,830 - INFO - DIAGNÓSTICO: Tarea Turnos_Python eliminada de running_tasks
2025-05-04 21:45:21,832 - INFO - Tarea Turnos_Python añadida a la cola de ejecución
2025-05-04 21:45:21,835 - INFO - DIAGNÓSTICO: Ejecutando tarea PyLogisticaMat directamente sin usar cola
2025-05-04 21:45:21,843 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyLogisticaMat
2025-05-04 21:45:21,846 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyLogisticaMat =====
2025-05-04 21:45:21,849 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:45:21,853 - INFO - Actualización de panel: Tarea=PyLogisticaMat, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:21.846430, Info=
2025-05-04 21:45:21,855 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:45:21,855 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py
2025-05-04 21:45:21,855 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py"
2025-05-04 21:45:21,871 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 40920
2025-05-04 21:45:22,872 - INFO - ===== RESULTADO: PyLogisticaMat - INICIADO CORRECTAMENTE (1.03 segundos) =====
2025-05-04 21:45:22,872 - INFO - Actualización de panel: Tarea=PyLogisticaMat, Estado=completed, Duración=1.03s, Hora=2025-05-04 21:45:22.872359, Info=
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Tarea PyLogisticaMat eliminada de running_tasks
2025-05-04 21:45:22,872 - INFO - Tarea PyLogisticaMat añadida a la cola de ejecución
2025-05-04 21:45:22,872 - INFO - Tareas cada 3 minutos programadas: FlujoMysql_i (Job ID: 2981960193488)
2025-05-04 21:45:22,872 - INFO - Total de trabajos programados ahora: 14
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Ejecutando tarea FlujoMysql_i directamente sin usar cola
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea FlujoMysql_i
2025-05-04 21:45:22,872 - INFO - ===== INICIO EJECUCIÓN DIRECTA: FlujoMysql_i =====
2025-05-04 21:45:22,872 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:45:22,872 - INFO - Actualización de panel: Tarea=FlujoMysql_i, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:22.872359, Info=
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py
2025-05-04 21:45:22,872 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py"
2025-05-04 21:45:22,894 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 29404
2025-05-04 21:45:23,894 - INFO - ===== RESULTADO: FlujoMysql_i - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:45:23,894 - INFO - Actualización de panel: Tarea=FlujoMysql_i, Estado=completed, Duración=1.02s, Hora=2025-05-04 21:45:23.894125, Info=
2025-05-04 21:45:23,898 - INFO - DIAGNÓSTICO: Tarea FlujoMysql_i eliminada de running_tasks
2025-05-04 21:45:23,898 - INFO - Tarea FlujoMysql_i añadida a la cola de ejecución
2025-05-04 21:45:23,901 - INFO - Todas las tareas han sido programadas
2025-05-04 21:45:23,901 - INFO - DIAGNÓSTICO: Se han programado 14 trabajos en total
2025-05-04 21:45:23,901 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,901 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,902 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,902 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,902 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,903 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,903 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,903 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,904 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,904 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,905 - INFO - DIAGNÓSTICO: Job programado - Función: execute_task
2025-05-04 21:45:23,905 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:45:23,905 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:45:23,905 - INFO - DIAGNÓSTICO: Job programado - Función: execute_interval_tasks
2025-05-04 21:45:23,906 - INFO - DIAGNÓSTICO: Configurando bucle de eventos asyncio
2025-05-04 21:45:23,907 - INFO - DIAGNÓSTICO: Bucle de eventos configurado correctamente
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '57398' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:45:48,704 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:45:48,705 - INFO - Archivo de configuración encontrado
2025-05-04 21:45:48,705 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:45:48,706 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:45:48,706 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:45:48,706 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:45:48,706 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:45:48,707 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:45:48,707 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:45:48,707 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:48,707 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:48,708 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:48,708 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:48,708 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:48,708 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:48,708 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:45:48,709 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:45:48,710 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:45:48,710 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:45:48,710 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:45:48,710 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:45:48,710 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:45:48,711 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:45:48,711 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:45:48,711 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:45:48,711 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:45:48,711 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:45:48,712 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:45:48,712 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:45:48,712 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:45:48,712 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:45:48,712 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:45:48,713 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:45:48,714 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:45:48,714 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:45:48,714 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:45:48,714 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:45:48,714 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:45:48,715 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:45:48,715 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:45:48,715 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:45:48,715 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:45:48,715 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:45:48,716 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:45:48,716 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 3012041727648)
2025-05-04 21:45:48,716 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:45:48,717 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:45:48,717 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:45:48,717 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:45:48,717 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:45:48,718 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:48,719 - INFO - Actualización de panel: Tarea=NdcBot, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:48.717431, Info=
2025-05-04 21:45:48,719 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:48,719 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:45:48,719 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py"
2025-05-04 21:45:48,729 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 42676
2025-05-04 21:45:49,731 - INFO - ===== RESULTADO: NdcBot - INICIADO CORRECTAMENTE (1.01 segundos) =====
2025-05-04 21:45:49,731 - INFO - Actualización de panel: Tarea=NdcBot, Estado=completed, Duración=1.01s, Hora=2025-05-04 21:45:49.731988, Info=
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Tarea NdcBot eliminada de running_tasks
2025-05-04 21:45:49,731 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:45:49,731 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:45:49,731 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:49,731 - INFO - Actualización de panel: Tarea=Py_INSERT_SQL_SERVER, Estado=running, Duración=0.00s, Hora=2025-05-04 21:45:49.731988, Info=
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:45:49,731 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
"
PS C:\Users\<USER>\OneDrive - kayze\Python> ^C
PS C:\Users\<USER>\OneDrive - kayze\Python>
PS C:\Users\<USER>\OneDrive - kayze\Python>  c:; cd 'c:\Users\<USER>\OneDrive - kayze\Python'; & 'c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe' 'c:\Users\<USER>\.
vscode\extensions\ms-python.debugpy-2025.6.0-win32-x64\bundled\libs\debugpy\launcher' '57450' '--' 'c:\Users\<USER>\OneDrive - kayze\Python\Schedule_ETL_TQW.py'
ADVERTENCIA: La ruta C:\Users\<USER>\OneDrive - kayze\Python no existe. Se usará una ruta alternativa.
ADVERTENCIA: Usando directorio actual como base: c:\Users\<USER>\OneDrive - kayze\Python
ETL TQW Scheduler v2.0
Usuario forzado a: pc
Directorio base: c:\Users\<USER>\OneDrive - kayze\Python
Archivo de configuración: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Archivo de log: c:\Users\<USER>\OneDrive - kayze\Python\logs\scheduler_20250504.log
Usuario actual: pc
Ruta base configurada: c:\Users\<USER>\OneDrive - kayze\Python
Actualizando rutas de configuración...
Verificando rutas de configuración para usuario: pc
Las rutas de configuración ya son correctas o no se pudieron corregir.
2025-05-04 21:46:15,011 - INFO - Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
Intentando cargar configuración desde: c:\Users\<USER>\OneDrive - kayze\Python\scheduler_config.json
2025-05-04 21:46:15,011 - INFO - Archivo de configuración encontrado
2025-05-04 21:46:15,011 - INFO - Configuración cargada: 26 tareas encontradas
2025-05-04 21:46:15,011 - INFO - Tareas cargadas: ReporteOtDigital, PyTOA_Flujo23, PyTOAFinal, CalidadNaranja_11AM, CalidadNaranja_3PM, CalidadNaranja_6PM, CalidadNew23_11AM, CalidadNew23_3PM,
 CalidadNew23_6PM, PyDesafioTecnico, PyDesafioTecnico2, NdcBot, Py_INSERT_SQL_SERVER, PyTOA30, FlujoMysql_i, Turnos_Python, PyLogisticaMat, AnexoLiquidacion, AnexoPDF, ETL_inventarios, PyFormD
eclaMaterial, PDF_Masivo_Inventarios, TQW_CALIDAD_REACTIVA_mysql, CalidadNew23, CalidadNaranja, ImportSQLserve
2025-05-04 21:46:15,011 - INFO - Iniciando programador de tareas - VERSIÓN DIAGNÓSTICO
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Total de tareas cargadas: 26
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Tarea 'ReporteOtDigital' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ReporteOtDigital.py - Existe archivo: True
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Tarea 'PyTOA_Flujo23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA_Flujo23.py - Existe archivo: True
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Tarea 'PyTOAFinal' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOAFinal.py - Existe archivo: True
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:46:15,011 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_11AM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_3PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23_6PM' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PyDesafioTecnico2' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyDesafioTecnico.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'NdcBot' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'Py_INSERT_SQL_SERVER' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PyTOA30' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'FlujoMysql_i' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\FlujoMysql_i.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'Turnos_Python' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyTurnos.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PyLogisticaMat' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyLogisticaMat.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'AnexoLiquidacion' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoLiquidacion.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'AnexoPDF' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\AnexoPDF.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'ETL_inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ETL_inventarios.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PyFormDeclaMaterial' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PyFormDeclaMaterial.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'PDF_Masivo_Inventarios' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\PDF_Masivo_Inventarios.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'TQW_CALIDAD_REACTIVA_mysql' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\TQW_CALIDAD_REACTIVA_mysql.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNew23' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNew23.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'CalidadNaranja' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\CalidadNaranja.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Tarea 'ImportSQLserve' - Ruta: c:\Users\<USER>\OneDrive - kayze\Python\ImportSQLserve.py - Existe archivo: True
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: ReporteOtDigital a las 11:40
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: PyTOA_Flujo23 a las 05:40
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: PyTOAFinal a las 05:30
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNaranja_11AM a las 11:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNaranja_3PM a las 15:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNaranja_6PM a las 18:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNew23_11AM a las 11:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNew23_3PM a las 15:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: CalidadNew23_6PM a las 18:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: PyDesafioTecnico a las 10:00
2025-05-04 21:46:15,027 - INFO - Tarea diaria programada: PyDesafioTecnico2 a las 16:00
2025-05-04 21:46:15,027 - INFO - Programando 6 tareas por intervalo
2025-05-04 21:46:15,027 - INFO - Tareas cada 120 minutos programadas: NdcBot, Py_INSERT_SQL_SERVER (Job ID: 2291927342752)
2025-05-04 21:46:15,027 - INFO - Total de trabajos programados ahora: 12
2025-05-04 21:46:15,027 - WARNING - Corrigiendo estado de tarea PyTOAFinal: estaba 'running' pero no está en la lista de tareas en ejecución
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Ejecutando tarea NdcBot directamente sin usar cola
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea NdcBot
2025-05-04 21:46:15,027 - INFO - ===== INICIO EJECUCIÓN DIRECTA: NdcBot =====
2025-05-04 21:46:15,027 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:46:15,027 - INFO - Actualización de panel: Tarea=NdcBot, Estado=running, Duración=0.00s, Hora=2025-05-04 21:46:15.027432, Info=
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py
2025-05-04 21:46:15,027 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\NdcBot.py"
2025-05-04 21:46:15,048 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 39508
2025-05-04 21:46:16,049 - INFO - ===== RESULTADO: NdcBot - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:46:16,049 - INFO - Actualización de panel: Tarea=NdcBot, Estado=completed, Duración=1.02s, Hora=2025-05-04 21:46:16.049078, Info=
2025-05-04 21:46:16,049 - INFO - DIAGNÓSTICO: Tarea NdcBot eliminada de running_tasks
2025-05-04 21:46:16,049 - INFO - Tarea NdcBot añadida a la cola de ejecución
2025-05-04 21:46:16,050 - INFO - DIAGNÓSTICO: Ejecutando tarea Py_INSERT_SQL_SERVER directamente sin usar cola
2025-05-04 21:46:16,050 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea Py_INSERT_SQL_SERVER
2025-05-04 21:46:16,050 - INFO - ===== INICIO EJECUCIÓN DIRECTA: Py_INSERT_SQL_SERVER =====
2025-05-04 21:46:16,050 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:46:16,051 - INFO - Actualización de panel: Tarea=Py_INSERT_SQL_SERVER, Estado=running, Duración=0.00s, Hora=2025-05-04 21:46:16.050080, Info=
2025-05-04 21:46:16,051 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:46:16,051 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
2025-05-04 21:46:16,051 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\Py_INSERT_SQL_SERVER.py
"
2025-05-04 21:46:16,053 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 39116
2025-05-04 21:46:17,068 - INFO - ===== RESULTADO: Py_INSERT_SQL_SERVER - INICIADO CORRECTAMENTE (1.02 segundos) =====
2025-05-04 21:46:17,068 - INFO - Actualización de panel: Tarea=Py_INSERT_SQL_SERVER, Estado=completed, Duración=1.02s, Hora=2025-05-04 21:46:17.068352, Info=
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Tarea Py_INSERT_SQL_SERVER eliminada de running_tasks
2025-05-04 21:46:17,068 - INFO - Tarea Py_INSERT_SQL_SERVER añadida a la cola de ejecución
2025-05-04 21:46:17,068 - INFO - Tareas cada 30 minutos programadas: PyTOA30, Turnos_Python, PyLogisticaMat (Job ID: 2291927341120)
2025-05-04 21:46:17,068 - INFO - Total de trabajos programados ahora: 13
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Ejecutando tarea PyTOA30 directamente sin usar cola
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Inicio de ejecución directa para tarea PyTOA30
2025-05-04 21:46:17,068 - INFO - ===== INICIO EJECUCIÓN DIRECTA: PyTOA30 =====
2025-05-04 21:46:17,068 - INFO - Ruta del archivo a ejecutar: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:46:17,068 - INFO - Actualización de panel: Tarea=PyTOA30, Estado=running, Duración=0.00s, Hora=2025-05-04 21:46:17.068352, Info=
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Archivo encontrado en ruta original: c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Ejecutando: c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py
2025-05-04 21:46:17,068 - INFO - DIAGNÓSTICO: Comando: "c:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe" "c:\Users\<USER>\OneDrive - kayze\Python\PyTOA30.py"
2025-05-04 21:46:17,179 - INFO - DIAGNÓSTICO: Proceso iniciado con PID: 27088
PS C:\Users\<USER>\OneDrive - kayze\Python>