import time
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np



def buscar_imagen_en_pantalla(imagen_a_buscar):

     # Imprime el nombre del archivo
    print(f"Buscando la imagen: {imagen_a_buscar}")

    # Toma una captura de pantalla
    screenshot = pyautogui.screenshot()
    screenshot_np = np.array(screenshot) 
    screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

    # Lee la imagen a buscar y conviértela a escala de grises
    template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

    w, h = template.shape[::-1]

    # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
    result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
    loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

    # Si encontró la imagen, devuelve las coordenadas del centro
    for pt in zip(*loc[::-1]):
        centro_x = pt[0] + w // 2
        centro_y = pt[1] + h // 2
        return (centro_x, centro_y)
    return None


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@************/master?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()
session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_DIRECTAX"))
session.commit()

list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')
# Crear el diccionario de tipos de datos personalizados
custom_dtypes = {
'Serial': sqlalchemy.types.String(50),
'Item': sqlalchemy.types.String(50),
'Org': sqlalchemy.types.String(50),
'Revision': sqlalchemy.types.String(50),
'Subinventory': sqlalchemy.types.String(50),
'Locator': sqlalchemy.types.String(50),
'Operation': sqlalchemy.types.String(50),
'Job': sqlalchemy.types.String(50),
'Step': sqlalchemy.types.String(50),
'Lot': sqlalchemy.types.String(50),
'State': sqlalchemy.types.String(50),
'Status': sqlalchemy.types.String(50),
'Receipt Date': sqlalchemy.types.String(50),
'Ship Date': sqlalchemy.types.String(50),
'Supplier Name': sqlalchemy.types.String(50),
'Supplier Lot': sqlalchemy.types.String(50),
'Supplier Serial': sqlalchemy.types.String(50),
'Unit Number': sqlalchemy.types.String(50),
'Attributes': sqlalchemy.types.String(50),
'[  ]': sqlalchemy.types.String(50),
'Unnamed: 20': sqlalchemy.types.String(50)
}

# Truncar las cadenas a un máximo de 100 caracteres
#  df_truncated = df_dtype_str.applymap(lambda x: x[:100])
# Insertar el DataFrame en la tabla SQL
df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")

time.sleep(7)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)