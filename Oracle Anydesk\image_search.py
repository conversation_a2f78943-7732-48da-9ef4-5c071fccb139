def buscar_imagen_en_pantalla(imagen_a_buscar):
    import pyautogui
    import cv2
    import numpy as np

    try:
        # Imprime el nombre del archivo
        print(f"Buscando la imagen: {imagen_a_buscar}")

        # Toma una captura de pantalla
        screenshot = pyautogui.screenshot()
        screenshot_np = np.array(screenshot)
        screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

        # Lee la imagen a buscar y conviértela a escala de grises
        template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

        w, h = template.shape[::-1]

        # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
        result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
        loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

        # Si encontró la imagen, devuelve las coordenadas del centro
        for pt in zip(*loc[::-1]):
            centro_x = pt[0] + w // 2
            centro_y = pt[1] + h // 2
            return (centro_x, centro_y)

        # Si no encontró la imagen, verifica si es una de las excepciones
        if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\D32Vina_reversa2.png",
                               "C:\\Users\\<USER>\\Desktop\\D32Vina_reversa.png",
                               "C:\\Users\\<USER>\\Desktop\\ContinueEnd.png"
                               ]:            
            print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
            return None

        return "IMAGE_NOT_FOUND"

    except Exception as e:
        print(f"Ocurrió un error durante la búsqueda de la imagen: {str(e)}")
        return "IMAGE_NOT_FOUND"