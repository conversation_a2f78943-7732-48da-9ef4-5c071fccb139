#!/usr/bin/env python3
"""
Quick verification script to test the UnboundLocalError fix.
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import upload_excel_to_sql_server
import pandas as pd
import tempfile

def test_variable_fix():
    """Test that the sql_columns variable error is fixed."""
    print("🧪 Testing UnboundLocalError fix...")

    # Create a simple test DataFrame
    test_data = {
        'col1': ['test1', 'test2'],
        'col2': ['value1', 'value2'],
        'fecha_integracion': pd.to_datetime(['2023-01-01', '2023-01-02'])
    }
    df = pd.DataFrame(test_data)

    # Create temporary Excel file
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
        df.to_excel(tmp.name, index=False)
        tmp_path = tmp.name

    try:
        # This should not raise UnboundLocalError anymore
        result = upload_excel_to_sql_server(
            excel_file_path=tmp_path,
            table_name='test_table',
            mode='append'
        )

        if result:
            print("✅ Test passed - No UnboundLocalError!")
            return True
        else:
            print("⚠️ Upload failed, but no UnboundLocalError (expected for test DB)")
            return True

    except Exception as e:
        if "UnboundLocalError" in str(e):
            print(f"❌ Test failed - UnboundLocalError still present: {e}")
            return False
        else:
            print(f"⚠️ Different error (expected): {e}")
            return True

    finally:
        # Clean up
        try:
            os.unlink(tmp_path)
        except:
            pass

if __name__ == "__main__":
    print("🚀 Starting UnboundLocalError fix verification...")
    success = test_variable_fix()

    if success:
        print("🎉 Fix verification successful!")
    else:
        print("❌ Fix verification failed!")
        sys.exit(1)