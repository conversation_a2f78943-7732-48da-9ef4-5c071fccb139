#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import re
import logging
import shutil

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('actualizar_script.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def backup_file(file_path):
    """Crea una copia de seguridad del archivo original"""
    backup_path = f"{file_path}.bak"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Backup creado en: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Error al crear backup: {e}")
        return False

def update_deprecated_methods(file_path):
    """Actualiza métodos deprecados de Selenium en el archivo"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Patrones a reemplazar
        replacements = [
            # find_element_by_xpath
            (r'find_element_by_xpath\(([^)]+)\)', r'find_element(By.XPATH, \1)'),
            
            # find_element_by_tag_name
            (r'find_element_by_tag_name\(([^)]+)\)', r'find_element(By.TAG_NAME, \1)'),
            
            # find_element_by_id
            (r'find_element_by_id\(([^)]+)\)', r'find_element(By.ID, \1)'),
            
            # find_element_by_name
            (r'find_element_by_name\(([^)]+)\)', r'find_element(By.NAME, \1)'),
            
            # find_element_by_class_name
            (r'find_element_by_class_name\(([^)]+)\)', r'find_element(By.CLASS_NAME, \1)'),
            
            # find_element_by_css_selector
            (r'find_element_by_css_selector\(([^)]+)\)', r'find_element(By.CSS_SELECTOR, \1)'),
            
            # find_element_by_link_text
            (r'find_element_by_link_text\(([^)]+)\)', r'find_element(By.LINK_TEXT, \1)'),
            
            # find_element_by_partial_link_text
            (r'find_element_by_partial_link_text\(([^)]+)\)', r'find_element(By.PARTIAL_LINK_TEXT, \1)')
        ]
        
        # Asegurar que By está importado
        by_import = 'from selenium.webdriver.common.by import By'
        if by_import not in content:
            content = f"{by_import}\n{content}"
            logger.info("Añadida importación de By")
        
        # Aplicar reemplazos
        modified = content
        for pattern, replacement in replacements:
            modified = re.sub(pattern, replacement, modified)
        
        # Verificar si hubo cambios
        if modified != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified)
            logger.info("Métodos deprecados actualizados exitosamente")
            return True
        else:
            logger.info("No se encontraron métodos deprecados para actualizar")
            return False
            
    except Exception as e:
        logger.error(f"Error al actualizar métodos: {e}")
        return False

def update_set_date_range_function(file_path):
    """Actualiza la función set_date_range con la implementación robusta"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Buscar la función set_date_range
        set_date_range_pattern = r'def set_date_range\(driver, start_date, end_date\):.*?(?=def |$)'
        set_date_range_match = re.search(set_date_range_pattern, content, re.DOTALL)
        
        if not set_date_range_match:
            logger.error("No se encontró la función set_date_range")
            return False
        
        # Nueva implementación
        new_implementation = '''def set_date_range(driver, start_date, end_date):
    try:
        # Importar time y By al inicio de la función para asegurar que están disponibles
        import time
        import os
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        logger.info(f"Estableciendo rango de fechas: {start_date} - {end_date}")
        time.sleep(2)
        
        # Intentar localizar el formulario de fechas primero (puede cambiar el path)
        logger.info("Buscando formulario de fechas...")
        
        # Lista de posibles XPaths para el input de fecha inicial
        start_input_xpaths = [
            "/html/body/div[5]/div[3]/table/tbody/tr[1]/td[2]/input",  # XPath original
            "//*[@id='filtroFecIni']",  # Buscar por ID si existe
            "//input[contains(@name, 'fecIni')]",  # Buscar por nombre parcial
            "//input[contains(@placeholder, 'inicio') or contains(@placeholder, 'Inicio')]", # Buscar por placeholder
            "//table//input[@type='text'][1]", # Primera input de texto en una tabla
            "//form//input[@type='text'][1]" # Primera input de texto en un formulario
        ]
        
        # Intentar cada XPath hasta encontrar uno que funcione
        start_input = None
        for xpath in start_input_xpaths:
            try:
                logger.info(f"Intentando XPath: {xpath}")
                # Esperar explícitamente a que el elemento esté disponible (hasta 5 segundos)
                start_input = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, xpath))
                )
                logger.info(f"Elemento encontrado con XPath: {xpath}")
                break
            except Exception as e:
                logger.info(f"XPath {xpath} no encontró el elemento: {str(e)}")
                continue
        
        if not start_input:
            logger.error("No se pudo encontrar el campo de fecha inicial")
            # Tomar screenshot para diagnosticar
            screenshot_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "error_screenshot.png")
            driver.save_screenshot(screenshot_path)
            logger.info(f"Screenshot guardado en: {screenshot_path}")
            raise Exception("No se pudo encontrar el campo de fecha inicial")
        
        # Proceder con la fecha de inicio
        start_input.clear()
        start_input.send_keys(start_date)
        logger.info("Fecha de inicio establecida")
        
        time.sleep(1)
        
        # Buscar el campo de fecha final relativo al campo de fecha inicial
        try:
            # Intentar varias estrategias para encontrar el segundo campo
            end_input_xpaths = [
                "/html/body/div[5]/div[3]/table/tbody/tr[1]/td[4]/input",  # XPath original
                "//*[@id='filtroFecFin']",  # Buscar por ID si existe
                "//input[contains(@name, 'fecFin')]",  # Buscar por nombre parcial
                "//input[contains(@placeholder, 'fin') or contains(@placeholder, 'Fin')]", # Buscar por placeholder
                "//table//input[@type='text'][2]", # Segunda input de texto en una tabla
                "//form//input[@type='text'][2]" # Segunda input de texto en un formulario
            ]
            
            end_input = None
            for xpath in end_input_xpaths:
                try:
                    end_input = WebDriverWait(driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, xpath))
                    )
                    logger.info(f"Campo de fecha final encontrado con XPath: {xpath}")
                    break
                except:
                    continue
            
            if not end_input:
                # Si no se encuentra, intentar con el segundo input de texto en la página
                inputs = driver.find_elements(By.XPATH, "//input[@type='text']")
                if len(inputs) > 1:
                    end_input = inputs[1]
                    logger.info("Campo de fecha final encontrado usando el segundo input de texto")
                else:
                    raise Exception("No se encontraron suficientes campos de texto")
        except Exception as e:
            logger.error(f"Error buscando campo de fecha final: {str(e)}")
            screenshot_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "error_screenshot2.png")
            driver.save_screenshot(screenshot_path)
            logger.info(f"Screenshot guardado en: {screenshot_path}")
            raise
        
        end_input.clear()
        end_input.send_keys(end_date)
        logger.info("Fecha de fin establecida")
        
    except Exception as e:
        logger.error(f"Error al establecer el rango de fechas: {str(e)}")
        logger.error(traceback.format_exc())
'''
        
        # Reemplazar la función
        modified_content = content.replace(set_date_range_match.group(0), new_implementation)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        logger.info("Función set_date_range actualizada exitosamente")
        return True
        
    except Exception as e:
        logger.error(f"Error al actualizar la función set_date_range: {e}")
        return False

def update_login_code(file_path):
    """Actualiza el código de login para usar métodos modernos de Selenium"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Buscar patrones de código para login
        login_patterns = [
            # Patrón para buscar el bloque de código que maneja el username_field
            (r'try:\s+username_field\s*=\s*driver\.find_element_by_xpath\([^)]+\).*?(?=password_field|try:)', 
             '''
        # Usar método moderno de Selenium (sin métodos deprecados)
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        try:
            # Esperar explícitamente a que el elemento esté disponible
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[1]/td[2]/input"))
            )
            logger.info("Usando WebDriverWait y EC para username")
'''),
            
            # Patrón para buscar el bloque de código que maneja el password_field
            (r'try:\s+password_field\s*=\s*driver\.find_element_by_xpath\([^)]+\).*?(?=login_button|try:)', 
             '''
        # Buscar el campo de password con el método moderno
        password_field = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/table/tbody/tr[3]/td[2]/input"))
        )
'''),
            
            # Patrón para buscar el bloque de código que maneja el login_button
            (r'try:\s+login_button\s*=\s*driver\.find_element_by_xpath\([^)]+\).*?(?=time\.sleep|try:)', 
             '''
        # Buscar el botón de login con el método moderno
        login_button = WebDriverWait(driver, 5).until(
            EC.element_to_be_clickable((By.XPATH, "/html/body/div[2]/table/tbody/tr/td[1]/div[2]/p/span/a"))
        )
''')
        ]
        
        # Aplicar reemplazos si se encuentran
        modified_content = content
        for pattern, replacement in login_patterns:
            match = re.search(pattern, modified_content, re.DOTALL)
            if match:
                modified_content = modified_content.replace(match.group(0), replacement)
                logger.info(f"Reemplazado bloque de código de login")
            else:
                logger.warning(f"No se encontró patrón en el código para reemplazar")
        
        # Guardar los cambios
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
            
        logger.info("Código de login actualizado exitosamente")
        return True
        
    except Exception as e:
        logger.error(f"Error al actualizar código de login: {e}")
        return False

def main():
    """Función principal"""
    logger.info("=== INICIANDO ACTUALIZACIÓN DE NDCBOT.PY ===")
    
    # Ruta al script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, "NdcBot.py")
    
    if not os.path.exists(file_path):
        logger.error(f"No se encontró el archivo: {file_path}")
        return
    
    # Crear backup
    if not backup_file(file_path):
        logger.error("No se pudo crear backup, abortando operación")
        return
    
    # Actualizar métodos deprecados
    logger.info("Actualizando métodos deprecados...")
    update_deprecated_methods(file_path)
    
    # Actualizar función set_date_range
    logger.info("Actualizando función set_date_range...")
    update_set_date_range_function(file_path)
    
    # Actualizar código de login
    logger.info("Actualizando código de login...")
    update_login_code(file_path)
    
    logger.info("=== ACTUALIZACIÓN COMPLETADA ===")
    logger.info("Ejecute NdcBot.py nuevamente para probar los cambios")
    logger.info("Si hay problemas, se restauró una copia de seguridad en NdcBot.py.bak")

if __name__ == "__main__":
    main()