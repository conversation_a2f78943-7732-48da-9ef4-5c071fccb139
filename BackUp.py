import pyodbc
import datetime
import os

# Configuración de la conexión a SQL Server
server = '20.20.20.205'
database = 'master'
username = 'sa'
password = 'N1c0l7as'

# Ruta de la carpeta donde se guardarán los backups
backup_folder = r'C:\Users\<USER>\OneDrive - kayze\TQW PYTHON'

# Generar el nombre del archivo de backup basado en la fecha actual
current_date = datetime.datetime.now().strftime('%Y%m%d')
backup_file = f'{database}_{current_date}.bak'
backup_path = os.path.join(backup_folder, backup_file)

conn = None
cursor = None

try:
    # Establecer la conexión a SQL Server utilizando pyodbc
    conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'
    conn = pyodbc.connect(conn_str, autocommit=True)

    # Crear un cursor para ejecutar comandos SQL
    cursor = conn.cursor()

    # Comando SQL para realizar el backup de la base de datos
    backup_query = f"BACKUP DATABASE {database} TO DISK = '{backup_path}' WITH COMPRESSION, INIT"

    # Ejecutar el comando de backup
    cursor.execute(backup_query)
    print(f"Backup de la base de datos {database} realizado exitosamente en {backup_path}")

except pyodbc.Error as e:
    print(f"Error al realizar el backup: {str(e)}")

finally:
    # Cerrar el cursor y la conexión si se crearon correctamente
    if cursor is not None:
        cursor.close()
    if conn is not None:
        conn.close()