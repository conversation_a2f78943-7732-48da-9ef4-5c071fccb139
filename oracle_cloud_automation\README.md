# Oracle Cloud Data Automation

Script automatizado para extraer datos de Oracle Cloud y cargarlos en SQL Server.

## Estructura del Proyecto

```
oracle_cloud_automation/
├── src/                    # Código fuente principal
│   ├── main.py            # Punto de entrada principal
│   ├── config.py          # Configuración del proyecto
│   ├── web_automation.py  # Lógica de automatización web
│   └── utils/             # Utilidades
│       ├── database.py    # Manejo de base de datos
│       └── file_utils.py  # Utilidades de archivos
├── drivers/               # Drivers de navegadores
│   └── msedgedriver.exe  # Driver de Microsoft Edge
└── logs/                 # Archivos de log generados

```

## Configuración

1. Asegúrate de tener el driver de Edge en la carpeta `drivers/`
2. Configura las credenciales en `src/config.py`
3. Los logs se guardarán en la carpeta `logs/`

## Uso

```bash
# Desde la carpeta raíz del proyecto
python src/main.py
```

## Características

- ✅ Extracción automática de datos de Oracle Cloud
- ✅ Integración con SQL Server
- ✅ Sistema de logging robusto
- ✅ Manejo de errores y reintentos
- ✅ Ciclo continuo con pausa configurable

## Dependencias

- Python 3.7+
- Selenium
- pandas
- pyodbc
