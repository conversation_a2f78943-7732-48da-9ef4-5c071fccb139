from contextlib import contextmanager
import pandas as pd
import mysql.connector
import pyodbc
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Optional

class DatabaseSyncManager:
    def __init__(self, log_file: str = "db_sync.log"):
        try:
            logging.basicConfig(
                filename=log_file,
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            
            # Configuración de conexión MySQL
            self.mysql_config = {
                'host': '**************',
                'user': 'ncornejo',
                'password': 'N1c0l7as17',
                'database': 'operaciones_tqw',
                'port': 3306,
                'raise_on_warnings': True,
                'autocommit': True,
                'pool_size': 5,
                'pool_reset_session': True
            }
            
            # Configuración de conexión SQL Server
            self.sqlserver_config = {
                'driver': 'ODBC Driver 17 for SQL Server',
                'server': '************',
                'database': 'telqway',
                'uid': 'ncornejo',
                'pwd': 'N1c0l7as17',
                'autocommit': True
            }
            
            # Validar conexiones iniciales
            logging.info("Validando conexiones iniciales...")
            print("Validando conexión a MySQL...")
            self.test_mysql_connection()
            print("Validando conexión a SQL Server...")
            self.test_sqlserver_connection()
            
            # Configurar mapeos de tablas
            self.setup_mappings()
            
        except Exception as e:
            logging.error(f"Error en inicialización: {str(e)}")
            raise
    
    def test_mysql_connection(self):
        """Prueba la conexión a MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchall()
            cursor.close()
            conn.close()
            logging.info("Conexión a MySQL validada correctamente")
            print("Conexión a MySQL validada correctamente")
        except Exception as e:
            error_msg = f"Error validando conexión MySQL: {str(e)}"
            logging.error(error_msg)
            print(f"ERROR: {error_msg}")
            raise
    
    def test_sqlserver_connection(self):
        """Prueba la conexión a SQL Server"""
        try:
            conn_str = f"DRIVER={{{self.sqlserver_config['driver']}}};SERVER={self.sqlserver_config['server']};DATABASE={self.sqlserver_config['database']};UID={self.sqlserver_config['uid']};PWD={self.sqlserver_config['pwd']}"
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchall()
            cursor.close()
            conn.close()
            logging.info("Conexión a SQL Server validada correctamente")
            print("Conexión a SQL Server validada correctamente")
        except Exception as e:
            error_msg = f"Error validando conexión SQL Server: {str(e)}"
            logging.error(error_msg)
            print(f"ERROR: {error_msg}")
            raise
            
    def setup_mappings(self):
        """Configura los mapeos de sincronización entre tablas"""
        self.sync_mapping = {
            'TB_TECNICO_RECLAMO': {'target': 'tb_comision_justificaOT', 'direction': 'mysql_to_sql'},
            'TB_SME_FORM_actividad': {
                'target': 'TB_SME_FORM_actividad',
                'direction': 'mysql_to_sql',
                'query': """
                    SELECT ID, NombreTecnico, NombreCoordinador, Fecha, 
                    TIME_FORMAT(HoraInicio, '%H:%i:%s') as HoraInicio, 
                    TIME_FORMAT(HoraTermino, '%H:%i:%s') as HoraTermino, 
                    Actividad, localidad, rut_cliente, zona, direccion, 
                    nombre_cliente, observacion 
                    FROM TB_SME_FORM_actividad
                """
            },
            'tb_user_tqw': {'target': 'tb_user_tqw', 'direction': 'mysql_to_sql'},
            'tb_log_app': {'target': 'TB_LOG_APP', 'direction': 'mysql_to_sql'},
            'TB_CLAVES_USUARIOS': {'target': 'tp_usuarioPass', 'direction': 'mysql_to_sql'},
            'TB_SOPORTE_CIERRE_ASEGURADO': {'target': 'TB_SOPORTE_CIERRE_ASEGURADO', 'direction': 'mysql_to_sql'},
            'tb_solicitud_excep_cal': {'target': 'tb_solicitud_excepcion', 'direction': 'mysql_to_sql'},
            'TB_LOGIST_FORM_JUSTIF': {'target': 'TB_LOGIST_FORM_JUSTIF', 'direction': 'mysql_to_sql'},
            'TP_MODELO_SENI_JUNI': {'target': 'Empleados', 'direction': 'mysql_to_sql'},
            'tp_ptos_23_new': {'target': 'TP_PTOS_23_NEW', 'direction': 'mysql_to_sql'}
        }

    @contextmanager
    def get_mysql_connection(self):
        """Crea una conexión a MySQL usando el driver nativo"""
        connection = None
        cursor = None
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor(dictionary=True)
            yield connection, cursor
        except Exception as e:
            logging.error(f"Error en conexión MySQL: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    @contextmanager
    def get_sqlserver_connection(self):
        """Crea una conexión a SQL Server usando pyodbc"""
        connection = None
        cursor = None
        try:
            conn_str = f"DRIVER={{{self.sqlserver_config['driver']}}};SERVER={self.sqlserver_config['server']};DATABASE={self.sqlserver_config['database']};UID={self.sqlserver_config['uid']};PWD={self.sqlserver_config['pwd']}"
            connection = pyodbc.connect(conn_str)
            cursor = connection.cursor()
            yield connection, cursor
        except Exception as e:
            logging.error(f"Error en conexión SQL Server: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def sync_table(self, source_table: str) -> None:
        try:
            mapping = self.sync_mapping.get(source_table)
            if not mapping:
                logging.warning(f"No se encontró configuración para la tabla {source_table}")
                return

            is_mysql_to_sql = mapping['direction'] == 'mysql_to_sql'
            query = mapping.get('query', f"SELECT * FROM {source_table}")
            target_table = mapping['target']
            
            logging.info(f"Iniciando sincronización: {source_table} -> {target_table}")
            
            # Obtener datos de la fuente
            if is_mysql_to_sql:
                # Leer desde MySQL
                with self.get_mysql_connection() as (conn, cursor):
                    logging.info(f"Ejecutando consulta en MySQL: {query}")
                    cursor.execute(query)
                    rows = cursor.fetchall()
                    
                    if not rows:
                        logging.warning(f"No se encontraron datos en tabla fuente {source_table}")
                        return
                        
                    # Convertir a DataFrame
                    df = pd.DataFrame(rows)
                    logging.info(f"Datos obtenidos de MySQL: {len(df)} filas")
                    
                    # Limpiar datos
                    df = df.replace({np.nan: None})
                    
                    # Insertar en SQL Server
                    with self.get_sqlserver_connection() as (target_conn, target_cursor):
                        # Truncar tabla destino
                        logging.info(f"Truncando tabla SQL Server: {target_table}")
                        target_cursor.execute(f"IF OBJECT_ID('{target_table}', 'U') IS NOT NULL TRUNCATE TABLE {target_table}")
                        target_conn.commit()
                        
                        # Preparar inserción
                        if len(df) > 0:
                            # Obtener columnas
                            columns = df.columns.tolist()
                            placeholders = ", ".join(["?"] * len(columns))
                            columns_str = ", ".join([f"[{col}]" for col in columns])
                            
                            # Inserción por lotes
                            insert_query = f"INSERT INTO {target_table} ({columns_str}) VALUES ({placeholders})"
                            batch_size = 1000
                            
                            for i in range(0, len(df), batch_size):
                                batch_end = min(i + batch_size, len(df))
                                batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
                                
                                target_cursor.executemany(insert_query, batch_data)
                                target_conn.commit()
                                logging.info(f"Insertado lote {i//batch_size + 1}: {len(batch_data)} filas")
            else:
                # Leer desde SQL Server
                with self.get_sqlserver_connection() as (conn, cursor):
                    logging.info(f"Ejecutando consulta en SQL Server: {query}")
                    cursor.execute(query)
                    rows = cursor.fetchall()
                    
                    if not rows:
                        logging.warning(f"No se encontraron datos en tabla fuente {source_table}")
                        return
                        
                    # Convertir a DataFrame - SQL Server no devuelve diccionarios, sino tuplas
                    columns = [column[0] for column in cursor.description]
                    df = pd.DataFrame.from_records(rows, columns=columns)
                    logging.info(f"Datos obtenidos de SQL Server: {len(df)} filas")
                    
                    # Limpiar datos
                    df = df.replace({np.nan: None})
                    
                    # Insertar en MySQL
                    with self.get_mysql_connection() as (target_conn, target_cursor):
                        # Truncar tabla destino
                        logging.info(f"Truncando tabla MySQL: {target_table}")
                        target_cursor.execute(f"TRUNCATE TABLE {target_table}")
                        target_conn.commit()
                        
                        # Preparar inserción
                        if len(df) > 0:
                            # Obtener columnas
                            columns = df.columns.tolist()
                            placeholders = ", ".join(["%s"] * len(columns))
                            columns_str = ", ".join([f"`{col}`" for col in columns])
                            
                            # Inserción por lotes
                            insert_query = f"INSERT INTO {target_table} ({columns_str}) VALUES ({placeholders})"
                            batch_size = 1000
                            
                            for i in range(0, len(df), batch_size):
                                batch_end = min(i + batch_size, len(df))
                                batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
                                
                                target_cursor.executemany(insert_query, batch_data)
                                target_conn.commit()
                                logging.info(f"Insertado lote {i//batch_size + 1}: {len(batch_data)} filas")
                    
            logging.info(f"Sincronización exitosa: {source_table} -> {mapping['target']}")
            
        except Exception as e:
            logging.error(f"Error sincronizando {source_table}: {str(e)}")
            # Levantar la excepción para que sea manejada por sync_all_tables
            raise
    
    def sync_all_tables(self) -> None:
        start_time = datetime.now()
        logging.info("Iniciando sincronización general")
        
        for source_table in self.sync_mapping.keys():
            try:
                self.sync_table(source_table)
            except Exception as e:
                logging.error(f"Error en tabla {source_table}: {str(e)}")
                # Continuar con la siguiente tabla en lugar de detenerse
                continue
                
        logging.info(f"Sincronización completa. Tiempo total: {datetime.now() - start_time}")
    
    def cleanup(self) -> None:
        try:
            # No es necesario disponer conexiones ya que están manejadas con context managers
            logging.info("Limpieza completada")
        except Exception as e:
            logging.error(f"Error en limpieza: {str(e)}")

def main():
    try:
        print("Iniciando sincronización de bases de datos...")
        sync_manager = DatabaseSyncManager()
        print("Conexiones establecidas correctamente")
        print("Iniciando sincronización de tablas...")
        sync_manager.sync_all_tables()
        print("Sincronización completada con éxito")
    except Exception as e:
        print(f"ERROR: {str(e)}")
        logging.error(f"Error en la ejecución principal: {str(e)}")
    finally:
        try:
            if 'sync_manager' in locals():
                sync_manager.cleanup()
            print("Proceso finalizado")
        except Exception as e:
            print(f"Error en limpieza final: {str(e)}")

if __name__ == "__main__":
    main()
