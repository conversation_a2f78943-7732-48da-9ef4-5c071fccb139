{"tasks": {"daily": {"ReporteOtDigital": {"name": "ReporteOtDigital", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\ReporteOtDigital.py", "status": "pending", "success_count": 12, "failure_count": 1, "max_runtime": 300, "priority": 1, "retries": 2, "execution_time": "11:40"}, "PyTOA_Flujo23": {"name": "PyTOA_Flujo23", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyTOA_Flujo23.py", "status": "pending", "success_count": 15, "failure_count": 0, "max_runtime": 300, "priority": 1, "retries": 2, "execution_time": "05:40"}, "PyTOAFinal": {"name": "PyTOAFinal", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyTOAFinal.py", "status": "running", "success_count": 15, "failure_count": 0, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-05-04T00:28:33.574568", "execution_time": "05:30"}, "CalidadNaranja_11AM": {"name": "CalidadNaranja_11AM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNaranja.py", "status": "pending", "success_count": 14, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "11:00"}, "CalidadNaranja_3PM": {"name": "CalidadNaranja_3PM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNaranja.py", "status": "pending", "success_count": 15, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "15:00"}, "CalidadNaranja_6PM": {"name": "CalidadNaranja_6PM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNaranja.py", "status": "pending", "success_count": 15, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "18:00"}, "CalidadNew23_11AM": {"name": "CalidadNew23_11AM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNew23.py", "status": "pending", "success_count": 14, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "11:00"}, "CalidadNew23_3PM": {"name": "CalidadNew23_3PM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNew23.py", "status": "pending", "success_count": 14, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "15:00"}, "CalidadNew23_6PM": {"name": "CalidadNew23_6PM", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNew23.py", "status": "pending", "success_count": 15, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2, "execution_time": "18:00"}, "PyDesafioTecnico": {"name": "PyDesafioTecnico", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyDesafioTecnico.py", "status": "pending", "success_count": 14, "failure_count": 1, "max_runtime": 300, "priority": 3, "retries": 2, "execution_time": "10:00"}, "PyDesafioTecnico2": {"name": "PyDesafioTecnico2", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyDesafioTecnico.py", "status": "pending", "success_count": 14, "failure_count": 1, "max_runtime": 300, "priority": 3, "retries": 2, "execution_time": "16:00"}}, "interval": {"NdcBot": {"name": "NdcBot", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\NdcBot.py", "status": "pending", "success_count": 87, "failure_count": 2, "max_runtime": 300, "priority": 1, "retries": 2, "interval": 120}, "Py_INSERT_SQL_SERVER": {"name": "Py_INSERT_SQL_SERVER", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\Py_INSERT_SQL_SERVER.py", "status": "pending", "success_count": 89, "failure_count": 0, "max_runtime": 300, "priority": 1, "retries": 2, "interval": 120}, "PyTOA30": {"name": "PyTOA30", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyTOA30.py", "status": "completed", "success_count": 302, "failure_count": 5, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-05-05T00:22:37.702527", "last_duration": 2.038301, "interval": 30}, "FlujoMysql_i": {"name": "FlujoMysql_i", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\FlujoMysql_i.py", "status": "completed", "success_count": 322, "failure_count": 6, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-05-05T00:43:41.246320", "last_duration": 2.027124, "interval": 30}, "Turnos_Python": {"name": "Turnos_Python", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyTurnos.py", "status": "completed", "success_count": 305, "failure_count": 3, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-05-05T00:22:37.718066", "last_duration": 2.022762, "interval": 30}, "PyLogisticaMat": {"name": "PyLogisticaMat", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyLogisticaMat.py", "status": "completed", "success_count": 303, "failure_count": 4, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-05-05T00:22:37.733592", "last_duration": 2.041284, "interval": 30}}, "manual": {"AnexoLiquidacion": {"name": "AnexoLiquidacion", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\AnexoLiquidacion.py", "status": "pending", "success_count": 8, "failure_count": 1, "max_runtime": 600, "priority": 2, "retries": 1}, "AnexoPDF": {"name": "AnexoPDF", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\AnexoPDF.py", "status": "pending", "success_count": 7, "failure_count": 0, "max_runtime": 600, "priority": 2, "retries": 1}, "ETL_inventarios": {"name": "ETL_inventarios", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\ETL_inventarios.py", "status": "pending", "success_count": 12, "failure_count": 2, "max_runtime": 900, "priority": 1, "retries": 2}, "PyFormDeclaMaterial": {"name": "PyFormDeclaMaterial", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PyFormDeclaMaterial.py", "status": "pending", "success_count": 5, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 1}, "PDF_Masivo_Inventarios": {"name": "PDF_Masivo_Inventarios", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\PDF_Masivo_Inventarios.py", "status": "pending", "success_count": 4, "failure_count": 1, "max_runtime": 1200, "priority": 3, "retries": 1}, "TQW_CALIDAD_REACTIVA_mysql": {"name": "TQW_CALIDAD_REACTIVA_mysql", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\TQW_CALIDAD_REACTIVA_mysql.py", "status": "pending", "success_count": 10, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2}, "CalidadNew23": {"name": "CalidadNew23", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNew23.py", "status": "pending", "success_count": 9, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2}, "CalidadNaranja": {"name": "CalidadNaranja", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\CalidadNaranja.py", "status": "pending", "success_count": 9, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2}, "ImportSQLserve": {"name": "ImportSQLserve", "path": "c:\\Users\\<USER>\\OneDrive - kayze\\Python\\ImportSQLserve.py", "status": "pending", "success_count": 14, "failure_count": 2, "max_runtime": 600, "priority": 1, "retries": 2}}}}