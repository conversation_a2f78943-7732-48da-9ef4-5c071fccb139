# -*- coding: utf-8 -*-
from contextlib import contextmanager
import pandas as pd
import mysql.connector
import pyodbc
import numpy as np
from datetime import datetime
import logging
import requests
from io import StringIO
from typing import Dict, List, Optional

class DatabaseSyncManager:
    def __init__(self, log_file: str = "db_sync.log"):
        try:
            logging.basicConfig(
                filename=log_file,
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            
            # Configuracion de conexion MySQL
            self.mysql_config = {
                'host': '**************',
                'user': 'ncornejo',
                'password': 'N1c0l7as17',
                'database': 'operaciones_tqw',
                'port': 3306,
                'raise_on_warnings': True,
                'autocommit': True,
                'pool_size': 5,
                'pool_reset_session': True
            }
            
            # Configuracion de conexion SQL Server
            self.sqlserver_config = {
                'driver': 'ODBC Driver 17 for SQL Server',
                'server': '************',
                'database': 'telqway',
                'uid': 'ncornejo',
                'pwd': 'N1c0l7as17',
                'autocommit': True
            }
            
            # Validar conexiones iniciales
            logging.info("Validando conexiones iniciales...")
            print("Validando conexi�n a MySQL...")
            self.test_mysql_connection()
            print("Validando conexi�n a SQL Server...")
            self.test_sqlserver_connection()
            
            # Configurar mapeos de tablas
            self.setup_mappings()
            
        except Exception as e:
            logging.error(f"Error en inicializaci�n: {str(e)}")
            print(f"ERROR de inicializaci�n: {str(e)}")
            raise
    
    def test_mysql_connection(self):
        """Prueba la conexi�n a MySQL"""
        try:
            conn = mysql.connector.connect(**self.mysql_config)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchall()
            cursor.close()
            conn.close()
            logging.info("Conexi�n a MySQL validada correctamente")
            print("Conexi�n a MySQL validada correctamente")
        except Exception as e:
            error_msg = f"Error validando conexi�n MySQL: {str(e)}"
            logging.error(error_msg)
            print(f"ERROR: {error_msg}")
            raise
    
    def test_sqlserver_connection(self):
        """Prueba la conexi�n a SQL Server"""
        try:
            conn_str = f"DRIVER={{{self.sqlserver_config['driver']}}};SERVER={self.sqlserver_config['server']};DATABASE={self.sqlserver_config['database']};UID={self.sqlserver_config['uid']};PWD={self.sqlserver_config['pwd']}"
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchall()
            cursor.close()
            conn.close()
            logging.info("Conexi�n a SQL Server validada correctamente")
            print("Conexi�n a SQL Server validada correctamente")
        except Exception as e:
            error_msg = f"Error validando conexi�n SQL Server: {str(e)}"
            logging.error(error_msg)
            print(f"ERROR: {error_msg}")
            raise
            
    def setup_mappings(self):
        """Configura los mapeos de sincronización entre tablas"""
        self.sync_mapping = {
            'TB_TECNICO_RECLAMO': {'target': 'tb_comision_justificaOT', 'direction': 'mysql_to_sql'},
            'TB_SME_FORM_actividad': {
                'target': 'TB_SME_FORM_actividad',
                'direction': 'mysql_to_sql',
                'query': """
                    SELECT 
                        ID, 
                        NombreTecnico, 
                        NombreCoordinador, 
                        Fecha, 
                        TIME_FORMAT(SEC_TO_TIME(HoraInicio/1000000000), '%H:%i:%s') as HoraInicio, 
                        TIME_FORMAT(SEC_TO_TIME(HoraTermino/1000000000), '%H:%i:%s') as HoraTermino, 
                        Actividad, 
                        localidad, 
                        rut_cliente, 
                        zona, 
                        direccion, 
                        nombre_cliente, 
                        observacion 
                    FROM TB_SME_FORM_actividad
                """
            },
            'tb_tecnico_kpi_documentos': {
                'target': 'tb_tecnico_kpi_documentos', 
                'direction': 'mysql_to_sql',
                'query': """
                    SELECT 
                        fecha,
                        rut,
                        nombre_tecnico,
                        tipo,
                        sub_tipo,
                        cantidad,
                        monto
                    FROM tb_tecnico_kpi_documentos
                """
            },
            'tb_user_tqw': {'target': 'tb_user_tqw', 'direction': 'mysql_to_sql'},
            'tb_log_app': {'target': 'TB_LOG_APP', 'direction': 'mysql_to_sql'},
            'TB_CLAVES_USUARIOS': {'target': 'tp_usuarioPass', 'direction': 'mysql_to_sql'},
            'TB_SOPORTE_CIERRE_ASEGURADO': {'target': 'TB_SOPORTE_CIERRE_ASEGURADO', 'direction': 'mysql_to_sql'},
            'tb_solicitud_excep_cal': {'target': 'tb_solicitud_excepcion', 'direction': 'mysql_to_sql'},
            'TB_LOGIST_FORM_JUSTIF': {'target': 'TB_LOGIST_FORM_JUSTIF', 'direction': 'mysql_to_sql'},
            'TP_MODELO_SENI_JUNI': {'target': 'Empleados', 'direction': 'mysql_to_sql'},
            'tp_ptos_23_new': {'target': 'TP_PTOS_23_NEW', 'direction': 'mysql_to_sql'},
            'hcmfront_doc_pendientes': {
                'target': 'tb_hcmfront_doc_pendientes',
                'source': 'csv',
                'url': 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSwTSgYUrKPTj3fdsz8R3-Rfhm19vtfEzvZz8aDHNzLOoN8okJKSmz8KYiRtewLfNwieiP_C2EGBXBF/pub?gid=0&single=true&output=csv'
            },
            'tb_auditorias_terreno': {
                'target': 'tb_auditorias_terreno',
                'direction': 'mysql_to_sql',
                'script': 'PyAuditoriaTerreno.py',
                'schedule': ['09:00', '14:00', '19:00']
            }
        }

    @contextmanager
    def get_mysql_connection(self):
        """Crea una conexi�n a MySQL usando el driver nativo"""
        connection = None
        cursor = None
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor(dictionary=True)
            yield connection, cursor
        except Exception as e:
            logging.error(f"Error en conexi�n MySQL: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    @contextmanager
    def get_sqlserver_connection(self):
        """Crea una conexi�n a SQL Server usando pyodbc"""
        connection = None
        cursor = None
        try:
            conn_str = f"DRIVER={{{self.sqlserver_config['driver']}}};SERVER={self.sqlserver_config['server']};DATABASE={self.sqlserver_config['database']};UID={self.sqlserver_config['uid']};PWD={self.sqlserver_config['pwd']}"
            connection = pyodbc.connect(conn_str)
            cursor = connection.cursor()
            yield connection, cursor
        except Exception as e:
            logging.error(f"Error en conexi�n SQL Server: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

    def _sync_from_csv(self, mapping: dict) -> None:
        """Sincroniza datos desde un CSV a la tabla MySQL especificada"""
        url = mapping['url']
        target_table = mapping['target']
        
        try:
            logging.info(f"Descargando CSV para {target_table}")
            print(f"Descargando datos CSV desde Google Sheets...")
            
            # Usar el método directo como en PyTurnos.py
            try:
                # Método directo y simple
                df = pd.read_csv(url)
                print(f"Datos descargados correctamente. {len(df)} filas obtenidas.")
            except Exception as e:
                logging.error(f"Error al descargar CSV desde {url}: {str(e)}")
                print(f"Error al descargar CSV desde {url}: {str(e)}")
                raise
                
            if df.empty:
                logging.warning(f"El CSV descargado está vacío: {url}")
                print("El CSV descargado está vacío.")
                return

            # Reemplazar NaN por None para compatibilidad con MySQL
            print("Limpiando datos...")
            df = df.replace({np.nan: None})

            # MySQL connection directa como en PyTurnos.py
            print("Conectando a MySQL...")
            mysql_conn = mysql.connector.connect(**self.mysql_config)
            mysql_cursor = mysql_conn.cursor()
            
            print(f"Truncando tabla MySQL {target_table}...")
            try:
                mysql_cursor.execute(f"TRUNCATE TABLE {target_table}")
                mysql_conn.commit()
            except Exception as e:
                # Si la tabla no existe, intentar crearla
                if "doesn't exist" in str(e):
                    print(f"La tabla {target_table} no existe, creándola...")
                    # Crear la tabla basada en las columnas del DataFrame
                    create_table_sql = f"CREATE TABLE {target_table} ("
                    
                    # Mapear tipos de datos de pandas a MySQL
                    type_mapping = {
                        'int64': 'BIGINT',
                        'float64': 'DOUBLE',
                        'object': 'TEXT',
                        'bool': 'BOOLEAN',
                        'datetime64[ns]': 'DATETIME',
                    }
                    
                    # Agregar columnas
                    columns = []
                    for col, dtype in df.dtypes.items():
                        mysql_type = type_mapping.get(str(dtype), 'TEXT')
                        columns.append(f"`{col}` {mysql_type}")
                    
                    create_table_sql += ", ".join(columns)
                    create_table_sql += ")"
                    
                    mysql_cursor.execute(create_table_sql)
                    mysql_conn.commit()
                    print(f"Tabla {target_table} creada exitosamente")
                else:
                    logging.error(f"Error al truncar tabla {target_table}: {str(e)}")
                    print(f"Error al truncar tabla {target_table}: {str(e)}")
                    mysql_cursor.close()
                    mysql_conn.close()
                    raise
            
            print(f"Insertando datos en tabla MySQL {target_table}...")
            # Convertir DataFrame a lista de tuplas para inserción
            cols = df.columns.tolist()
            placeholders = ", ".join(["%s"] * len(cols))
            columns_str = ", ".join([f"`{col}`" for col in cols])
            
            # Preparar consulta INSERT
            mysql_insert_query = f"INSERT INTO {target_table} ({columns_str}) VALUES ({placeholders})"
            
            # Insertar por lotes para mejorar rendimiento
            batch_size = 1000
            total_records = len(df)
            
            try:
                for i in range(0, total_records, batch_size):
                    batch_end = min(i + batch_size, total_records)
                    # Convertir los valores a lista de tuplas, reemplazando NaN por None
                    batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
                    mysql_cursor.executemany(mysql_insert_query, batch_data)
                    mysql_conn.commit()
                    print(f"Insertados {batch_end}/{total_records} registros en MySQL...")
            except Exception as e:
                logging.error(f"Error al insertar en MySQL: {str(e)}")
                print(f"Error al insertar en MySQL: {str(e)}")
                # Mostrar una muestra de los datos para diagnóstico
                print("Muestra de datos que causaron el error:")
                sample = df.iloc[i:i+5] if i < len(df) else df.iloc[-5:]
                print(sample)
                mysql_cursor.close()
                mysql_conn.close()
                raise
            
            mysql_cursor.close()
            mysql_conn.close()
            print(f"Datos insertados correctamente en MySQL. Total: {total_records} registros.")
            logging.info(f"Sincronización CSV -> MySQL exitosa: {target_table}")
        except Exception as e:
            logging.error(f"Error sincronizando CSV a {target_table}: {str(e)}")
            raise

    def sync_table(self, source_table: str) -> None:
        try:
            mapping = self.sync_mapping.get(source_table)
            if not mapping:
                logging.warning(f"No se encontró configuración para la tabla {source_table}")
                return

            # Manejar origen CSV
            if mapping.get('source') == 'csv':
                self._sync_from_csv(mapping)
                return
                
            # Manejar scripts externos (como PyAuditoriaTerreno.py)
            if mapping.get('script'):
                script_path = mapping.get('script')
                logging.info(f"Ejecutando script externo: {script_path}")
                print(f"------------------------------")
                print(f"EJECUTANDO SCRIPT EXTERNO: {script_path}")
                print(f"------------------------------")
                
                import subprocess
                import sys
                import os
                
                # Obtener el directorio actual para asegurar que el script se ejecute desde la ubicación correcta
                current_dir = os.path.dirname(os.path.abspath(__file__))
                script_full_path = os.path.join(current_dir, script_path)
                
                if not os.path.exists(script_full_path):
                    error_msg = f"Error: El script {script_path} no existe en la ruta {current_dir}"
                    logging.error(error_msg)
                    print(error_msg)
                    return
                    
                try:
                    # Ejecutar el script Python como un proceso separado
                    result = subprocess.run(
                        [sys.executable, script_full_path],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    
                    # Registrar la salida del script
                    logging.info(f"Script {script_path} ejecutado con éxito")
                    logging.info(f"Salida del script: {result.stdout}")
                    print(f"Script {script_path} ejecutado con éxito")
                    
                    if result.stderr:
                        logging.warning(f"Mensajes de error del script: {result.stderr}")
                        print(f"Advertencias del script: {result.stderr}")
                        
                except subprocess.CalledProcessError as e:
                    error_msg = f"Error al ejecutar el script {script_path}: {str(e)}"
                    logging.error(error_msg)
                    logging.error(f"Salida de error: {e.stderr}")
                    print(error_msg)
                    raise
                    
                return
                
            is_mysql_to_sql = mapping['direction'] == 'mysql_to_sql'
            direction_str = "MySQL → SQL Server" if is_mysql_to_sql else "SQL Server → MySQL"
            
            query = mapping.get('query', f"SELECT * FROM {source_table}")
                
            target_table = mapping['target']
            
            print(f"------------------------------")
            print(f"INICIANDO SINCRONIZACIÓN: {source_table} → {target_table}")
            print(f"Dirección: {direction_str}")
            print(f"------------------------------")
            
            logging.info(f"Iniciando sincronizaci�n: {source_table} -> {target_table}")
            
            # Obtener datos de la fuente
            if is_mysql_to_sql:
                # Leer desde MySQL
                with self.get_mysql_connection() as (conn, cursor):
                    logging.info(f"Ejecutando consulta en MySQL: {query}")
                    cursor.execute(query)
                    rows = cursor.fetchall()
                    
                    if not rows:
                        logging.warning(f"No se encontraron datos en tabla fuente {source_table}")
                        return
                        
                    # Convertir a DataFrame
                    df = pd.DataFrame(rows)
                    logging.info(f"Datos obtenidos de MySQL: {len(df)} filas")
                    
                    # Limpiar datos
                    df = df.replace({np.nan: None})
                    
                    # Insertar en SQL Server
                    with self.get_sqlserver_connection() as (target_conn, target_cursor):
                        # Truncar tabla destino
                        logging.info(f"Truncando tabla SQL Server: {target_table}")
                        target_cursor.execute(f"IF OBJECT_ID('{target_table}', 'U') IS NOT NULL TRUNCATE TABLE {target_table}")
                        target_conn.commit()
                        
                        # Preparar inserci�n
                        if len(df) > 0:
                            # Obtener columnas
                            columns = df.columns.tolist()
                            placeholders = ", ".join(["?"] * len(columns))
                            columns_str = ", ".join([f"[{col}]" for col in columns])
                            
                            # Inserci�n por lotes
                            insert_query = f"INSERT INTO {target_table} ({columns_str}) VALUES ({placeholders})"
                            batch_size = 1000
                            
                            for i in range(0, len(df), batch_size):
                                batch_end = min(i + batch_size, len(df))
                                batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
                                
                                try:
                                    target_cursor.executemany(insert_query, batch_data)
                                    target_conn.commit()
                                    logging.info(f"Insertado lote {i//batch_size + 1}: {len(batch_data)} filas")
                                except Exception as insert_err:
                                    logging.error(f"Error al insertar lote en {target_table}: {str(insert_err)}")
                                    print(f"Error al insertar lote en {target_table}: {str(insert_err)}")
                                    # Mostrar muestra de datos problemáticos para diagnóstico
                                    if i < len(df):
                                        sample_data = df.iloc[i:i+5] if i+5 < len(df) else df.iloc[i:]
                                        print(f"Muestra de datos problemáticos: {sample_data}")
                                    raise
            else:
                # Leer desde SQL Server
                with self.get_sqlserver_connection() as (conn, cursor):
                    logging.info(f"Ejecutando consulta en SQL Server: {query}")
                    print(f"Ejecutando consulta en SQL Server: {query}")
                    try:
                        cursor.execute(query)
                        rows = cursor.fetchall()
                        
                        if not rows:
                            print(f"ADVERTENCIA: No se encontraron datos en tabla/vista fuente {source_table}")
                            logging.warning(f"No se encontraron datos en tabla fuente {source_table}")
                            return
                            
                        # Convertir a DataFrame - SQL Server no devuelve diccionarios, sino tuplas
                        columns = [column[0] for column in cursor.description]
                        print(f"Columnas obtenidas de SQL Server: {columns}")
                        df = pd.DataFrame.from_records(rows, columns=columns)
                        logging.info(f"Datos obtenidos de SQL Server: {len(df)} filas")
                        print(f"Datos obtenidos de SQL Server: {len(df)} filas")
                        
                        # Mostrar información detallada para diagnóstico
                        print(f"Tipos de datos detectados: {df.dtypes.to_dict()}")
                        print(f"Primeras 5 filas de datos:")
                        print(df.head(5))
                    except Exception as query_err:
                        error_msg = f"Error al ejecutar consulta en SQL Server: {str(query_err)}"
                        print(error_msg)
                        logging.error(error_msg)
                        raise
                    
                    # Limpiar datos
                    df = df.replace({np.nan: None})
                    
                    # Insertar en MySQL
                    with self.get_mysql_connection() as (target_conn, target_cursor):
                        # Verificar si la tabla existe
                        target_cursor.execute(f"""
                            SELECT COUNT(*)
                            FROM information_schema.tables 
                            WHERE table_schema = DATABASE() 
                            AND table_name = '{target_table}'
                        """)
                        table_exists = target_cursor.fetchone()[0] > 0
                        
                        if table_exists:
                            # Si la tabla existe, truncarla
                            logging.info(f"Truncando tabla MySQL: {target_table}")
                            target_cursor.execute(f"TRUNCATE TABLE {target_table}")
                            target_conn.commit()
                        else:
                            # Si la tabla no existe, crearla basada en el DataFrame
                            logging.info(f"La tabla {target_table} no existe, creándola...")
                            create_table_sql = f"CREATE TABLE {target_table} ("
                            
                            # Mapear tipos de datos de pandas a MySQL
                            type_mapping = {
                                'int64': 'BIGINT',
                                'float64': 'DOUBLE',
                                'object': 'TEXT',
                                'bool': 'BOOLEAN',
                                'datetime64[ns]': 'DATETIME',
                            }
                            
                            # Agregar columnas
                            column_parts = []
                            for col, dtype in df.dtypes.items():
                                mysql_type = type_mapping.get(str(dtype), 'TEXT')
                                column_parts.append(f"`{col}` {mysql_type}")
                            
                            create_table_sql += ", ".join(column_parts)
                            create_table_sql += ")"
                            
                            try:
                                print(f"Ejecutando creación de tabla: {create_table_sql}")
                                target_cursor.execute(create_table_sql)
                                target_conn.commit()
                                logging.info(f"Tabla {target_table} creada exitosamente")
                                print(f"Tabla {target_table} creada exitosamente")
                            except Exception as create_err:
                                logging.error(f"Error al crear la tabla {target_table}: {str(create_err)}")
                                print(f"Error al crear la tabla {target_table}: {str(create_err)}")
                                raise
                        
                        # Preparar inserción
                        if len(df) > 0:
                            # Obtener columnas
                            columns = df.columns.tolist()
                            placeholders = ", ".join(["%s"] * len(columns))
                            columns_str = ", ".join([f"`{col}`" for col in columns])
                            
                            # Imprimir información para diagnóstico
                            print(f"Estructura de la tabla {target_table}:")
                            print(f"- Columnas: {columns}")
                            print(f"- Tipos de datos: {dict(zip(columns, df.dtypes.astype(str).tolist()))}")
                            
                            # Inserci�n por lotes
                            insert_query = f"INSERT INTO {target_table} ({columns_str}) VALUES ({placeholders})"
                            batch_size = 1000
                            
                            for i in range(0, len(df), batch_size):
                                batch_end = min(i + batch_size, len(df))
                                batch_data = [tuple(None if pd.isna(x) else x for x in row) for row in df.iloc[i:batch_end].values]
                                
                                try:
                                    target_cursor.executemany(insert_query, batch_data)
                                    target_conn.commit()
                                    logging.info(f"Insertado lote {i//batch_size + 1}: {len(batch_data)} filas")
                                except Exception as insert_err:
                                    logging.error(f"Error al insertar lote en {target_table}: {str(insert_err)}")
                                    print(f"Error al insertar lote en {target_table}: {str(insert_err)}")
                                    # Mostrar muestra de datos problemáticos para diagnóstico
                                    if i < len(df):
                                        sample_data = df.iloc[i:i+5] if i+5 < len(df) else df.iloc[i:]
                                        print(f"Muestra de datos problemáticos: {sample_data}")
                                    raise
                    
            logging.info(f"Sincronizaci�n exitosa: {source_table} -> {mapping['target']}")
            
        except Exception as e:
            logging.error(f"Error sincronizando {source_table}: {str(e)}")
            # Levantar la excepci�n para que sea manejada por sync_all_tables
            raise


    def sync_all_tables(self) -> None:
        start_time = datetime.now()
        logging.info("Iniciando sincronización general")
        
        for source_table in self.sync_mapping.keys():
            try:
                print(f"Sincronizando tabla: {source_table}")
                mapping = self.sync_mapping.get(source_table)
                direction = mapping.get('direction', 'unknown')
                print(f"Dirección de sincronización: {direction}")
                
                # Verificar si es un script programado
                if mapping.get('script') and mapping.get('schedule'):
                    current_time = datetime.now().strftime('%H:%M')
                    schedule_times = mapping.get('schedule')
                    
                    # Verificar si la hora actual está dentro del rango de tolerancia para alguna hora programada
                    should_run = False
                    for scheduled_time in schedule_times:
                        # Convertir a objetos datetime para comparación con tolerancia
                        scheduled_dt = datetime.strptime(scheduled_time, '%H:%M')
                        current_dt = datetime.strptime(current_time, '%H:%M')
                        
                        # Tolerancia de 5 minutos antes o después de la hora programada
                        time_diff = abs((current_dt - scheduled_dt).total_seconds()) / 60
                        if time_diff <= 5:  # 5 minutos de tolerancia
                            should_run = True
                            break
                    
                    if not should_run:
                        print(f"Script {mapping.get('script')} no programado para ejecutarse ahora ({current_time})")
                        print(f"Horarios programados: {', '.join(schedule_times)}")
                        logging.info(f"Script {mapping.get('script')} no programado para ejecutarse ahora ({current_time})")
                        continue
                
                # Si es sql_to_mysql, verificar que la tabla destino exista o se pueda crear
                if direction == 'sql_to_mysql':
                    print(f"Verificando existencia de tabla MySQL para: {source_table}")
                    target_table = mapping.get('target')
                    with self.get_mysql_connection() as (conn, cursor):
                        cursor.execute(f"""
                            SELECT COUNT(*)
                            FROM information_schema.tables 
                            WHERE table_schema = DATABASE() 
                            AND table_name = '{target_table}'
                        """)
                        exists = cursor.fetchone()[0] > 0
                        print(f"¿Tabla {target_table} existe en MySQL? {exists}")
                
                self.sync_table(source_table)
                print(f"Tabla {source_table} sincronizada correctamente")
            except Exception as e:
                error_details = str(e)
                if error_details == "0":
                    error_details = "Error desconocido (posiblemente error de creación de tabla o conversión de datos)"
                    
                print(f"Error en tabla {source_table}: {error_details}")
                logging.error(f"Error en tabla {source_table}: {error_details}")
                # Imprimir traceback para mejor diagnóstico
                import traceback
                print(f"Traceback completo para {source_table}:")
                traceback.print_exc()
                # Continuar con la siguiente tabla en lugar de detenerse
                continue


        logging.info(f"Sincronizaci�n completa. Tiempo total: {datetime.now() - start_time}")

    def cleanup(self) -> None:
        try:
            # No es necesario disponer conexiones ya que est�n manejadas con context managers
            logging.info("Limpieza completada")
        except Exception as e:
            logging.error(f"Error en limpieza: {str(e)}")

def main():
    try:
        print("Iniciando sincronizaci�n de bases de datos...")
        sync_manager = DatabaseSyncManager()
        print("Conexiones establecidas correctamente")
        print("Iniciando sincronizaci�n de tablas...")
        sync_manager.sync_all_tables()
        print("Sincronizaci�n completada con �xito")
    except Exception as e:
        print(f"ERROR: {str(e)}")
        logging.error(f"Error en la ejecuci�n principal: {str(e)}")
    finally:
        try:
            if 'sync_manager' in locals():
                sync_manager.cleanup()
            print("Proceso finalizado")
        except Exception as e:
            print(f"Error en limpieza final: {str(e)}")

if __name__ == "__main__":
    main()