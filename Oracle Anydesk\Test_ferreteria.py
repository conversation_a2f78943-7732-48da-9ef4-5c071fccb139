
import pyperclip

# from selenium import webdriver
# from selenium.webdriver.common.by import By  # Importa la clase By
# from selenium.webdriver.common.keys import Keys
import time
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriveFr.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

import psutil
from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np
import sys

def cerrar_proceso(nombre_proceso):
    procesos_cerrados = 0
    for proc in psutil.process_iter():
        try:
            if proc.name().lower() == nombre_proceso.lower():
                proc.terminate()
                procesos_cerrados += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if procesos_cerrados > 0:
        print(f"Se cerraron {procesos_cerrados} procesos de {nombre_proceso}.")
    else:
        print(f"No se encontraron procesos de {nombre_proceso}.")

class ImageNotFoundError(Exception):
    pass

def buscar_imagen_en_pantalla(imagen_a_buscar):
    try:
        # Imprime el nombre del archivo
        print(f"Buscando la imagen: {imagen_a_buscar}")

        # Toma una captura de pantalla
        screenshot = pyautogui.screenshot()
        screenshot_np = np.array(screenshot)
        screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

        # Lee la imagen a buscar y conviértela a escala de grises
        template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

        w, h = template.shape[::-1]

        # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
        result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
        loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

        # Si encontró la imagen, devuelve las coordenadas del centro
        for pt in zip(*loc[::-1]):
            centro_x = pt[0] + w // 2
            centro_y = pt[1] + h // 2
            return (centro_x, centro_y)

        # Si no encontró la imagen, verifica si es una de las excepciones
        if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\SecurityBox.png",
                               "C:\\Users\\<USER>\\Desktop\\SecurityRun.png",
                               "C:\\Users\\<USER>\\Desktop\\UsuarioChrome.png",
                               "C:\\Users\\<USER>\\Desktop\\UsuarioOracleJcepeda.png"]:
            print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
            return None

   

    except Exception as e:
        print(f"Ocurrió un error durante la búsqueda de la imagen: {str(e)}")
        return "IMAGE_NOT_FOUND"


time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Vina.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\StockPanel.png')
if coordenadas:
       pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CantidadPanel.png')
if coordenadas:
       pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])





time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])





time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\Organization.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\D31_ferret.png')
if coordenadas:
      pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CeldaVacia.png')
if coordenadas:
       pyautogui.rightClick(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CopyAll.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Ferreteria\\CierreVentana.png')
if coordenadas:
       pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

