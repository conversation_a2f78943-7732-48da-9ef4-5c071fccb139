import pandas as pd
import datetime as date
import requests
import json
from datetime import datetime, timedelta
import time
import numpy as np
import math
import pyodbc
import mysql.connector
import os

PERIODO_ACTUAL = '202509'

# ==============================================================================
# FUNCIÓN ANÁLISIS RGU PROBLEMÁTICO
# ==============================================================================

def analizar_rgu_problematico(sql_server_config, mysql_config, periodo):
    """
    Analiza RGU problemático desde SQL Server y lo inserta en MySQL tp_ptos_23_new
    
    Parámetros:
    - sql_server_config: Diccionario con configuración de conexión SQL Server
    - mysql_config: Diccionario con configuración de conexión MySQL
    - periodo: Período en formato 'YYYYMM' (ej: '202505')
    
    Retorna:
    - True si se procesó correctamente, False en caso de error
    """
    
    try:
        print(f"📊 Iniciando análisis RGU problemático para período {periodo}...")
        
        # Query para obtener combinaciones RGU problemático desde SQL Server
        query_analisis = f"""
        SELECT DISTINCT
            [Tipo red],
            [Trabajo],
            [Producto],
            [Clase vivienda], 
            [Tipo vivienda],
            REPLACE(CONCAT([Tipo red], Trabajo, Producto, [Clase vivienda], [Tipo vivienda]), ' ', '') as llave,
            0 as PuntosVTROct2023,
            0 as PuntosTQWOct23,
            0 as [Q actividad SSPP],
            0 as [Q act. Servicio],
            NULL as RGU,
            'RGU_NULL_PROBLEMATICO' as Clasif_final,
            'ANALISIS_PROBLEMATICO' as Segmento
        FROM ProduccionNDC 
        WHERE FORMAT(mes_contable,'yyyyMM') = '{periodo}'
        AND RGU IS NULL
        AND Estado = 'PGSC'
        """
        
        # Ejecutar query y convertir a DataFrame
        print(f"🔍 Ejecutando query de análisis en SQL Server...")
        # Crear conexión a SQL Server
        sql_conn_str = f"DRIVER={{{sql_server_config['driver']}}};SERVER={sql_server_config['server']};DATABASE={sql_server_config['database']};UID={sql_server_config['uid']};PWD={sql_server_config['pwd']}"
        sql_conn = pyodbc.connect(sql_conn_str)
        df_problematico = pd.read_sql_query(query_analisis, sql_conn)
        
        # Verificar si hay datos
        if df_problematico.empty:
            print(f"ℹ️  No se encontraron registros RGU problemático para el período {periodo}")
            return True
        
        print(f"✅ Encontrados {len(df_problematico)} registros RGU problemático")
        
        # Mostrar resumen de datos encontrados
        print(f"📋 Resumen por Tipo de Red:")
        resumen_red = df_problematico['Tipo red'].value_counts()
        for tipo_red, cantidad in resumen_red.items():
            print(f"   - {tipo_red}: {cantidad} registros")
        
        # Limpiar registros anteriores del análisis problemático en MySQL
        print(f"🗑️  Limpiando registros anteriores de análisis problemático...")
        
        # Crear conexión directa a MySQL
        mysql_conn = mysql.connector.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )
        
        cursor_mysql = mysql_conn.cursor()
        
        # Eliminar registros de análisis problemático anteriores
        delete_query = """
        DELETE FROM tp_ptos_23_new 
        WHERE Segmento = 'ANALISIS_PROBLEMATICO' 
        AND Clasif_final = 'RGU_NULL_PROBLEMATICO'
        """
        
        cursor_mysql.execute(delete_query)
        registros_eliminados = cursor_mysql.rowcount
        
        if registros_eliminados > 0:
            print(f"🗑️  Eliminados {registros_eliminados} registros anteriores de análisis problemático")
        
        # Preparar inserción manual
        columns = df_problematico.columns.tolist()
        columns_str = ", ".join([f"`{col}`" for col in columns])
        placeholders = ", ".join([f"%s" for _ in columns])
        
        # Construir consulta de inserción
        insert_query = f"INSERT INTO tp_ptos_23_new ({columns_str}) VALUES ({placeholders})"
        
        # Preparar valores para inserción
        values = []
        for _, row in df_problematico.iterrows():
            row_values = []
            for val in row:
                # Convertir NaN a None para que MySQL lo interprete como NULL
                if pd.isna(val):
                    row_values.append(None)
                else:
                    row_values.append(val)
            values.append(tuple(row_values))
        
        # Ejecutar inserción
        cursor_mysql.executemany(insert_query, values)
        
        # Confirmar transacción
        mysql_conn.commit()
        cursor_mysql.close()
        mysql_conn.close()
        
        print(f"✅ Análisis RGU problemático completado exitosamente")
        print(f"📊 Total registros procesados: {len(df_problematico)}")
        print(f"🎯 Período: {periodo}")
        print(f"⏰ Procesado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en análisis RGU problemático: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Función para insertar datos por lotes en MySQL
def insert_mysql_in_batches(dataframe, table_name, mysql_config, if_exists='replace', batch_size=10000):
    """
    Inserta un DataFrame en MySQL por lotes usando mysql.connector directamente.
    
    Args:
        dataframe: DataFrame de pandas a insertar
        table_name: Nombre de la tabla destino
        mysql_config: Diccionario con configuración de conexión MySQL
        if_exists: Estrategia si la tabla existe ('replace', 'append', 'fail')
        batch_size: Número de filas por lote
    """
    total_rows = len(dataframe)
    total_batches = math.ceil(total_rows / batch_size)
    
    print(f"Insertando {total_rows} registros en '{table_name}' en {total_batches} lotes...")
    
    if total_rows <= 0:
        print(f"No hay datos para insertar en la tabla '{table_name}'")
        return
        
    # Establecer conexión a MySQL
    try:
        conn = mysql.connector.connect(
            host=mysql_config['host'],
            port=mysql_config['port'],
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )
        cursor = conn.cursor()
        
        # Si es reemplace, eliminar tabla primero
        if if_exists == 'replace':
            print(f"Eliminando registros existentes de {table_name}...")
            cursor.execute(f"TRUNCATE TABLE {table_name}")
            conn.commit()
        
        # Obtener nombres de columnas
        columns = dataframe.columns.tolist()
        columns_str = ", ".join([f"`{col}`" for col in columns])
        placeholders = ", ".join([f"%s" for _ in columns])
        
        # Construir consulta de inserción
        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # Procesar por lotes
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch_df = dataframe.iloc[start_idx:end_idx]
            
            # Preparar valores para inserción
            values = []
            for _, row in batch_df.iterrows():
                row_values = []
                for val in row:
                    # Convertir NaN a None para que MySQL lo interprete como NULL
                    if pd.isna(val):
                        row_values.append(None)
                    else:
                        # Convertir fechas a formato string
                        if isinstance(val, (datetime, pd.Timestamp)):
                            row_values.append(val.strftime("%Y-%m-%d %H:%M:%S"))
                        else:
                            row_values.append(val)
                values.append(tuple(row_values))
            
            # Intentamos varias veces en caso de error temporal de conexión
            max_retries = 3
            retry_count = 0
            success = False
            
            while not success and retry_count < max_retries:
                try:
                    cursor.executemany(insert_query, values)
                    conn.commit()
                    success = True
                    print(f"Lote {i+1}/{total_batches} completado ({end_idx - start_idx} registros)")
                except Exception as e:
                    retry_count += 1
                    print(f"Error en lote {i+1}, intento {retry_count}: {e}")
                    if retry_count >= max_retries:
                        conn.rollback()
                        raise
                    time.sleep(2)  # Esperamos antes de reintentar
        
        cursor.close()
        conn.close()
        print(f"Conexión a MySQL cerrada. Inserción a '{table_name}' completada.")
        
    except Exception as e:
        print(f"Error en la inserción a MySQL: {e}")
        import traceback
        traceback.print_exc()
        raise

# Configurar el tamaño máximo de lote
BATCH_SIZE = 5000

try:
    print("Iniciando la carga de datos...")
    
    # Configuraciones de bases de datos
    sql_server_config = {
        'driver': 'ODBC Driver 17 for SQL Server',
        'server': '************',
        'database': 'telqway',
        'uid': 'ncornejo',
        'pwd': 'N1c0l7as17'
    }
    
    mysql_config = {
        'host': '**************',
        'port': 3306,
        'user': 'ncornejo',
        'password': 'N1c0l7as17',
        'database': 'operaciones_tqw'
    }
    
    # Establecer conexión a SQL Server
    print("Conectando a SQL Server...")
    sql_conn_str = f"DRIVER={{{sql_server_config['driver']}}};SERVER={sql_server_config['server']};DATABASE={sql_server_config['database']};UID={sql_server_config['uid']};PWD={sql_server_config['pwd']}"
    sql_conn = pyodbc.connect(sql_conn_str)
    sql_cursor = sql_conn.cursor()

    # Lectura de archivos Excel
    print("Leyendo archivos Excel...")
    data_xls = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Norte.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls2 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Sur.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls3 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Metro.xlsx', 'Sheet1', dtype=str, index_col=None)
    data_xls4 = pd.read_excel(r'C:\Users\<USER>\Dropbox\PythonNDC\Centro.xlsx', 'Sheet1', dtype=str, index_col=None)

    df = pd.DataFrame(data_xls)
    df2 = pd.DataFrame(data_xls2)
    df3 = pd.DataFrame(data_xls3)
    df4 = pd.DataFrame(data_xls4)

    print("Archivos Excel cargados correctamente en DataFrames.")

    # Inserción de datos en SQL Server usando conexión directa
    print("Insertando datos en SQL Server...")
    
    # Primero truncar la tabla
    print("Truncando tabla tb_paso_pyNdc...")
    sql_cursor.execute("IF OBJECT_ID('tb_paso_pyNdc', 'U') IS NOT NULL TRUNCATE TABLE tb_paso_pyNdc")
    sql_conn.commit()
    
    # Función para insertar DataFrame en SQL Server
    def insert_df_to_sql_server(df, table_name, cursor, connection, batch_size=1000):
        if df.empty:
            print(f"DataFrame vacío, no hay datos para insertar en {table_name}")
            return
            
        # Obtener lista de columnas
        columns = df.columns.tolist()
        columns_str = ", ".join([f"[{col}]" for col in columns])
        placeholders = ", ".join(["?" for _ in columns])
        
        # Preparar query
        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        
        # Insertar por lotes
        total_rows = len(df)
        total_batches = math.ceil(total_rows / batch_size)
        
        print(f"Insertando {total_rows} filas en {table_name} en {total_batches} lotes...")
        
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            
            # Convertir a lista de tuplas
            rows = []
            for _, row in batch_df.iterrows():
                # Convertir NaN a None
                row_values = [None if pd.isna(val) else val for val in row]
                rows.append(tuple(row_values))
            
            # Ejecutar inserción
            cursor.executemany(insert_query, rows)
            connection.commit()
            print(f"Lote {i+1}/{total_batches} insertado en {table_name}")
    
    # Insertar cada DataFrame
    print("Insertando datos de Metro...")
    insert_df_to_sql_server(df3, 'tb_paso_pyNdc', sql_cursor, sql_conn)
    
    print("Insertando datos de Sur...")
    insert_df_to_sql_server(df2, 'tb_paso_pyNdc', sql_cursor, sql_conn)
    
    print("Insertando datos de Norte...")
    insert_df_to_sql_server(df, 'tb_paso_pyNdc', sql_cursor, sql_conn)
    
    print("Insertando datos de Centro...")
    insert_df_to_sql_server(df4, 'tb_paso_pyNdc', sql_cursor, sql_conn)

    # Ejecución de procedimientos almacenados usando conexión directa
    print("Ejecutando procedimientos almacenados en SQL Server...")
    sql_cursor.execute("exec SP_INSERT_NDC")
    sql_conn.commit()
    print("SP_INSERT_NDC ejecutado exitosamente")

    print("Ejecutando procedimientos almacenados adicionales...")
    # Definir el periodo como variable estática
    
    sql_cursor.execute(f"exec SP_CREATE_NDC_PRODUCCION '{PERIODO_ACTUAL}'")
    sql_conn.commit()
    print(f"SP_CREATE_NDC_PRODUCCION '{PERIODO_ACTUAL}' ejecutado exitosamente")
    
    # NUEVO: Análisis RGU problemático
    print("\n" + "="*60)
    print("🔬 INICIANDO ANÁLISIS RGU PROBLEMÁTICO")
    print("="*60)
    
    exito_analisis = analizar_rgu_problematico(sql_server_config, mysql_config, PERIODO_ACTUAL)
    
    if exito_analisis:
        print("✅ Análisis RGU problemático completado exitosamente")
    else:
        print("⚠️  Error en análisis RGU problemático - Continuando con el flujo")
    
    print("="*60 + "\n")

    # Ejecutar los dos procedimientos almacenados en secuencia
    print(f"Ejecutando SP_TQW_COMISION_2023 '{PERIODO_ACTUAL}'...")
    sql_cursor.execute(f"EXEC SP_TQW_COMISION_2023 '{PERIODO_ACTUAL}'")
    sql_conn.commit()
    print(f"SP_TQW_COMISION_2023 ejecutado exitosamente")
    
    print(f"Ejecutando SP_TQW_COMISION_2023_V2 '{PERIODO_ACTUAL}'...")
    sql_cursor.execute(f"EXEC SP_TQW_COMISION_2023_V2 '{PERIODO_ACTUAL}'")
    sql_conn.commit()
    print(f"SP_TQW_COMISION_2023_V2 ejecutado exitosamente")

    # Lectura de datos para inserción en MySQL usando conexión directa
    print("Leyendo datos de SQL Server para transferir a MySQL...")
    Data = pd.read_sql_query("SELECT * from ProduccionNDC WHERE FORMAT(mes_contable,'yyyyMM') >= '202410'", sql_conn)
    print(f"Leídos {len(Data)} registros de ProduccionNDC")
    
    Data2 = pd.read_sql_query("SELECT * FROM TB_TQW_COMISION_2023", sql_conn)
    print(f"Leídos {len(Data2)} registros de TB_TQW_COMISION_2023")

    consulta_sql = "SELECT * FROM TB_MYSQL_CHART_NDC_MENSUAL"
    consulta_sql2 = "SELECT * FROM TB_MYSQL_CHART_NDC_DIA"
    consulta_comisiones_new = "SELECT * FROM TB_TQW_COMISION_RENEW"
    consulta_calidad_flujo = "SELECT * FROM vw_CalidadFlujo_kpi"
    consulta_ndc_rank = "SELECT * FROM PRODUCCION_NDC_RANK_Red"
    # tb_vtr_px_diaria = "SELECT * FROM tb_vtr_px_diaria"
    
    Data3 = pd.read_sql_query(consulta_sql, sql_conn)
    print(f"Leídos {len(Data3)} registros de TB_MYSQL_CHART_NDC_MENSUAL")
    
    Data4 = pd.read_sql_query(consulta_sql2, sql_conn)
    print(f"Leídos {len(Data4)} registros de TB_MYSQL_CHART_NDC_DIA")
    
    Data5 = pd.read_sql_query(consulta_comisiones_new, sql_conn)
    print(f"Leídos {len(Data5)} registros de TB_TQW_COMISION_RENEW")
    
    print("Leyendo datos de PRODUCCION_NDC_RANK_Red...")
    Data6 = pd.read_sql_query(consulta_ndc_rank, sql_conn)
    print(f"Leídos {len(Data6)} registros de PRODUCCION_NDC_RANK_Red")
    
    # Data7 = pd.read_sql_query(tb_vtr_px_diaria, sql_conn)
    
    print("Leyendo datos de vw_CalidadFlujo_kpi...")
    vw_CalidadFlujo_kpi = pd.read_sql_query(consulta_calidad_flujo, sql_conn)
    print(f"Se obtuvieron {len(vw_CalidadFlujo_kpi)} registros de vw_CalidadFlujo_kpi")

    # Inserción de datos en MySQL por lotes usando conexión directa
    print("Insertando datos en MySQL por lotes...")
    
    # Tablas más pequeñas primero
    insert_mysql_in_batches(Data3, 'tb_mysql_chart_ndc_mensual', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    insert_mysql_in_batches(Data4, 'tb_mysql_chart_ndc_dia', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    insert_mysql_in_batches(Data5, 'tb_tqw_comision_renew', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    insert_mysql_in_batches(Data6, 'PRODUCCION_NDC_RANK_Red', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    # insert_mysql_in_batches(Data7, 'tb_vtr_px_diaria', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    
    # Tablas más grandes ahora
    print(f"Iniciando inserción de datos en tb_paso_pyndc ({len(Data)} registros)...")
    insert_mysql_in_batches(Data,    'tb_paso_pyndc', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)
    
    print(f"Iniciando inserción de datos en tb_paso_kpi2023 ({len(Data2)} registros)...")
    insert_mysql_in_batches(Data2, 'tb_paso_kpi2023', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)

    print("¡Proceso de transferencia de datos completado exitosamente!")


    # Insertar vw_CalidadFlujo_kpi
    print(f"Insertando datos en vw_calidadflujo_kpi ({len(vw_CalidadFlujo_kpi)} registros)...")
    insert_mysql_in_batches(vw_CalidadFlujo_kpi, 'vw_calidadflujo_kpi', mysql_config, if_exists='replace', batch_size=BATCH_SIZE)

    
    
except Exception as e:
    print(f"Error durante la ejecución: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    # Cerrar conexiones
    print("Cerrando conexiones...")
    try:
        if 'sql_cursor' in locals() and sql_cursor:
            sql_cursor.close()
        if 'sql_conn' in locals() and sql_conn:
            sql_conn.close()
            print("Conexión a SQL Server cerrada")
    except Exception as e:
        print(f"Error al cerrar conexión SQL Server: {e}")
        
    print("Proceso finalizado")