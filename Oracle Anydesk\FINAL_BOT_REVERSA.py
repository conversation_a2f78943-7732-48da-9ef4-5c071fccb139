from selenium import webdriver
from selenium.webdriver.common.by import By  # Importa la clase By
from selenium.webdriver.common.keys import Keys
import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

from sqlalchemy import text
import sqlalchemy

import os
import glob

from image_search import buscar_imagen_en_pantalla

import pyautogui
import cv2
import numpy as np

# def buscar_imagen_en_pantalla(imagen_a_buscar):
#     try:
#         # Imprime el nombre del archivo
#         print(f"Buscando la imagen: {imagen_a_buscar}")

#         # Toma una captura de pantalla
#         screenshot = pyautogui.screenshot()
#         screenshot_np = np.array(screenshot)
#         screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

#         # Lee la imagen a buscar y conviértela a escala de grises
#         template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

#         w, h = template.shape[::-1]

#         # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
#         result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
#         loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

#         # Si encontró la imagen, devuelve las coordenadas del centro
#         for pt in zip(*loc[::-1]):
#             centro_x = pt[0] + w // 2
#             centro_y = pt[1] + h // 2
#             return (centro_x, centro_y)

#         # Si no encontró la imagen, verifica si es una de las excepciones
#         if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\D32Vina_reversa2.png",
#                                "C:\\Users\\<USER>\\Desktop\\D32Vina_reversa.png",
#                                "C:\\Users\\<USER>\\Desktop\\ContinueEnd.png"
#                                ]:            
#             print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
#             return None

        
#         # cerrar_proceso("msedge.exe")
#         # cerrar_chrome_suavemente()
#         return "IMAGE_NOT_FOUND"

#     except Exception as e:
#         print(f"Ocurrió un error durante la búsqueda de la imagen: {str(e)}")
#         return "IMAGE_NOT_FOUND"


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\STGO_reversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(4)



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\G41Stgo_reversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(14)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(8)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(80)

engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()
session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_REVERSAX"))
session.commit()

list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')
# Crear el diccionario de tipos de datos personalizados
custom_dtypes = {
'Serial': sqlalchemy.types.String(50),
'Item': sqlalchemy.types.String(50),
'Org': sqlalchemy.types.String(50),
'Revision': sqlalchemy.types.String(50),
'Subinventory': sqlalchemy.types.String(50),
'Locator': sqlalchemy.types.String(50),
'Operation': sqlalchemy.types.String(50),
'Job': sqlalchemy.types.String(50),
'Step': sqlalchemy.types.String(50),
'Lot': sqlalchemy.types.String(50),
'State': sqlalchemy.types.String(50),
'Status': sqlalchemy.types.String(50),
'Receipt Date': sqlalchemy.types.String(50),
'Ship Date': sqlalchemy.types.String(50),
'Supplier Name': sqlalchemy.types.String(50),
'Supplier Lot': sqlalchemy.types.String(50),
'Supplier Serial': sqlalchemy.types.String(50),
'Unit Number': sqlalchemy.types.String(50),
'Attributes': sqlalchemy.types.String(50),
'[  ]': sqlalchemy.types.String(50),
'Unnamed: 20': sqlalchemy.types.String(50)
}

# Truncar las cadenas a un máximo de 100 caracteres
#  df_truncated = df_dtype_str.applymap(lambda x: x[:100])
# Insertar el DataFrame en la tabla SQL

# Insertar el DataFrame en la tabla SQL
df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")
                 

time.sleep(7)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

################## ################## ################## 
################## VIÑA ################## ################## 
###################################################### 
# ################## ################## ##################   
time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\VinaReversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D32Vina_reversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D32Vina_reversa2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(10)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(16)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(120)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(4)

df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame de VIÑA: {len(df)}")
                 

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)

############################################################
########## CONCE ###########################################
############################################################






time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Conce.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)



pyautogui.scroll(-1200)

pyautogui.scroll(-1200)
 
pyautogui.scroll(-1200)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J64Conce_reversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(12)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(6)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(50)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(10)

df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")
                 

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)




############################################################
########## ANTO ###########################################
############################################################

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\AntoDirecta.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\A91Anto_reversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(12)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(4)

df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")
                 

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)





############################################################
########## TEMUCO ###########################################
############################################################


pyautogui.scroll(-1200)

pyautogui.scroll(-1200)

pyautogui.scroll(-1200)


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\TemucoDirecta.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J68_TemucoReversa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(12)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(8)

df.to_sql('TB_PASO_0_ORACLE_REVERSAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")
                 

time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)







session.execute(text("EXEC SP_INSERT_ORACLE_REVERSA"))
session.commit()

Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_REVERSAX", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)
engineMYsql_new = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)



series_reversa = pd.read_sql_query("SELECT * FROM tb_logis_tecnico_serie_reversa", engineMYsql_new )
series_reversa.to_sql('tb_logis_tecnico_serie_reversa',engine , if_exists='replace',index=False)



Data.to_sql('tb_logist_bdreversa', engineMYsql_new, if_exists='replace',index=False)



sql2 = text("CALL UpdateLogisticaReversa();")

try:
    with engineMYsql_new.connect() as connection:
        connection.execute(sql2)
        print("Stored Procedure executed successfully!")
except Exception as e:
    print(f"Error executing Stored Procedure: {str(e)}")
finally:
    session.close()
    engineMYsql_new.dispose()



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreOracle.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])
time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OkOracleCierre.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge.png')
if coordenadas:
    pyautogui.rightClick(coordenadas[0], coordenadas[1])


time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CloseWindows.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])