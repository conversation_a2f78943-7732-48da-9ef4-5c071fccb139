import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
import os
import socket
import ssl
from typing import List, Optional, Dict, Union
import logging

# Configurar logging
logger = logging.getLogger(__name__)

def enviar_correo(
    destinatario: Union[str, List[str]],
    asunto: str,
    cuerpo: str,
    remitente: str = '<EMAIL>',
    cc: Optional[Union[str, List[str]]] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    archivos_adjuntos: Optional[List[str]] = None,
    html: bool = False,
    smtp_config: Optional[Dict] = None,
    debug: bool = True
) -> Dict:
    """
    Función mejorada para enviar correos electrónicos con mejor manejo de errores.
    
    Args:
        destinatario: Email o lista de emails de destinatarios
        asunto: Asunto del correo
        cuerpo: Contenido del correo
        remitente: Email del remitente (por defecto: <EMAIL>)
        cc: Email o lista de emails en copia
        bcc: Email o lista de emails en copia oculta
        archivos_adjuntos: Lista de rutas a archivos para adjuntar
        html: Boolean para indicar si el cuerpo es HTML (True) o texto plano (False)
        smtp_config: Diccionario con configuración SMTP personalizada
        debug: Activar modo debug para más información
        
    Returns:
        Dict: Diccionario con resultado de la operación {'exito': bool, 'mensaje': str}
    """
    # Configuración por defecto del servidor SMTP
    default_smtp = {
        'server': 'mail.telqway.cl',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'N1c0l7as17#',
        'use_tls': False,
        'use_ssl': True,
        'timeout': 30
    }
    
    # Usar configuración personalizada si se proporciona
    config = {**default_smtp, **(smtp_config or {})}
    
    if debug:
        logger.info(f"Configuración SMTP: servidor={config['server']}, puerto={config['port']}, ssl={config['use_ssl']}, tls={config['use_tls']}")
    
    # Validar configuración básica
    if not config['server'] or not config['username'] or not config['password']:
        return {"exito": False, "mensaje": "Configuración SMTP incompleta"}
    
    # Verificar conectividad al servidor
    if debug:
        logger.info(f"Verificando conectividad a {config['server']}:{config['port']}...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((config['server'], config['port']))
            sock.close()
            if result != 0:
                return {"exito": False, "mensaje": f"No se puede conectar a {config['server']}:{config['port']}. Puerto cerrado o bloqueado."}
            logger.info("✓ Conectividad verificada")
        except Exception as e:
            return {"exito": False, "mensaje": f"Error verificando conectividad: {str(e)}"}
    
    # Crear mensaje
    mensaje = MIMEMultipart()
    mensaje['From'] = remitente
    
    # Manejar destinatarios múltiples
    if isinstance(destinatario, list):
        mensaje['To'] = ', '.join(destinatario)
    else:
        mensaje['To'] = destinatario
    
    # Manejar CC
    if cc:
        if isinstance(cc, list):
            mensaje['Cc'] = ', '.join(cc)
        else:
            mensaje['Cc'] = cc
    
    # Manejar BCC (no se agrega al encabezado pero se usa para enviar)
    bcc_list = []
    if bcc:
        if isinstance(bcc, list):
            bcc_list = bcc
        else:
            bcc_list = [bcc]
    
    mensaje['Subject'] = asunto
    
    # Agregar cuerpo del mensaje
    if html:
        mensaje.attach(MIMEText(cuerpo, 'html', 'utf-8'))
    else:
        mensaje.attach(MIMEText(cuerpo, 'plain', 'utf-8'))
    
    # Agregar archivos adjuntos si existen
    if archivos_adjuntos:
        for archivo in archivos_adjuntos:
            if os.path.exists(archivo):
                try:
                    with open(archivo, 'rb') as file:
                        part = MIMEApplication(file.read(), Name=os.path.basename(archivo))
                        part['Content-Disposition'] = f'attachment; filename="{os.path.basename(archivo)}"'
                        mensaje.attach(part)
                    if debug:
                        logger.info(f"✓ Archivo adjunto agregado: {archivo}")
                except Exception as e:
                    logger.error(f"Error adjuntando archivo {archivo}: {str(e)}")
            else:
                logger.warning(f"Archivo no encontrado: {archivo}")
    
    server = None
    try:
        # Determinar el tipo de conexión (SSL o TLS)
        if debug:
            logger.info(f"Conectando a {config['server']}:{config['port']}...")
        
        if config['use_ssl']:
            # Crear contexto SSL con verificación relajada para servidores internos
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            server = smtplib.SMTP_SSL(
                config['server'], 
                config['port'], 
                timeout=config.get('timeout', 30),
                context=context
            )
        else:
            server = smtplib.SMTP(
                config['server'], 
                config['port'], 
                timeout=config.get('timeout', 30)
            )
            if config['use_tls']:
                server.starttls()
        
        if debug:
            server.set_debuglevel(2)
            logger.info("✓ Conexión establecida")
        
        # Iniciar sesión
        if debug:
            logger.info(f"Autenticando como {config['username']}...")
        
        server.login(config['username'], config['password'])
        
        if debug:
            logger.info("✓ Autenticación exitosa")
        
        # Crear lista completa de destinatarios para el envío
        todos_destinatarios = []
        if isinstance(destinatario, list):
            todos_destinatarios.extend(destinatario)
        else:
            todos_destinatarios.append(destinatario)
            
        if cc:
            if isinstance(cc, list):
                todos_destinatarios.extend(cc)
            else:
                todos_destinatarios.append(cc)
                
        if bcc_list:
            todos_destinatarios.extend(bcc_list)
        
        if debug:
            logger.info(f"Enviando a {len(todos_destinatarios)} destinatario(s)...")
        
        # Enviar correo
        server.send_message(mensaje, from_addr=remitente, to_addrs=todos_destinatarios)
        
        if debug:
            logger.info("✓ Mensaje enviado")
        
        server.quit()
        
        return {"exito": True, "mensaje": f"Correo enviado con éxito a {mensaje['To']}"}
    
    except smtplib.SMTPAuthenticationError as e:
        error_msg = f"Error de autenticación: Usuario o contraseña incorrectos. Detalles: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except smtplib.SMTPConnectError as e:
        error_msg = f"Error de conexión al servidor SMTP: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except smtplib.SMTPServerDisconnected as e:
        error_msg = f"El servidor SMTP se desconectó inesperadamente: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except smtplib.SMTPRecipientsRefused as e:
        error_msg = f"Destinatario(s) rechazado(s): {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except socket.timeout as e:
        error_msg = f"Timeout al conectar con el servidor SMTP: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except socket.gaierror as e:
        error_msg = f"Error DNS: No se puede resolver el servidor {config['server']}: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except ssl.SSLError as e:
        error_msg = f"Error SSL/TLS: {str(e)}"
        logger.error(error_msg)
        return {"exito": False, "mensaje": error_msg}
    
    except Exception as e:
        error_msg = f"Error inesperado al enviar correo: {type(e).__name__}: {str(e)}"
        logger.error(error_msg)
        import traceback
        logger.error(traceback.format_exc())
        return {"exito": False, "mensaje": error_msg}
    
    finally:
        if server:
            try:
                server.quit()
            except:
                pass

# Función de prueba rápida
def test_email():
    """Función para probar el envío de correos"""
    import logging
    from datetime import datetime
    
    # Configurar logging para la prueba
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("\n" + "="*60)
    print("PRUEBA DE ENVÍO DE CORREO")
    print("="*60)
    
    resultado = enviar_correo(
        destinatario='<EMAIL>',
        asunto=f'Prueba de correo - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
        cuerpo='Este es un correo de prueba enviado desde email_utils_v2.py',
        debug=True
    )
    
    print(f"\nResultado: {resultado}")
    print("="*60)
    
    return resultado['exito']

if __name__ == "__main__":
    test_email()