import pandas as pd
from datetime import datetime
import requests
import json
import mysql.connector
import pymssql  # Importar pymssql para SQL Server
import pyodbc  # Para SQL Server con ODBC

from email_utils import enviar_correo

# Función para obtener conexión a SQL Server
def get_sqlserver_connection():
    try:
        # Intentar con pyodbc primero, es más estándar
        conn_str = 'DRIVER={ODBC Driver 17 for SQL Server};SERVER=************;DATABASE=telqway;UID=ncornejo;PWD=**********;MARS_Connection=Yes'
        conn = pyodbc.connect(conn_str)
        print("Conexión exitosa con pyodbc")
        return conn
    except Exception as e:
        print(f"Error con pyodbc: {e}")
        # Fallback a pymssql si pyodbc falla
        try:
            conn = pymssql.connect(server='************', user='ncornejo', password='**********', database='telqway')
            print("Conexión exitosa con pymssql")
            return conn
        except Exception as e_pymssql:
            print(f"Error con pymssql: {e_pymssql}")
            raise Exception("No se pudo conectar a SQL Server con ninguno de los drivers.")

# Función para obtener conexión a MySQL
def get_mysql_connection():
    return mysql.connector.connect(
        host='**************',
        user='ncornejo',
        password='**********',
        database='operaciones_tqw',
        port=3306
    )

# --- Conexiones a Bases de Datos ---
print("Estableciendo conexiones a las bases de datos...")
sqlserver_conn = get_sqlserver_connection()
mysql_conn = get_mysql_connection()
print("Conexiones establecidas.")

# --- Configuración de Correo ---
# (La configuración de SMTP se maneja dentro de email_utils, no es necesaria aquí)

# --- Carga de Datos Inicial ---
url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTteU27KE8UKXoec9RT1XvNsQWIWJ-6-lsNl0jurlHwgoSMpviPbcnH9U-0LCQkePYZXL1sZFoDaF7s/pub?output=csv'
print(f"Descargando datos desde: {url}")
df = pd.read_csv(url)
print(f"Registros leídos del CSV: {len(df)}")

# --- Funciones de Base de Datos ---

def safe_to_sql(dataframe, table_name, connection, db_type, if_exists='replace', batch_size=500):
    """
    Inserta un DataFrame en la base de datos usando el driver nativo.
    db_type: 'sqlserver' o 'mysql'.
    if_exists: 'replace' (elimina y crea) o 'append' (solo inserta).
    """
    if dataframe.empty:
        print(f"DataFrame para la tabla {table_name} está vacío. No se realiza ninguna acción.")
        return True

    cols = dataframe.columns.tolist()
    # Convertir valores NaN de pandas a None de Python para compatibilidad con BD
    records = [tuple(None if pd.isna(x) else x for x in row) for row in dataframe.itertuples(index=False, name=None)]
    
    cursor = connection.cursor()
    try:
        if if_exists == 'replace':
            print(f"Modo 'replace': Eliminando y creando la tabla {table_name} en {db_type}.")
            if db_type == 'mysql':
                # Se usa DROP + CREATE para asegurar una tabla limpia con la estructura del DataFrame
                cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`;")
                # Asumiendo VARCHAR(255) para simplicidad. Ajustar si es necesario.
                col_defs = ", ".join(f"`{c}` VARCHAR(255)" for c in cols)
                cursor.execute(f"CREATE TABLE `{table_name}` ({col_defs});")
            else: # sqlserver
                cursor.execute(f"IF OBJECT_ID('{table_name}', 'U') IS NOT NULL DROP TABLE {table_name};")
                col_defs = ", ".join(f"[{c}] VARCHAR(255)" for c in cols)
                cursor.execute(f"CREATE TABLE {table_name} ({col_defs});")
        
        # Construir la sentencia INSERT
        columns_sql = ", ".join(f"`{c}`" if db_type == 'mysql' else f"[{c}]" for c in cols)
        placeholders = ", ".join(["%s"] * len(cols))
        insert_sql = f"INSERT INTO `{table_name}` ({columns_sql}) VALUES ({placeholders});" if db_type == 'mysql' else f"INSERT INTO {table_name} ({columns_sql}) VALUES ({placeholders});"
        
        # Inserción por lotes para manejar grandes volúmenes de datos
        print(f"Iniciando inserción por lotes en {table_name}. Total registros: {len(records)}. Tamaño del lote: {batch_size}.")
        for i in range(0, len(records), batch_size):
            batch = records[i:i+batch_size]
            cursor.executemany(insert_sql, batch)
            print(f"  ... Lote {i//batch_size + 1} insertado.")
        
        connection.commit()
        print(f"Inserción de datos en {table_name} completada exitosamente.")
        return True
    except Exception as e:
        print(f"Error al guardar en {table_name}: {e}")
        # Imprimir una muestra de los datos problemáticos puede ayudar a diagnosticar
        if 'records' in locals() and len(records) > 0:
            print("Muestra de datos que podrían estar causando el error:")
            print(records[0])
        connection.rollback() # Revertir cambios en caso de error
        return False
    finally:
        cursor.close()

def safe_read_sql(query, connection):
    """
    Ejecuta una consulta de lectura y devuelve un DataFrame usando un driver nativo.
    """
    try:
        print(f"Ejecutando consulta: {query[:80]}...")
        df_result = pd.read_sql(query, connection)
        print(f"Consulta completada. Registros obtenidos: {len(df_result)}")
        return df_result
    except Exception as e:
        print(f"Error al ejecutar consulta: {str(e)}")
        return pd.DataFrame() # Devolver DataFrame vacío en caso de error

# --- Lógica Principal del Proceso ---

# 1. Cargar datos del CSV a tablas de paso
print("=== PASO 1: Cargando datos de Google Sheets a tablas de paso ===")
df['fecha_upload'] = datetime.now()
safe_to_sql(df, 'TB_HCMFRONT_PASO', sqlserver_conn, 'sqlserver', if_exists='replace')
safe_to_sql(df, 'tb_hcmfront_paso', mysql_conn, 'mysql', if_exists='replace')

# 2. Actualizar estado de usuarios en MySQL basado en la carga
print("=== PASO 2: Actualizando estado de vigencia de usuarios en MySQL ===")
update_query = """
UPDATE tb_user_tqw 
SET vigente = 'No'
WHERE PERFIL = 'TECNICO RESIDENCIAL' 
AND vigente = 'Si'
AND rut NOT IN (
    SELECT REPLACE(`Número de Identificación`, '.', '') FROM tb_hcmfront_paso
);
"""
try:
    print("Ejecutando consulta de actualización en MySQL...")
    cursor = mysql_conn.cursor()
    cursor.execute(update_query)
    mysql_conn.commit()
    print(f"Actualización completada. {cursor.rowcount} filas afectadas.")
    cursor.close()
except Exception as e:
    print(f"Error en la actualización de vigencia en MySQL: {str(e)}")
    mysql_conn.rollback()

# 3. Sincronizar datos desde SQL Server a MySQL
print("=== PASO 3: Sincronizando datos de SQL Server a MySQL ===")

# Sincronizar tb_user_tqw
print("--- Sincronizando usuarios (VW_HCMFRONT_TB_USER) ---")
Data = safe_read_sql("SELECT * FROM VW_HCMFRONT_TB_USER", sqlserver_conn)
# Usar 'replace' para truncar y recargar, evitando duplicados
safe_to_sql(Data, 'tb_user_tqw', mysql_conn, 'mysql', if_exists='replace')

# Sincronizar tb_claves_usuarios
print("--- Sincronizando claves (VW_HCMFRONT_TB_CLAVES) ---")
Data2 = safe_read_sql("SELECT * FROM VW_HCMFRONT_TB_CLAVES", sqlserver_conn)
safe_to_sql(Data2, 'tb_claves_usuarios', mysql_conn, 'mysql', if_exists='replace')

# Sincronizar tp_modelo_seni_juni
print("--- Sincronizando segmentos (VW_HCMFRONT_SEGMENTO) ---")
Data3 = safe_read_sql("SELECT * FROM VW_HCMFRONT_SEGMENTO", sqlserver_conn)

if not Data3.empty:
    column_mapping = {'Rut': 'RUT', 'categoria': 'Categoria'}
    Data3 = Data3.rename(columns=column_mapping)
    mysql_columns = ['RUT', 'Categoria', 'Nombre', 'periodo', 'modelo_turno']
    # Asegurarse de que todas las columnas necesarias existen
    for col in mysql_columns:
        if col not in Data3.columns:
            Data3[col] = None # Añadir columna faltante con valores nulos
    Data3_reordered = Data3[mysql_columns]
    safe_to_sql(Data3_reordered, 'tp_modelo_seni_juni', mysql_conn, 'mysql', if_exists='replace')

# 4. Enviar correos de bienvenida a usuarios nuevos
print("=== PASO 4: Enviando correos de bienvenida ===")
# Se asume que 'Data' contiene los usuarios recién sincronizados
if not Data.empty:
    selected_fields = Data[['Nombre', 'correo_super', 'email', 'Rut']]
    print(f"Se procesarán {len(selected_fields)} registros para posible envío de correos.")

    for index, row in selected_fields.iterrows():
        try:
            # Validar que el destinatario no sea nulo
            if pd.isna(row['email']) or row['email'].strip() == '':
                print(f"Correo no válido para {row['Nombre']}. Saltando.")
                continue

            asunto = f"BIENVENIDO AL PORTAL DE OPERACIONES {row['Nombre']}"
            destinatario = row['email']
            
            cc_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
            if pd.notna(row['correo_super']):
                cc_list.append(row['correo_super'])
            
            cuerpo = f'''
            Estimado/a {row['Nombre']},

            ¡Bienvenido/a a Telqway! Nos complace darte la más cordial bienvenida a nuestro equipo.
            Como parte de tu integración, te proporcionamos acceso a nuestro portal del área, una herramienta fundamental para tu trabajo diario.

            Datos de acceso a la intranet:
            URL: https://appoperaciones.telqway.cl/
            Usuario: {row['email']}
            Contraseña: {str(row['Rut'])[:-2]} (sin puntos ni guión)

            En nuestra intranet encontrarás:
            - Indicadores de producción y calidad.
            - Módulo de logística y control de inventario.
            - Herramientas de colaboración y noticias.

            Si tienes alguna duda, contacta a tu supervisor directo.
            ¡Te deseamos mucho éxito!

            Atentamente,
            Equipo Telqway'''

            resultado = enviar_correo(destinatario=destinatario, asunto=asunto, cuerpo=cuerpo, cc=cc_list)
            
            if resultado.get("exito"):
                print(f"Correo enviado exitosamente a {destinatario}.")
            else:
                print(f"Error al enviar correo a {destinatario}: {resultado.get('mensaje', 'Error desconocido')}")
        
        except Exception as e:
            print(f"Error crítico al procesar el correo para {row.get('email', 'N/A')}: {str(e)}")
            continue
else:
    print("No se encontraron usuarios para enviar correos.")

# --- Cierre de Conexiones ---
try:
    print("Cerrando conexiones a bases de datos...")
    if sqlserver_conn:
        sqlserver_conn.close()
    if mysql_conn:
        mysql_conn.close()
    print("Conexiones cerradas.")
except Exception as e:
    print(f"Error al cerrar las conexiones: {e}")

print("Proceso completado.")