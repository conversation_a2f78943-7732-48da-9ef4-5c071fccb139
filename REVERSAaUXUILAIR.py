
from selenium import webdriver
from selenium.webdriver.common.by import By  # Importa la clase By
from selenium.webdriver.common.keys import Keys
import time
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()

session.execute(text("EXEC SP_INSERT_ORACLE_REVERSA"))
session.commit()

Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_REVERSAX", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('TB_LOGIST_bdReversa', engineMYsql, if_exists='replace',index=False)


# Define la sentencia SQL para crear el índice
sql = text("CREATE INDEX idx_serial ON TB_LOGIST_bdReversa (Serial(255));")


session.close()

# Ejecuta la sentencia SQL
with engineMYsql.connect() as connection:
    connection.execute(sql)

# Cierra la conexión
engineMYsql.dispose()

