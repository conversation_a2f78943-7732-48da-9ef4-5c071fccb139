import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from tabulate import tabulate

engine = create_engine('mssql+pyodbc://sa:N1c0l7as@************/master?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# Ruta del archivo CSV
Url_link = "C:\\Users\\<USER>\\\Downloads\\Produc_Ene.txt"

# Cargar el archivo CSV en fragmentos para manejar grandes volúmenes de datos
chunk_size = 10000  # Tamaño del fragmento
# chunks = pd.read_csv(Url_link, encoding='latin-1', delimiter=';', skiprows=2, chunksize=chunk_size)
chunks = pd.read_csv(Url_link, encoding='latin-1', delimiter='\t', chunksize=chunk_size)

# Inicializar un DataFrame vacío para acumular los resultados
df_total = pd.DataFrame()

# Procesar cada fragmento
for chunk in chunks:
    # Limpieza de los datos en el chunk
    chunk.columns = chunk.columns.astype(str).str.replace("Consolidado\[", "", regex=True)
    chunk.columns = chunk.columns.str.replace("]", "", regex=True)

    # Acumular los resultados
    df_total = pd.concat([df_total, chunk])


print(df_total.head())


df_total.to_sql('TB_SHARE_PRODUCTIVIDAD23_paso', engine, if_exists='replace',index=False)

# # Crear un data frame a partir de una consulta a SQL Server
# Data = pd.read_sql_query("SELECT * FROM  vw_CalidadFlujo WHERE FORMAT(MES_CONTABLE,'yyyyMM') IN ('202308','202307','202306','202309','202310','202311','202312','202401','202402','202403','202404') ", engine)

# engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

# Data.to_sql('tb_py_Flujo_Calidad', engineMYsql, if_exists='replace',index=False)

# Data2 = pd.read_sql_query("SELECT * FROM TB_TQW_COMISION_2023", engine)

# Data2.to_sql('tb_paso_KPI2023', engineMYsql, if_exists='replace',index=False)



