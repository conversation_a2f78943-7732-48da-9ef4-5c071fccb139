import pyautogui
from sqlalchemy import create_engine, <PERSON><PERSON><PERSON>, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import sqlalchemy
from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import time
import os
from datetime import date
import pandas as pd
import glob
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

import datetime





# dtypes = {
#     'Celular': sqlalchemy.types.VARCHAR(100),
#     'Clase de Vivienda': sqlalchemy.types.VARCHAR(100),
#     'Coord X': sqlalchemy.types.Float(),
#     'Coord Y': sqlalchemy.types.Float(),
#     'Notas Materiales': sqlalchemy.types.VARCHAR(255),
#     'Indicador Capacidad': sqlalchemy.types.VARCHAR(100),
#     'Area derivación': sqlalchemy.types.VARCHAR(100),
#     'Nodo': sqlalchemy.types.VARCHAR(100),
#     'Nro Solicitud de Servicio': sqlalchemy.types.VARCHAR(100),
#     'Teléfono': sqlalchemy.types.VARCHAR(100),
#     'Prioridad': sqlalchemy.types.VARCHAR(100),
#     'Razón de Cancelación': sqlalchemy.types.VARCHAR(255),
#     'Provincia': sqlalchemy.types.VARCHAR(100),
#     'Subnodo': sqlalchemy.types.VARCHAR(100),
#     'Código postal': sqlalchemy.types.VARCHAR(100),
#     'ID de actividad': sqlalchemy.types.VARCHAR(100),
#     'Código GIS': sqlalchemy.types.VARCHAR(100),
#     'Cantidad de derivaciones NO terreno': sqlalchemy.types.Integer(),
#     'Cantidad de derivaciones terreno': sqlalchemy.types.Integer(),
#     'Tipo de work skill Siebel': sqlalchemy.types.VARCHAR(100)
# }



# PATH = "C:/Users/<USER>/Desktop/Bot Python/chromedriver3.exe"
driver = webdriver.Edge(executable_path="C:/Users/<USER>/Desktop/Bot Python/edgedriver_win64/msedgedriver.exe")
# driver = webdriver.Chrome(PATH)

driver.get("https://vtr.fs.ocs.oraclecloud.com/")

time.sleep(7) 
# search = driver.find_element_by_xpath("/html/body/div/div/div/div/form/div[2]/div[1]/div[1]/div/input").send_keys("<EMAIL>")

# time.sleep(3) 
# search = driver.find_element_by_xpath("/html/body/div/div/div/div/form/div[2]/div[2]/div[1]/div/input").send_keys("NA1311ch")



search = driver.find_element_by_xpath("/html/body/div/div/div/div/form/div[2]/div[6]/div[2]/button").click()

time.sleep(5) 
search = driver.find_element_by_xpath("/html/body/div/form[1]/div/div/div[2]/div[1]/div/div/div/div/div[1]/div[3]/div/div/div/div[2]/div[2]/div/input[1]").send_keys("<EMAIL>")

#Boton Aceptar
search = driver.find_element_by_xpath("/html/body/div/form[1]/div/div/div[2]/div[1]/div/div/div/div/div[1]/div[3]/div/div/div/div[4]/div/div/div/div/input").click()

time.sleep(5) 
search = driver.find_element_by_xpath("/html/body/div/form[1]/div/div/div[2]/div[1]/div/div/div/div/div/div[3]/div/div[2]/div/div[3]/div/div[2]/input").send_keys("NI1607co")



time.sleep(5) 
search = driver.find_element_by_xpath("/html/body/div/form[1]/div/div/div[2]/div[1]/div/div/div/div/div/div[3]/div/div[2]/div/div[4]/div[2]/div/div/div/div/input").click()

time.sleep(5) 
search = driver.find_element_by_xpath("/html/body/div/form/div/div/div[2]/div[1]/div/div/div/div/div/div[3]/div/div[2]/div/div[3]/div[2]/div/div/div[1]/input").click()

time.sleep(7) 

search = driver.find_element_by_xpath("/html/body/div[14]/div[1]/main/div/div[2]/div[3]/div[1]/div[2]/div/div[2]/div[2]/div/div[2]/div[3]/div[2]/div[1]/button[3]").click()

time.sleep(5) 
search = driver.find_element_by_xpath("/html/body/div[14]/div[1]/main/div/div[2]/div[3]/div[1]/div[2]/div/div[2]/div[2]/div/div[2]/div[3]/div[2]/div[1]/button[3]").click()

fecha_actual = datetime.datetime.now() # Obtiene la fecha y hora actual
fecha_anterior = fecha_actual - datetime.timedelta(days=1) # Resta un día a la fecha actual

fecha_anterior_str = fecha_actual.strftime('%m_%d_%y') # Convierte la fecha a formato de cadena

print(fecha_anterior_str)


try:

        
    # today = datetime.date.today()
    # date_str = today.strftime('%m_%d_%y')  # Formato: MM_DD_YY

    file_path = rf'C:\Users\<USER>\Downloads\Actividades-VTR_{fecha_anterior_str}.xlsx'

    if os.path.exists(file_path):
        os.remove(file_path)


    # Espera hasta que el botón esté presente y visible
    button = WebDriverWait(driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, "button[title='Vista']")))
    
    # Realiza el clic en el botón
    button.click()
    time.sleep(5) 
   
    #
    search = driver.find_element_by_xpath("/html/body/div[26]/div/div/div/div[1]/form/div/div[3]/oj-checkboxset/div[1]/span/span/input").click()



    # Localizar el contenedor por su xpath
    contenedor = driver.find_element(By.XPATH, '/html/body/div[26]/div/div/div/div[2]')

    # Obtener todos los elementos dentro del contenedor
    elementos = contenedor.find_elements(By.XPATH, './/*')

    # Hacer clic en el primer elemento dentro del contenefdor
    if elementos:
        primer_elemento = elementos[0]
        primer_elemento.click()

    
    time.sleep(4) 


    button2 = WebDriverWait(driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, "button[title='Acciones']")))
    # Realiza el clic en el botón
    button2.click()

    time.sleep(4) 
    search = driver.find_element_by_xpath("/html/body/div[26]/div/div/button[3]").click()
    time.sleep(20) 
    
    
    engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')

    Session = sessionmaker(bind=engine)
    session = Session()


        # Formatear la ruta del archivo Excel con la variable de fecha
    ruta_excel = rf'C:\Users\<USER>\Downloads\Actividades-VTR_{fecha_anterior_str}.xlsx'

    # Leer el archivo Excel utilizando la ruta formateada
    data_xls4 = pd.read_excel(ruta_excel, engine='openpyxl')

    df4 = pd.DataFrame(data_xls4)


    # Ahora df4 contiene la fecha actual en formato de fecha y puede ser guardada en la base de datos
    df4.to_sql('TB_TOA_PY_HOY', engine, if_exists='replace', index=False)




    engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

    df4.to_sql('TB_TOA_PY_HOY', engineMYsql, if_exists='replace',index=False)
  
    session.execute("exec SP_CREATE_TOA_HOY")  #INSERTAR LOGDEDECLARACIONDEORDENES.REGISTRO.HISTORICO.NDCOMPLETO.4FUNCIONES
    session.commit()
    session.close()

except Exception as e:
    print("Error al hacer clic en el botón:", e)



finally:
    # Cierra el navegador al finalizar
    driver.quit()
