from selenium import webdriver
from selenium.webdriver.edge.service import Service
from PIL import Image
import io
import time
import os

def take_full_screenshot(driver, url, output_path):
    driver.get(url)
    # Increased wait time for Power BI reports which can be slower to load
    driver.implicitly_wait(20)
    # Additional explicit wait for dynamic content
    time.sleep(5)

    # Get viewport dimensions
    viewport_width = driver.execute_script("return window.innerWidth")
    viewport_height = driver.execute_script("return window.innerHeight")

    # Get total height
    total_height = driver.execute_script("return document.body.scrollHeight")

    # Calculate number of scrolls needed
    scrolls = (total_height // viewport_height) + 1

    screenshots = []

    for i in range(scrolls):
        # Scroll to position
        scroll_position = i * viewport_height
        driver.execute_script(f"window.scrollTo(0, {scroll_position})")
        time.sleep(1)  # Wait for content to load

        # Take screenshot
        screenshot = driver.get_screenshot_as_png()
        img = Image.open(io.BytesIO(screenshot))

        # Crop to viewport area (remove any extra if window is larger)
        img = img.crop((0, 0, viewport_width, viewport_height))
        screenshots.append(img)

    # Stitch images vertically
    if screenshots:
        total_width = screenshots[0].width
        stitched_height = sum(img.height for img in screenshots)
        stitched = Image.new('RGB', (total_width, stitched_height))

        y_offset = 0
        for img in screenshots:
            stitched.paste(img, (0, y_offset))
            y_offset += img.height

        stitched.save(output_path)
        print(f"Full page screenshot saved to {output_path}")
    else:
        print("No screenshots captured")

if __name__ == "__main__":
    # Set up Edge driver (using local msedgedriver.exe)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")

    if not os.path.exists(msedgedriver_path):
        print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
        print("Por favor, descargue la versión más reciente desde:")
        print("https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        print("y descomprímalo en esta carpeta")
        exit(1)

    print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")

    options = webdriver.EdgeOptions()
    options.add_argument("--headless")  # Run in headless mode
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--ignore-ssl-errors=yes")
    options.add_argument("--ignore-certificate-errors")
    options.add_argument("--allow-running-insecure-content")
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")

    try:
        driver = webdriver.Edge(service=Service(msedgedriver_path), options=options)
        print("Driver Edge inicializado correctamente")
    except Exception as e:
        print(f"Error de inicialización: {str(e)}")
        exit(1)

    # Test with the provided URL
    url = "https://app.powerbi.com/view?r=eyJrIjoiNzA2ZjEwZmYtMzUyZS00OTBjLThjMTQtNzY3N2NlNDI5YmZiIiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"
    output_path = os.path.join(script_dir, "powerbi_report_full.png")
    take_full_screenshot(driver, url, output_path)

    driver.quit()