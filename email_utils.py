import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
import os
from typing import List, Optional, Dict, Union

def enviar_correo(
    destinatario: Union[str, List[str]],
    asunto: str,
    cuerpo: str,
    remitente: str = '<EMAIL>',
    cc: Optional[Union[str, List[str]]] = None,
    bcc: Optional[Union[str, List[str]]] = None,
    archivos_adjuntos: Optional[List[str]] = None,
    html: bool = False,
    smtp_config: Optional[Dict] = None
) -> Dict:
    """
    Función para enviar correos electrónicos de manera transversal en la aplicación.
    
    Args:
        destinatario: Email o lista de emails de destinatarios
        asunto: Asunto del correo
        cuerpo: Contenido del correo
        remitente: Email del remitente (por defecto: <EMAIL>)
        cc: Email o lista de emails en copia
        bcc: Email o lista de emails en copia oculta
        archivos_adjuntos: Lista de rutas a archivos para adjuntar
        html: Boolean para indicar si el cuerpo es HTML (True) o texto plano (False)
        smtp_config: Diccionario con configuración SMTP personalizada
        
    Returns:
        Dict: Diccionario con resultado de la operación {'exito': bool, 'mensaje': str}
    """
    # Configuración por defecto del servidor SMTP
    default_smtp = {
        'server': 'mail.telqway.cl',
        'port': 465,
        'username': '<EMAIL>',
        'password': 'N1c0l7as17#',
        'use_tls': False,
        'use_ssl': True
    }
    
    # Usar configuración personalizada si se proporciona
    config = smtp_config if smtp_config else default_smtp
    
    # Crear mensaje
    mensaje = MIMEMultipart()
    mensaje['From'] = remitente
    
    # Manejar destinatarios múltiples
    if isinstance(destinatario, list):
        mensaje['To'] = ', '.join(destinatario)
    else:
        mensaje['To'] = destinatario
    
    # Manejar CC
    if cc:
        if isinstance(cc, list):
            mensaje['Cc'] = ', '.join(cc)
        else:
            mensaje['Cc'] = cc
    
    # Manejar BCC (no se agrega al encabezado pero se usa para enviar)
    bcc_list = []
    if bcc:
        if isinstance(bcc, list):
            bcc_list = bcc
        else:
            bcc_list = [bcc]
    
    mensaje['Subject'] = asunto
    
    # Agregar cuerpo del mensaje
    if html:
        mensaje.attach(MIMEText(cuerpo, 'html'))
    else:
        mensaje.attach(MIMEText(cuerpo, 'plain'))
    
    # Agregar archivos adjuntos si existen
    if archivos_adjuntos:
        for archivo in archivos_adjuntos:
            if os.path.exists(archivo):
                with open(archivo, 'rb') as file:
                    part = MIMEApplication(file.read(), Name=os.path.basename(archivo))
                    part['Content-Disposition'] = f'attachment; filename="{os.path.basename(archivo)}"'
                    mensaje.attach(part)
    
    try:
        # Determinar el tipo de conexión (SSL o TLS)
        if config['use_ssl']:
            server = smtplib.SMTP_SSL(config['server'], config['port'])
        else:
            server = smtplib.SMTP(config['server'], config['port'])
            if config['use_tls']:
                server.starttls()
        
        # Iniciar sesión
        server.login(config['username'], config['password'])
        
        # Crear lista completa de destinatarios para el envío
        todos_destinatarios = []
        if isinstance(destinatario, list):
            todos_destinatarios.extend(destinatario)
        else:
            todos_destinatarios.append(destinatario)
            
        if cc:
            if isinstance(cc, list):
                todos_destinatarios.extend(cc)
            else:
                todos_destinatarios.append(cc)
                
        if bcc_list:
            todos_destinatarios.extend(bcc_list)
        
        # Enviar correo
        server.send_message(mensaje, from_addr=remitente, to_addrs=todos_destinatarios)
        server.quit()
        
        return {"exito": True, "mensaje": f"Correo enviado con éxito a {mensaje['To']}"}
    
    except Exception as e:
        return {"exito": False, "mensaje": f"Error al enviar correo: {str(e)}"}