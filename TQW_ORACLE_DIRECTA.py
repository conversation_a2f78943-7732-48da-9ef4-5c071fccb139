
import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, Column, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta
import os 
import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine

from sqlalchemy import text
import sqlalchemy


engine = create_engine('mssql+pyodbc://sa:N1c0l7as@20.20.20.205/master?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@181.212.32.10/master?driver=ODBC+Driver+17+for+SQL+Server')
Session = sessionmaker(bind=engine)
session = Session()




# Data = pd.read_sql_query("SELECT * FROM TB_PASO_ORACLE_DIRECTAX", engine)

# engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

# Data.to_sql('TB_FERRET_DIRECTA1', engineMYsql, if_exists='replace',index=False)


# # Define la sentencia SQL para crear el índice
# sql = text("CREATE INDEX idx_serial ON TB_FERRET_DIRECTA1 (Serial(255));")




# session.close()

# # Ejecuta la sentencia SQL
# with engineMYsql.connect() as connection:
#     connection.execute(sql)

# # Cierra la conexión
# engineMYsql.dispose()




Data = pd.read_sql_query("SELECT * FROM VW_LOGIS_SERIES_ASIGNADAS", engine)

engineMYsql = create_engine('mysql+mysqlconnector://telqwayc_ncornejo:N1c0l7as17@192.140.57.20:3306/telqwayc_db_operacion', echo=False)

Data.to_sql('TB_LOGIS_MOVIMIENTOS', engineMYsql, if_exists='append',index=False)


