-- =====================================================
-- INSTRUCCIONES PARA REORDENAR COLUMNA "Técnico"
-- =====================================================

/*
INSTRUCCIONES PARA EJECUTAR EL SCRIPT reorder_columns.sql:

1. ABRIR SQL Server Management Studio (SSMS)
2. CONECTARSE al servidor: *************
3. SELECCIONAR la base de datos: telqway
4. ABRIR el archivo: reorder_columns.sql
5. EJECUTAR el script completo (F5 o botón Execute)

EL SCRIPT HARÁ LO SIGUIENTE:

✅ PASO 1: Crear tabla nueva con "Técnico" en primera posición
✅ PASO 2: <PERSON>pia<PERSON> todos los datos (463,091 registros)
✅ PASO 3: Verificar que la copia fue exitosa
✅ PASO 4: Eliminar tabla original
✅ PASO 5: Renombrar nueva tabla
✅ PASO 6: Verificar estructura final
✅ PASO 7: Contar registros finales

RESULTADO ESPERADO:
- Columna "Técnico" en primera posición
- Todas las demás columnas en mismo orden
- Todos los datos preservados
- Sin pérdida de información

TIEMPO ESTIMADO: 5-10 minutos
ESPACIO REQUERIDO: ~2GB adicional temporalmente

IMPORTANTE:
- NO cerrar SSMS durante la ejecución
- Esperar a que termine completamente
- Verificar el mensaje final de confirmación
*/

-- =====================================================
-- VERIFICACIÓN POST-EJECUCIÓN
-- =====================================================

-- Ejecutar estas consultas después del script:

-- 1. Verificar orden de columnas:
SELECT TOP 5 COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'tb_toa_reporte_diario' AND TABLE_SCHEMA = 'dbo'
ORDER BY ORDINAL_POSITION;

-- 2. Verificar que "Técnico" es la primera columna:
SELECT COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'tb_toa_reporte_diario' AND TABLE_SCHEMA = 'dbo'
ORDER BY ORDINAL_POSITION;

-- 3. Contar registros totales:
SELECT COUNT(*) as total_registros FROM tb_toa_reporte_diario;

-- 4. Verificar datos más recientes:
SELECT TOP 5 fecha_integracion
FROM tb_toa_reporte_diario
ORDER BY fecha_integracion DESC;

-- =====================================================
-- RESULTADO ESPERADO
-- =====================================================

/*
COLUMN_NAME: Técnico (primera columna)
DATA_TYPE: nvarchar
CHARACTER_MAXIMUM_LENGTH: -1 (NVARCHAR(MAX))

Total registros: 463,091 (sin cambios)
Última fecha: 2025-09-05T14:26:41.303Z
*/