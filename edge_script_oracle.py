from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time
import os
import sys
import logging
import pandas as pd
import pyodbc
import glob

# Configuración de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('edge_script_oracle.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuración de SQL Server
SQL_SERVER_CONFIG = {
    'driver': 'ODBC Driver 17 for SQL Server',
    'server': '************',
    'database': 'telqway',
    'uid': 'ncornejo',
    'pwd': 'N1c0l7as17'
}

# Función para subir archivo Excel a SQL Server
def upload_excel_to_sql_server(excel_file_path, table_name='tb_toa_reporte_diario', mode='replace'):
    """
    Sube un archivo Excel a SQL Server en la tabla especificada

    Args:
        excel_file_path: Ruta completa del archivo Excel
        table_name: Nombre de la tabla destino (default: tb_toa_reporte_diario)

    Returns:
        bool: True si se subió correctamente, False en caso de error
    """
    try:
        print(f"📊 Iniciando carga de Excel a SQL Server...")
        print(f"   - Archivo: {excel_file_path}")
        print(f"   - Tabla destino: {table_name}")

        # Verificar que el archivo existe
        if not os.path.exists(excel_file_path):
            print(f"❌ Error: El archivo {excel_file_path} no existe")
            return False

        # Leer archivo Excel
        print("   - Leyendo archivo Excel...")
        df = pd.read_excel(excel_file_path, engine='openpyxl')

        if df.empty:
            print("❌ Error: El archivo Excel está vacío")
            return False

        print(f"   - Archivo cargado: {len(df)} filas, {len(df.columns)} columnas (Excel original)")
        print(f"   - Se agregará columna 'fecha_integracion' con timestamp actual")

        # Mostrar resumen de mapeo de columnas si hay nombres largos
        long_columns = [col for col in df.columns if len(col) > 128]
        if long_columns:
            print(f"   - ⚠️  {len(long_columns)} columnas con nombres demasiado largos serán renombradas:")
            for i, col in enumerate(long_columns[:5]):  # Mostrar solo las primeras 5
                print(f"     - '{col[:60]}...' → 'col_{i+1}'")
            if len(long_columns) > 5:
                print(f"     - ... y {len(long_columns) - 5} columnas más")

        # Analizar longitudes de datos para determinar tipos de columna
        print("   - Analizando estructura de datos...")
        column_types = {}
        column_mapping = {}  # Mapeo entre nombres originales y nombres SQL

        for col in df.columns:
            # Verificar si el nombre de columna es demasiado largo para SQL Server (límite 128 chars)
            if len(col) > 128:
                # Crear un nombre más corto manteniendo legibilidad
                short_name = f"col_{len(column_mapping) + 1}"
                column_mapping[col] = short_name
                sql_col_name = short_name
                print(f"   - ⚠️  Nombre de columna demasiado largo: '{col[:50]}...' → '{sql_col_name}'")
            else:
                sql_col_name = col
                column_mapping[col] = col

            # Calcular longitud máxima de los datos en esta columna
            non_null_data = df[col].dropna()
            if len(non_null_data) > 0:
                max_length = non_null_data.astype(str).str.len().max()

                # Determinar tipo de columna basado en la longitud
                if max_length <= 255:
                    column_types[sql_col_name] = 'NVARCHAR(1000)'  # Un poco más de margen
                elif max_length <= 4000:
                    column_types[sql_col_name] = 'NVARCHAR(4000)'
                else:
                    column_types[sql_col_name] = 'NTEXT'  # Para textos muy largos
            else:
                column_types[sql_col_name] = 'NVARCHAR(255)'  # Default para columnas vacías

            print(f"   - Columna '{sql_col_name}': {column_types[sql_col_name]} (máx. {max_length if len(non_null_data) > 0 else 0} caracteres)")

        # Agregar columna de timestamp para tracking de integración
        timestamp_col = 'fecha_integracion'
        column_types[timestamp_col] = 'DATETIME'
        column_mapping[timestamp_col] = timestamp_col  # Agregar al mapeo de columnas
        print(f"   - Columna '{timestamp_col}': {column_types[timestamp_col]} (timestamp de integración)")

        # Agregar columna de timestamp al DataFrame
        current_timestamp = pd.Timestamp.now()
        df[timestamp_col] = current_timestamp
        print(f"   - Timestamp de integración: {current_timestamp}")

        # Crear conexión a SQL Server
        print("   - Conectando a SQL Server...")
        conn_str = f"DRIVER={{{SQL_SERVER_CONFIG['driver']}}};SERVER={SQL_SERVER_CONFIG['server']};DATABASE={SQL_SERVER_CONFIG['database']};UID={SQL_SERVER_CONFIG['uid']};PWD={SQL_SERVER_CONFIG['pwd']}"
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        # Verificar si la tabla existe
        cursor.execute(f"SELECT OBJECT_ID('{table_name}', 'U')")
        table_exists = cursor.fetchone()[0] is not None

        if mode == 'replace':
            print(f"   - Modo REPLACE: Recreando tabla {table_name}...")
            # Eliminar tabla si existe
            if table_exists:
                cursor.execute(f"DROP TABLE {table_name}")
                conn.commit()
                print(f"   - Tabla {table_name} eliminada")
            table_exists = False
        else:  # mode == 'append'
            print(f"   - Modo APPEND: Verificando tabla {table_name}...")
            if table_exists:
                print(f"   - Tabla {table_name} existe, se agregarán datos")
            else:
                print(f"   - Tabla {table_name} no existe, se creará")

        # Crear la tabla si no existe
        if not table_exists:
            print(f"   - Creando tabla {table_name}...")

            # Crear nueva tabla con tipos de columna apropiados
            columns_def = []
            for col, col_type in column_types.items():
                columns_def.append(f"[{col}] {col_type}")

            create_table_sql = f"CREATE TABLE {table_name} ({', '.join(columns_def)})"
            print(f"   - SQL de creación: {create_table_sql}")
            cursor.execute(create_table_sql)
            conn.commit()

            print("   - Tabla creada exitosamente")
        else:
            print(f"   - Tabla {table_name} ya existe, omitiendo creación")

        # Preparar inserción usando los nombres de columna SQL
        sql_columns = [column_mapping[col] for col in df.columns]
        columns_str = ", ".join([f"[{col}]" for col in sql_columns])
        placeholders = ", ".join(["?" for _ in sql_columns])

        insert_query = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
        print(f"   - Query de inserción preparada con {len(sql_columns)} columnas")

        # Insertar datos por lotes
        batch_size = 500  # Reducido para evitar problemas de memoria
        total_rows = len(df)
        total_batches = (total_rows + batch_size - 1) // batch_size

        print(f"   - Insertando {total_rows} registros en {total_batches} lotes...")

        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]

            # Preparar valores
            values = []
            for _, row in batch_df.iterrows():
                row_values = []
                for col_name, val in row.items():
                    if pd.isna(val):
                        row_values.append(None)
                    elif col_name == timestamp_col:
                        # Para la columna timestamp, mantener el formato datetime
                        if isinstance(val, pd.Timestamp):
                            row_values.append(val)
                        else:
                            row_values.append(pd.Timestamp(val))
                    else:
                        # Para otras columnas, convertir a string
                        str_val = str(val)
                        # Para columnas NTEXT, no hay límite práctico
                        # Para NVARCHAR, SQL Server manejará el truncamiento automáticamente si es necesario
                        row_values.append(str_val)
                values.append(tuple(row_values))

            # Ejecutar inserción con manejo de errores
            try:
                cursor.executemany(insert_query, values)
                conn.commit()
                print(f"   - Lote {i+1}/{total_batches} completado ({len(values)} registros)")
            except Exception as batch_error:
                print(f"   - Error en lote {i+1}: {str(batch_error)}")
                # Intentar insertar fila por fila para identificar el problema
                print("   - Intentando inserción fila por fila...")
                successful_inserts = 0
                for j, row_data in enumerate(values):
                    try:
                        cursor.execute(insert_query, row_data)
                        conn.commit()
                        successful_inserts += 1
                    except Exception as row_error:
                        print(f"     - Error en fila {j+1}: {str(row_error)}")
                        # Continuar con la siguiente fila
                        continue

                print(f"   - Insertadas {successful_inserts}/{len(values)} filas del lote problemático")

        # Cerrar conexiones
        cursor.close()
        conn.close()

        print("✅ Carga completada exitosamente!")
        print(f"   - Total registros procesados: {total_rows}")
        print(f"   - Columna 'fecha_integracion' agregada con timestamp: {current_timestamp}")

        # Mostrar resumen del mapeo de columnas si aplica
        renamed_columns = [col for col in df.columns if column_mapping.get(col) != col]
        if renamed_columns:
            print(f"   - 📝 {len(renamed_columns)} columnas renombradas por límite de longitud SQL Server")
            print("   - Los nombres originales se mantendrán en el DataFrame, pero se usarán nombres cortos en SQL")

        return True

    except Exception as e:
        print(f"❌ Error al subir Excel a SQL Server: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Función para encontrar el archivo Excel más reciente en la carpeta de descargas
def find_latest_excel_file(download_dir=None, pattern="*.xlsx", max_age_minutes=15, min_timestamp=None):
    """
    Encuentra el archivo Excel más reciente en el directorio de descargas
    que haya sido creado/modificado dentro de los últimos X minutos

    Args:
        download_dir: Directorio de descargas (si no se especifica, usa el predeterminado)
        pattern: Patrón de búsqueda de archivos
        max_age_minutes: Edad máxima del archivo en minutos (solo archivos recientes)

    Returns:
        str: Ruta del archivo más reciente dentro del período o None si no se encuentra
    """
    try:
        if download_dir is None:
            # Usar directorio de descargas predeterminado
            if os.name == 'nt':  # Windows
                download_dir = os.path.join(os.path.expanduser("~"), "Downloads")
            else:  # Linux/Mac
                download_dir = os.path.join(os.path.expanduser("~"), "Downloads")

        print(f"🔍 Buscando archivos Excel en: {download_dir}")
        print(f"   - Patrón: {pattern}")
        print(f"   - Buscando archivos de los últimos {max_age_minutes} minutos")
        if min_timestamp:
            print(f"   - Solo archivos más nuevos que: {time.ctime(min_timestamp)}")

        # Buscar archivos que coincidan con el patrón
        excel_files = glob.glob(os.path.join(download_dir, pattern))

        if not excel_files:
            print("   - No se encontraron archivos Excel")
            return None

        # Filtrar archivos por edad máxima (solo archivos recientes)
        current_time = time.time()
        max_age_seconds = max_age_minutes * 60
        recent_files = []

        print(f"   - Revisando {len(excel_files)} archivos Excel encontrados...")

        for file_path in excel_files:
            try:
                file_mtime = os.path.getmtime(file_path)
                file_age_seconds = current_time - file_mtime
                file_age_minutes = file_age_seconds / 60

                # Solo considerar archivos creados/modificados en los últimos X minutos
                # Y más nuevos que el timestamp mínimo si se especifica
                age_filter = file_age_seconds <= max_age_seconds
                timestamp_filter = min_timestamp is None or file_mtime > min_timestamp

                if age_filter and timestamp_filter:
                    recent_files.append((file_path, file_age_seconds))
                    print(f"   - ✅ Archivo válido: {os.path.basename(file_path)} (edad: {file_age_minutes:.1f} min)")
                elif not age_filter:
                    print(f"   - ⏰ Archivo antiguo: {os.path.basename(file_path)} (edad: {file_age_minutes:.1f} min)")
                elif not timestamp_filter:
                    print(f"   - 📅 Archivo anterior al límite: {os.path.basename(file_path)} (edad: {file_age_minutes:.1f} min)")

            except Exception as e:
                print(f"   - Error al verificar archivo {os.path.basename(file_path)}: {str(e)}")

        if not recent_files:
            print(f"   - No se encontraron archivos Excel creados en los últimos {max_age_minutes} minutos")
            print("   - Posibles causas:")
            print("     • La descarga aún no ha terminado")
            print("     • El archivo se guardó en una ubicación diferente")
            print("     • El patrón de búsqueda no coincide")
            return None

        # Encontrar el archivo más reciente entre los archivos recientes
        latest_file, file_age_seconds = min(recent_files, key=lambda x: x[1])  # El más reciente (menor edad)
        file_time = time.ctime(os.path.getmtime(latest_file))
        file_size = os.path.getsize(latest_file)
        file_age_minutes = file_age_seconds / 60

        print(f"   - 🎯 Archivo seleccionado: {os.path.basename(latest_file)}")
        print(f"   - 📅 Última modificación: {file_time}")
        print(f"   - 📏 Tamaño: {file_size} bytes")
        print(f"   - ⏱️  Edad: {file_age_minutes:.1f} minutos")
        print(f"   - 📂 Ubicación: {os.path.dirname(latest_file)}")

        return latest_file

    except Exception as e:
        print(f"❌ Error al buscar archivo Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# Mostrar banner de inicio
start_time = time.time()
print("="*80)
print(f"INICIANDO SCRIPT DE NAVEGACIÓN A ORACLE CLOUD - {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*80)

def run_oracle_navigation():
    # Contador de ciclos
    cycle_count = 0

    try:
        # Configuración de opciones para Edge
        print("1. Configurando navegador...")
        edge_options = Options()
        edge_options.add_argument('--start-maximized')
        edge_options.add_argument('--disable-gpu')
        edge_options.add_argument('--no-sandbox')
        edge_options.add_experimental_option('excludeSwitches', ['enable-logging'])

        # Usar el driver local en vez de la descarga automática
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")

            if not os.path.exists(msedgedriver_path):
                print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
                print("Por favor, asegúrese de tener el driver de Edge en la misma carpeta que este script")
                sys.exit(1)

            print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
            service = Service(msedgedriver_path)
            driver = webdriver.Edge(service=service, options=edge_options)
            print("Driver Edge inicializado correctamente")
        except Exception as e:
            print(f"Error al inicializar msedgedriver local: {str(e)}")
            sys.exit(1)
    except Exception as e:
        print(f"Error de inicialización del driver: {str(e)}")
        sys.exit(1)

    # CICLO PERMANENTE
    while True:
        try:
            cycle_count += 1
            cycle_start_time = time.time()

            print("\n" + "="*100)
            print(f"🚀 INICIANDO CICLO #{cycle_count} - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("="*100)
            print("📊 Ciclo permanente: Centro → Metropolitana → Centro → Metropolitana...")
            print("⏹️  Para detener: Ctrl+C en la consola")
            print("="*100 + "\n")

            # Configurar tiempo de espera para elementos
            wait = WebDriverWait(driver, 15)  # Aumentado a 15 segundos
            
            # Navegar a Oracle Cloud
            print("2. Navegando a Oracle Cloud...")
            try:
                # URL específica de Oracle Cloud
                oracle_url = "https://vtr.fs.ocs.oraclecloud.com/"
                print(f"   - Accediendo a: {oracle_url}")
                driver.get(oracle_url)
                
                # Esperar a que la página cargue
                print("   - Esperando que la página de Oracle Cloud cargue completamente...")
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                
                # Verificar título de la página
                titulo = driver.title
                print(f"   - Página de Oracle Cloud cargada. Título: {titulo}")
                
                # VERIFICAR SI YA ESTAMOS EN LA ÚLTIMA ETAPA
                print("   - Verificando si ya estamos en la sesión de Oracle Cloud...")
                try:
                    # Verificar específicamente si el elemento con ID elId50 está presente (indica que ya estamos en la etapa final de Oracle Cloud)
                    elemento_indicador = WebDriverWait(driver, 10).until(
                        EC.visibility_of_element_located((By.ID, "elId50")))
                    print("   - Elemento con ID 'elId50' encontrado - Sesión activa en etapa final confirmada")
                except Exception:
                    # Si no se encuentra el elemento elId50, verificar otras alternativas
                    try:
                        # Intento alternativo con elId1540 (criterio anterior)
                        elemento_indicador = WebDriverWait(driver, 5).until(
                            EC.visibility_of_element_located((By.ID, "elId1540")))
                        print("   - Elemento con ID 'elId1540' encontrado, pero no es el elemento principal buscado")
                        # No consideramos esto como sesión activa en la etapa final
                        raise Exception("No se encontró el elemento específico elId50")
                    except Exception:
                        # Si no se encuentra elId1540, verificar título como última alternativa
                        if "Oracle Field Service" in driver.title:
                            print("   - Título 'Oracle Field Service' detectado, pero no el elemento específico elId50")
                            # No consideramos esto como sesión activa en la etapa final
                            raise Exception("No se encontró el elemento específico elId50")
                        else:
                            # Verificar si hay algún otro elemento característico de Oracle Cloud
                            try:
                                # Intentar encontrar alguna característica de la UI
                                elemento_indicador = WebDriverWait(driver, 5).until(
                                    EC.presence_of_element_located((By.CSS_SELECTOR, ".oj-web-applayout-header-title")))
                                print("   - Elemento UI detectado, pero no el elemento específico elId50")
                                # No consideramos esto como sesión activa en la etapa final
                                raise Exception("No se encontró el elemento específico elId50")
                            except Exception:
                                # Si nada funciona, lanzar excepción para continuar con flujo normal
                                raise Exception("No se detectó sesión activa por ningún método")
            
            print("\n" + "="*80)
            print("¡SESIÓN ACTIVA EN ETAPA FINAL DETECTADA!")
            print("Se detectó el elemento 'elId50' que confirma que estamos en la etapa final de Oracle Cloud.")
            print("Saltando directamente a la etapa final.")
            print("="*80 + "\n")
            
            # Tomar screenshot de la sesión activa
            driver.save_screenshot("session_already_active.png")
            
            # Confirmación de navegación exitosa por sesión activa detectada
            print("9. Navegación a Oracle Cloud completada exitosamente (elemento elId50 detectado)")
            print("   - Navegador abierto y listo para uso.")
            
            # PASO 10: Interactuar con el elemento elId50 ya detectado
            print("10. Interactuando con el elemento elId50 detectado...")
            try:
                # Dado que ya detectamos el elemento, no necesitamos buscarlo de nuevo
                print("   - Haciendo clic en el elemento elId50 previamente detectado...")
                try:
                    elemento_indicador.click()
                    print("   - Clic realizado correctamente en el elemento elId50")
                except Exception as e:
                    print(f"   - Error al hacer clic normal: {str(e)}")
                    # Intentar con JavaScript como alternativa
                    try:
                        print("   - Intentando hacer clic con JavaScript...")
                        driver.execute_script("arguments[0].click();", elemento_indicador)
                        print("   - Clic realizado con JavaScript en elemento elId50")
                    except Exception as js_e:
                        print(f"   - Error también con JavaScript: {str(js_e)}")
                
                # Tomar screenshot después de hacer clic
                driver.save_screenshot("after_click_elId50_session_active.png")
                print("   - Interacción con elemento elId50 completada")
            except Exception as e:
                print(f"   - ERROR: No se pudo interactuar con el elemento elId50: {str(e)}")
                driver.save_screenshot("error_elId50_session_active.png")
                print("   - Continuando con el flujo a pesar del error")
    
                print("   - El navegador NO se cerrará automáticamente.")
    
                # Ir directamente al bloque finally para mantener el navegador abierto
                return
            
        except Exception as e:
            # No se encontró el elemento, lo que significa que no estamos en la sesión
            print("   - No se detectó sesión activa, procediendo con el inicio de sesión normal.")
        
        # PASO 1: Hacer clic en "Conectarse con SSO"
        print("3. Haciendo clic en 'Conectarse con SSO'...")
        try:
            sso_button = wait.until(EC.element_to_be_clickable((By.ID, "sign-in-with-sso")))
            sso_button.click()
            print("   - Botón 'Conectarse con SSO' clickeado correctamente")
            
            # PASO 2: Ingresar nombre de usuario para SSO
            print("4. Ingresando nombre de usuario para SSO...")
            # Esperar a que la página se cargue completamente
            time.sleep(3)  # Espera para asegurar que la página ha cambiado
            
            # Incrementar el tiempo de espera específicamente para este paso
            username_wait = WebDriverWait(driver, 30)  # Espera más larga para este elemento crítico
            
            # Esperar a que la URL cambie completamente (puede tardar más tiempo)
            print("   - Esperando a que la página de SSO cargue completamente...")
            try:
                # Esperar a que cambie la URL (suele ser un indicador de carga completa)
                old_url = driver.current_url
                WebDriverWait(driver, 10).until(lambda d: d.current_url != old_url)
                print(f"   - URL cambiada de {old_url} a {driver.current_url}")
            except Exception as e:
                print(f"   - No se detectó cambio en la URL, continuando de todas formas: {str(e)}")
            
            # Dar tiempo adicional para que los elementos se carguen completamente
            time.sleep(5)  # Espera adicional después de que la página cambie
            
            # Imprimir la URL actual para verificar que estamos en la página correcta
            print(f"   - URL actual: {driver.current_url}")
            
            # Tomar screenshot antes de buscar el campo
            driver.save_screenshot("before_username_field.png")
            
            # Intentar diferentes métodos para encontrar el campo de usuario
            try:
                # Primero intentar por ID correcto: sso_username
                print("   - Intentando encontrar el campo de usuario por ID (sso_username)...")
                username_input = username_wait.until(EC.visibility_of_element_located((By.ID, "sso_username")))
                print("   - Campo de usuario encontrado por ID sso_username")
            except Exception as e:
                print(f"   - No se encontró por ID sso_username: {str(e)}")
                try:
                    # Intentar por CSS selector con ID correcto
                    print("   - Intentando encontrar el campo de usuario por CSS selector...")
                    username_input = username_wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, "input#sso_username")))
                    print("   - Campo de usuario encontrado por CSS selector")
                except Exception as e:
                    print(f"   - No se encontró por CSS selector: {str(e)}")
                    try:
                        # Intentar por nombre (atributo name)
                        print("   - Intentando encontrar el campo de usuario por NAME...")
                        username_input = username_wait.until(EC.visibility_of_element_located((By.NAME, "sso_username")))
                        print("   - Campo de usuario encontrado por NAME")
                    except Exception as e:
                        print(f"   - No se encontró por NAME: {str(e)}")
                        try:
                            # Intentar con ID alternativo (por si acaso)
                            print("   - Intentando encontrar el campo de usuario por ID alternativo (username)...")
                            username_input = username_wait.until(EC.visibility_of_element_located((By.ID, "username")))
                            print("   - Campo de usuario encontrado por ID alternativo")
                        except Exception as e:
                            print(f"   - No se encontró por ID alternativo: {str(e)}")
                            try:
                                # Intentar por XPath para input con placeholder
                                print("   - Intentando encontrar el campo de usuario por XPath con placeholder...")
                                username_input = username_wait.until(EC.visibility_of_element_located(
                                    (By.XPATH, "//input[@placeholder='Nombre de usuario']")))
                                print("   - Campo de usuario encontrado por XPath con placeholder")
                            except Exception as e:
                                print(f"   - No se encontró por XPath con placeholder: {str(e)}")
                                # Último intento: cualquier input visible
                                print("   - Intentando encontrar cualquier input en la página...")
                                username_input = username_wait.until(EC.visibility_of_element_located((By.TAG_NAME, "input")))
                                print("   - Se encontró al menos un campo input")
                        
            # Tomar screenshot para debug
            driver.save_screenshot("username_field.png")
            
            # Verificar que el campo es interactuable
            print("   - Verificando que el campo de usuario es interactuable...")
            
            # Pausa breve antes de interactuar con el elemento
            time.sleep(1)
            
            # Limpiar e ingresar usuario con JS como método alternativo
            try:
                username_input.clear()
                print("   - Campo de usuario limpiado correctamente")
            except Exception as e:
                print(f"   - Error al limpiar el campo de usuario: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    driver.execute_script("arguments[0].value = '';", username_input)
                    print("   - Campo de usuario limpiado con JavaScript")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
            
            try:
                username_input.send_keys("ncornejoh")
                print("   - Nombre de usuario ingresado correctamente")
            except Exception as e:
                print(f"   - Error al ingresar nombre de usuario: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    driver.execute_script("arguments[0].value = 'ncornejoh';", username_input)
                    print("   - Nombre de usuario ingresado con JavaScript")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
                    
            # Tomar screenshot después de la interacción
            driver.save_screenshot("after_username_input.png")
            print("   - Nombre de usuario ingresado")
            
            # Hacer clic en "Continuar con SSO"
            print("   - Buscando botón 'Continuar con SSO'...")
            # Incrementar tiempo de espera para este botón también
            button_wait = WebDriverWait(driver, 20)
            
            # Tomar screenshot antes de buscar el botón
            driver.save_screenshot("before_continue_button.png")
            
            try:
                # Intentar por ID
                print("   - Intentando encontrar el botón por ID...")
                continue_button = button_wait.until(EC.element_to_be_clickable((By.ID, "signin-with-sso-button")))
                print("   - Botón encontrado por ID")
            except Exception as e:
                print(f"   - No se encontró el botón por ID signin-with-sso-button: {str(e)}")
                try:
                    # Intentar ID alternativo
                    print("   - Intentando encontrar el botón por ID alternativo...")
                    continue_button = button_wait.until(EC.element_to_be_clickable((By.ID, "continue-with-sso")))
                    print("   - Botón encontrado por ID alternativo")
                except Exception as e:
                    print(f"   - No se encontró el botón por ID alternativo: {str(e)}")
                    try:
                        # Intentar por CSS selector
                        print("   - Intentando encontrar el botón por CSS selector...")
                        continue_button = button_wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']")))
                        print("   - Botón encontrado por CSS selector")
                    except Exception as e:
                        print(f"   - No se encontró el botón por CSS selector: {str(e)}")
                        try:
                            # Intentar por texto visible
                            print("   - Intentando encontrar el botón por texto visible...")
                            continue_button = button_wait.until(EC.element_to_be_clickable(
                                (By.XPATH, "//button[contains(text(), 'Continuar con SSO')]")))
                            print("   - Botón encontrado por texto visible")
                        except Exception as e:
                            print(f"   - No se encontró el botón por texto visible: {str(e)}")
                            # Último intento: por tipo de botón
                            print("   - Intentando encontrar el botón por tipo submit...")
                            continue_button = button_wait.until(EC.element_to_be_clickable(
                                (By.XPATH, "//button[@type='submit']")))
                            print("   - Botón encontrado por tipo submit")
            
            # Tomar screenshot para debug
            driver.save_screenshot("continue_button.png")
            
            # Intentar clic con diferentes métodos para mayor seguridad
            try:
                print("   - Intentando hacer clic en el botón...")
                continue_button.click()
                print("   - Botón 'Continuar con SSO' clickeado correctamente")
            except Exception as e:
                print(f"   - Error al hacer clic normal: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    print("   - Intentando hacer clic con JavaScript...")
                    driver.execute_script("arguments[0].click();", continue_button)
                    print("   - Botón 'Continuar con SSO' clickeado con JavaScript")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
                    # Intentar con Actions
                    try:
                        print("   - Intentando hacer clic con Actions...")
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(continue_button).click().perform()
                        print("   - Botón 'Continuar con SSO' clickeado con Actions")
                    except Exception as act_e:
                        print(f"   - Error también con Actions: {str(act_e)}")
                        # Último recurso: Enter en el campo de usuario
                        print("   - Intentando enviar Enter en el campo de usuario...")
                        try:
                            username_input.send_keys(Keys.ENTER)
                            print("   - Se envíó Enter en el campo de usuario como alternativa")
                        except Exception as key_e:
                            print(f"   - Error al enviar Enter: {str(key_e)}")
            
            # Tomar screenshot después del intento de clic
            driver.save_screenshot("after_continue_button.png")
            print("   - Botón 'Continuar con SSO' procesado")
            
            # PASO 3: Microsoft Login - Ingresar correo directamente
            print("5. Procesando página de Microsoft Login...")
            # Esperar a que se cargue la página de Microsoft
            time.sleep(8)  # Espera más larga para garantizar la carga completa
            
            # Tomar screenshot para debug
            driver.save_screenshot("microsoft_login.png")
            
            # Esperar explicitamente hasta detectar algún elemento de Microsoft
            try:
                # Esperar a que aparezca algún elemento de Microsoft (logo, título, etc.)
                wait.until(lambda d: "microsoftonline.com" in d.current_url)
                print("   - Página de Microsoft detectada con éxito")
            except Exception as e:
                print(f"   - No se detectó la URL de Microsoft, pero continuando... {str(e)}")
                
            # Esperar un poco más para asegurar carga completa
            time.sleep(5)  # Incrementado para dar más tiempo de carga
            
            # Tomar captura para ver la página actual
            driver.save_screenshot("before_email_input.png")
            print(f"   - URL actual: {driver.current_url}")
            
            # Buscar directamente el campo de correo electrónico
            print("   - Buscando campo para ingresar correo electrónico...")
            email_wait = WebDriverWait(driver, 25)  # Tiempo de espera extenso para el campo de correo
            
            try:
                # Intentar encontrar el campo de correo por varios métodos
                try:
                    # Por ID común en Microsoft
                    print("   - Intentando encontrar campo de correo por ID...")
                    email_input = email_wait.until(EC.visibility_of_element_located((By.ID, "i0116")))
                    print("   - Campo de correo encontrado por ID")
                except Exception as e:
                    print(f"   - No se encontró campo por ID i0116: {str(e)}")
                    try:
                        # Por NAME
                        print("   - Intentando encontrar campo de correo por NAME...")
                        email_input = email_wait.until(EC.visibility_of_element_located((By.NAME, "loginfmt")))
                        print("   - Campo de correo encontrado por NAME")
                    except Exception as e:
                        print(f"   - No se encontró campo por NAME loginfmt: {str(e)}")
                        try:
                            # Por placeholder
                            print("   - Intentando encontrar campo de correo por placeholder...")
                            email_input = email_wait.until(EC.visibility_of_element_located(
                                (By.XPATH, "//input[@placeholder='Email, teléfono o Skype']")))
                            print("   - Campo de correo encontrado por placeholder")
                        except Exception as e:
                            print(f"   - No se encontró campo por placeholder: {str(e)}")
                            # Último intento - cualquier input de tipo email o text
                            print("   - Buscando cualquier campo de input válido...")
                            inputs = driver.find_elements(By.TAG_NAME, "input")
                            email_input = None
                            for inp in inputs:
                                try:
                                    input_type = inp.get_attribute("type")
                                    if input_type in ["email", "text"] and inp.is_displayed():
                                        email_input = inp
                                        print(f"   - Encontrado input genérico de tipo: {input_type}")
                                        break
                                except:
                                    continue
                            
                            if email_input is None:
                                raise Exception("No se encontró ningún campo de entrada válido")
                
                # Tomar screenshot después de encontrar el campo
                driver.save_screenshot("found_email_field.png")
                
                # Limpiar e ingresar el correo
                print("   - Ingresando correo electrónico...")
                try:
                    email_input.clear()
                    email_input.send_keys("<EMAIL>")
                    print("   - Correo electrónico ingresado")
                except Exception as e:
                    print(f"   - Error al ingresar correo normal: {str(e)}")
                    try:
                        # Intentar con JavaScript
                        driver.execute_script("arguments[0].value = '<EMAIL>';", email_input)
                        print("   - Correo electrónico ingresado con JavaScript")
                    except Exception as js_e:
                        print(f"   - Error también con JavaScript: {str(js_e)}")
                
                # Tomar screenshot después de ingresar el correo
                driver.save_screenshot("after_email_input.png")
                
                # Hacer clic en Siguiente o enviar Enter
                try:
                    # Buscar botón Siguiente/Next
                    print("   - Buscando botón para continuar...")
                    try:
                        # Por ID
                        next_button = email_wait.until(EC.element_to_be_clickable((By.ID, "idSIButton9")))
                        next_button.click()
                        print("   - Botón Siguiente clickeado")
                    except Exception as e:
                        print(f"   - No se encontró botón por ID: {str(e)}")
                        try:
                            # Por texto
                            next_button = email_wait.until(EC.element_to_be_clickable(
                                (By.XPATH, "//input[@value='Siguiente']|//input[@value='Next']")))
                            next_button.click()
                            print("   - Botón Siguiente clickeado por texto")
                        except Exception as e:
                            print(f"   - No se encontró botón por texto: {str(e)}")
                            # Enviar Enter en el campo de correo
                            print("   - Enviando Enter en el campo de correo...")
                            email_input.send_keys(Keys.ENTER)
                            print("   - Enter enviado en el campo de correo")
                except Exception as e:
                    print(f"   - Error al hacer clic en Siguiente: {str(e)}")
                    # Último intento con Enter
                    try:
                        email_input.send_keys(Keys.ENTER)
                        print("   - Enter enviado como último recurso")
                    except:
                        print("   - No se pudo enviar Enter")
                
                # Tomar screenshot después de hacer clic
                driver.save_screenshot("after_email_next.png")
                
            except Exception as e:
                print(f"   - Error al procesar el campo de correo: {str(e)}")
                driver.save_screenshot("error_email_page.png")
                
            # Espera adicional después de ingresar correo
            time.sleep(5)
            
            # PASO 4: Ingresar contraseña
            print("6. Ingresando contraseña...")
            # Esperar un momento para que aparezca el campo de contraseña
            time.sleep(3)
            
            # Tomar captura antes de buscar el campo de contraseña
            driver.save_screenshot("before_password.png")
            
            try:
                # Intentar por ID (más común en Microsoft)
                password_input = wait.until(EC.visibility_of_element_located((By.ID, "i0118")))
            except Exception:
                try:
                    # Intentar por tipo de input
                    password_input = wait.until(EC.visibility_of_element_located((By.XPATH, "//input[@type='password']")))
                except Exception:
                    try:
                        # Intentar por nombre
                        password_input = wait.until(EC.visibility_of_element_located((By.NAME, "passwd")))
                    except Exception:
                        # Último intento: cualquier input visible (que no sea el email)
                        all_inputs = driver.find_elements(By.TAG_NAME, "input")
                        for input_elem in all_inputs:
                            try:
                                if input_elem.get_attribute("type") == "password" or \
                                   "password" in input_elem.get_attribute("id").lower() or \
                                   "password" in input_elem.get_attribute("name").lower():
                                    password_input = input_elem
                                    break
                            except:
                                continue
                        if not password_input:
                            # Si no se encuentra ningún campo de contraseña, tomar un último screenshot
                            driver.save_screenshot("no_password_field.png")
                            raise Exception("No se pudo encontrar el campo de contraseña")
            
            password_input.clear()
            password_input.send_keys("Telqway.202517")
            print("   - Contraseña ingresada")
            
            # Hacer clic en Sign in
            try:
                # Tomar captura antes de buscar el botón
                driver.save_screenshot("before_signin.png")
                
                # Intentar varias estrategias para encontrar el botón
                try:
                    # Por tipo submit
                    sign_in_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='submit']")))
                except Exception:
                    try:
                        # Por ID (si está disponible)
                        sign_in_button = wait.until(EC.element_to_be_clickable((By.ID, "idSIButton9")))
                    except Exception:
                        try:
                            # Por texto del botón
                            sign_in_button = wait.until(EC.element_to_be_clickable(
                                (By.XPATH, "//button[contains(text(), 'Sign in')]")))
                        except Exception:
                            # Último intento: cualquier botón o input tipo submit
                            buttons = driver.find_elements(By.TAG_NAME, "button")
                            for button in buttons:
                                if "sign in" in button.text.lower() or "iniciar" in button.text.lower():
                                    sign_in_button = button
                                    break
                            
                            if not sign_in_button:
                                inputs = driver.find_elements(By.XPATH, "//input[@type='submit']")
                                if inputs:
                                    sign_in_button = inputs[0]
                                else:
                                    # Si no se encuentra botón, usar Enter en el campo de contraseña
                                    driver.save_screenshot("no_signin_button.png")
                                    print("   - No se encontró botón de inicio, enviando Enter en el campo de contraseña")
                                    password_input.send_keys(Keys.ENTER)
                                    sign_in_button = None
                
                if sign_in_button:
                    sign_in_button.click()
                    
                print("   - Botón 'Sign in' clickeado correctamente")
                
            except Exception as e:
                print(f"   - Error al hacer clic en Sign in: {str(e)}")
                driver.save_screenshot("signin_error.png")
                # Intentar continuar a pesar del error
                try:
                    password_input.send_keys(Keys.ENTER)
                except:
                    pass
            
            # PASO 5: Verificar si aparece el diálogo "Stay signed in?"
            print("7. Verificando diálogo 'Stay signed in'...")
            time.sleep(5)  # Espera para que aparezca el diálogo si existe
            
            try:
                # Buscar botón "Yes" o "Sí"
                yes_button = driver.find_element(By.XPATH, "//input[@value='Yes']")
                yes_button.click()
                print("   - Se hizo clic en 'Yes' en el diálogo 'Stay signed in'")
            except Exception as e:
                print("   - No se encontró diálogo 'Stay signed in', continuando...")
            
            # PASO 6: Esperar a que se cargue Oracle Cloud completamente
            print("8. Esperando carga completa de Oracle Cloud...")
            wait.until(EC.title_contains("Oracle Field Service"))
            time.sleep(5)  # Espera adicional para asegurar carga completa
            
            print("   - Oracle Cloud cargado completamente")
            print("   - Login con SSO completado exitosamente")
            
        except Exception as e:
            print(f"   - ERROR en proceso de autenticación: {str(e)}")
            driver.save_screenshot("error_auth.png")
            raise
        
        # Confirmación de navegación exitosa
        print("9. Navegación a Oracle Cloud completada exitosamente")
        print("   - Navegador abierto y listo para uso.")
        
        # PASO 10: Identificar y hacer clic en el elemento con ID elId50
        print("10. Buscando e interactuando con el elemento elId50...")
        try:
            # Esperar a que el elemento elId50 esté visible
            print("   - Buscando el elemento con ID 'elId50'...")
            elemento_elId50 = WebDriverWait(driver, 15).until(
                EC.visibility_of_element_located((By.ID, "elId50")))
            
            # Tomar screenshot antes de hacer clic
            driver.save_screenshot("before_click_elId50.png")
            
            # Hacer clic en el elemento
            print("   - Elemento elId50 encontrado. Haciendo clic...")
            try:
                elemento_elId50.click()
                print("   - Clic realizado correctamente en el elemento elId50")
            except Exception as e:
                print(f"   - Error al hacer clic normal: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    print("   - Intentando hacer clic con JavaScript...")
                    driver.execute_script("arguments[0].click();", elemento_elId50)
                    print("   - Clic realizado con JavaScript en elemento elId50")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
            
            # Tomar screenshot después de hacer clic
            driver.save_screenshot("after_click_elId50.png")
            print("   - Interacción con elemento elId50 completada")
            
        except Exception as e:
            print(f"   - ERROR: No se pudo encontrar o interactuar con el elemento elId50: {str(e)}")
            driver.save_screenshot("error_elId50.png")
            print("   - Continuando con el flujo a pesar del error")

        # PASO 13: Buscar e interactuar con el elemento "Acciones"
        print("13. Buscando e interactuando con el elemento 'Acciones'...")
        try:
            # Esperar adicional para asegurar que la página esté completamente cargada
            print("   - Esperando que la página esté completamente cargada...")
            time.sleep(3)  # Espera adicional antes de buscar elementos

            # Tomar screenshot antes de buscar el elemento
            driver.save_screenshot("before_search_acciones.png")

            # Múltiples estrategias para encontrar el botón "Acciones"
            acciones_button = None

            # Estrategia 1: XPath por texto (original)
            try:
                print("   - Buscando por XPath con texto 'Acciones'...")
                acciones_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Acciones')]"))
                )
                print("   - Elemento encontrado por XPath con texto")
            except Exception as e:
                print(f"   - No encontrado por XPath texto: {str(e)}")

                # Estrategia 2: Buscar por clase o atributos específicos
                try:
                    print("   - Buscando por selectores CSS más específicos...")
                    # Buscar botones que contengan "Acciones" en cualquier atributo
                    all_buttons = driver.find_elements(By.TAG_NAME, "button")
                    for btn in all_buttons:
                        try:
                            btn_text = btn.text.strip()
                            if "Acciones" in btn_text or "acciones" in btn_text.lower():
                                if btn.is_displayed() and btn.is_enabled():
                                    acciones_button = btn
                                    print(f"   - Elemento encontrado por búsqueda de texto: '{btn_text}'")
                                    break
                        except:
                            continue
                except Exception as e:
                    print(f"   - Error en búsqueda por texto: {str(e)}")

                # Estrategia 3: Buscar por data attributes o aria labels
                if not acciones_button:
                    try:
                        print("   - Buscando por atributos data-* o aria-*...")
                        acciones_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, "[aria-label*='Acciones'], [data-tooltip*='Acciones'], button[title*='Acciones']"))
                        )
                        print("   - Elemento encontrado por atributos data/aria")
                    except Exception as e:
                        print(f"   - No encontrado por atributos: {str(e)}")

                # Estrategia 4: Buscar en el header/navigation area específicamente
                if not acciones_button:
                    try:
                        print("   - Buscando en área de navegación/header...")
                        header_buttons = driver.find_elements(By.CSS_SELECTOR, "header button, nav button, .navbar button, .header button")
                        for btn in header_buttons:
                            try:
                                if btn.is_displayed() and btn.is_enabled():
                                    btn_text = btn.text.strip()
                                    if "Acciones" in btn_text or "acciones" in btn_text.lower():
                                        acciones_button = btn
                                        print(f"   - Elemento encontrado en header: '{btn_text}'")
                                        break
                            except:
                                continue
                    except Exception as e:
                        print(f"   - Error en búsqueda header: {str(e)}")

            # Verificar si se encontró el elemento
            if not acciones_button:
                raise Exception("No se pudo encontrar el botón 'Acciones' con ninguna estrategia")

            # Tomar screenshot antes de hacer clic
            driver.save_screenshot("before_click_acciones.png")

            # Hacer clic en el elemento "Acciones"
            print("   - Elemento 'Acciones' encontrado. Haciendo clic...")
            try:
                # Scroll al elemento antes de hacer clic
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", acciones_button)
                time.sleep(1)  # Pequeña pausa después del scroll

                acciones_button.click()
                print("   - Clic realizado correctamente en el elemento 'Acciones'")
            except Exception as e:
                print(f"   - Error al hacer clic normal: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    print("   - Intentando hacer clic con JavaScript...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'}); arguments[0].click();", acciones_button)
                    print("   - Clic realizado con JavaScript en elemento 'Acciones'")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
                    # Intentar con Actions como último recurso
                    try:
                        print("   - Intentando con Actions (move and click)...")
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(acciones_button).pause(1).click().perform()
                        print("   - Clic realizado con Actions en elemento 'Acciones'")
                    except Exception as act_e:
                        print(f"   - Error también con Actions: {str(act_e)}")
                        raise Exception(f"Todas las estrategias de clic fallaron: Normal={str(e)}, JS={str(js_e)}, Actions={str(act_e)}")

            # Esperar más tiempo para que aparezca el menú modal
            print("   - Esperando que aparezca el menú modal...")
            time.sleep(3)

            # Verificar que el modal apareció buscando el botón "Exportar"
            try:
                export_check = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                )
                print("   - Modal 'Acciones' abierto correctamente (botón Exportar detectado)")
            except Exception:
                print("   - Modal puede haber aparecido pero botón Exportar no detectado aún")

            # Tomar screenshot después de hacer clic
            driver.save_screenshot("after_click_acciones.png")
            print("   - Interacción con elemento 'Acciones' completada")

        except Exception as e:
            print(f"   - ERROR: No se pudo encontrar o interactuar con el elemento 'Acciones': {str(e)}")
            driver.save_screenshot("error_acciones.png")
            print("   - Continuando con el flujo a pesar del error")

        # PASO 14: Buscar e interactuar con el botón "Exportar" dentro del menú "Acciones"
        print("14. Buscando e interactuando con el botón 'Exportar'...")
        try:
            # Esperar más tiempo para que el modal se cargue completamente
            print("   - Esperando que el modal 'Acciones' se cargue completamente...")
            time.sleep(5)  # Aumentado de 3 a 5 segundos

            # Tomar screenshot del estado del modal
            driver.save_screenshot("modal_acciones_state.png")

            # Múltiples estrategias para encontrar el botón "Exportar"
            exportar_button = None

            # Estrategia 1: XPath por texto (original)
            try:
                print("   - Buscando 'Exportar' por XPath con texto...")
                exportar_button = WebDriverWait(driver, 15).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                )
                print("   - Botón 'Exportar' encontrado por XPath con texto")
            except Exception as e:
                print(f"   - No encontrado por XPath texto: {str(e)}")

                # Estrategia 2: Buscar por texto en todos los botones dentro del modal
                try:
                    print("   - Buscando 'Exportar' por búsqueda de texto en botones...")
                    # Buscar específicamente en elementos que podrían estar dentro de un modal/dialog
                    modal_selectors = [
                        ".modal button", ".dialog button", ".popup button",
                        "[role='dialog'] button", "[role='menu'] button",
                        ".dropdown-menu button", ".menu button"
                    ]

                    for selector in modal_selectors:
                        try:
                            modal_buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                            for btn in modal_buttons:
                                try:
                                    btn_text = btn.text.strip()
                                    if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                        if btn.is_displayed() and btn.is_enabled():
                                            exportar_button = btn
                                            print(f"   - Botón 'Exportar' encontrado en modal: '{btn_text}'")
                                            break
                                except:
                                    continue
                            if exportar_button:
                                break
                        except:
                            continue
                except Exception as e:
                    print(f"   - Error en búsqueda modal: {str(e)}")

                # Estrategia 3: Búsqueda general en todos los botones visibles
                if not exportar_button:
                    try:
                        print("   - Buscando 'Exportar' en todos los botones visibles...")
                        all_buttons = driver.find_elements(By.TAG_NAME, "button")
                        for btn in all_buttons:
                            try:
                                if btn.is_displayed() and btn.is_enabled():
                                    btn_text = btn.text.strip()
                                    if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                        exportar_button = btn
                                        print(f"   - Botón 'Exportar' encontrado globalmente: '{btn_text}'")
                                        break
                            except:
                                continue
                    except Exception as e:
                        print(f"   - Error en búsqueda global: {str(e)}")

                # Estrategia 4: Buscar por atributos data-* o aria labels
                if not exportar_button:
                    try:
                        print("   - Buscando 'Exportar' por atributos data-* o aria-*...")
                        exportar_button = WebDriverWait(driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, "[aria-label*='Exportar'], [data-tooltip*='Exportar'], button[title*='Exportar']"))
                        )
                        print("   - Botón 'Exportar' encontrado por atributos data/aria")
                    except Exception as e:
                        print(f"   - No encontrado por atributos: {str(e)}")

                # Estrategia 5: Buscar en elementos que podrían estar en shadow DOM o iframes
                if not exportar_button:
                    try:
                        print("   - Buscando en posibles iframes o shadow DOM...")
                        # Verificar si hay iframes
                        iframes = driver.find_elements(By.TAG_NAME, "iframe")
                        for iframe in iframes:
                            try:
                                driver.switch_to.frame(iframe)
                                iframe_buttons = driver.find_elements(By.TAG_NAME, "button")
                                for btn in iframe_buttons:
                                    try:
                                        if btn.is_displayed() and btn.is_enabled():
                                            btn_text = btn.text.strip()
                                            if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                                exportar_button = btn
                                                print(f"   - Botón 'Exportar' encontrado en iframe: '{btn_text}'")
                                                break
                                    except:
                                        continue
                                driver.switch_to.default_content()
                                if exportar_button:
                                    break
                            except:
                                driver.switch_to.default_content()
                                continue
                    except Exception as e:
                        print(f"   - Error en búsqueda iframe: {str(e)}")
                        driver.switch_to.default_content()

            # Verificar si se encontró el elemento
            if not exportar_button:
                # Último intento: esperar más tiempo y buscar de nuevo
                print("   - Último intento: esperando más tiempo y buscando nuevamente...")
                time.sleep(3)
                try:
                    exportar_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                    )
                    print("   - Botón 'Exportar' encontrado en último intento")
                except Exception:
                    raise Exception("No se pudo encontrar el botón 'Exportar' con ninguna estrategia después de múltiples intentos")

            # Tomar screenshot antes de hacer clic
            driver.save_screenshot("before_click_exportar.png")

            # Hacer clic en el botón "Exportar"
            print("   - Botón 'Exportar' encontrado. Haciendo clic...")
            try:
                # Scroll al elemento antes de hacer clic
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", exportar_button)
                time.sleep(1)  # Pequeña pausa después del scroll

                exportar_button.click()
                print("   - Clic realizado correctamente en el botón 'Exportar'")
            except Exception as e:
                print(f"   - Error al hacer clic normal: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    print("   - Intentando hacer clic con JavaScript...")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'}); arguments[0].click();", exportar_button)
                    print("   - Clic realizado con JavaScript en botón 'Exportar'")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
                    # Intentar con Actions como último recurso
                    try:
                        print("   - Intentando con Actions (move and click)...")
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(exportar_button).pause(1).click().perform()
                        print("   - Clic realizado con Actions en botón 'Exportar'")
                    except Exception as act_e:
                        print(f"   - Error también con Actions: {str(act_e)}")
                        raise Exception(f"Todas las estrategias de clic fallaron: Normal={str(e)}, JS={str(js_e)}, Actions={str(act_e)}")

            # Esperar un momento para que aparezca el diálogo de exportación
            print("   - Esperando que aparezca el diálogo de exportación...")
            time.sleep(3)

            # Tomar screenshot después de hacer clic
            driver.save_screenshot("after_click_exportar.png")
            print("   - Interacción con botón 'Exportar' completada")

            # PASO 15: Esperar a que se complete la descarga del archivo Excel
            print("15. Esperando descarga del archivo Excel...")
            time.sleep(10)  # Esperar 10 segundos para que inicie la descarga

            # Intentar encontrar el archivo descargado por varios minutos
            max_attempts = 18  # Intentar por 3 minutos (18 * 10 segundos)
            excel_file = None

            print(f"   - 🔄 Iniciando búsqueda de archivo Excel descargado...")
            print(f"   - 📊 Buscaré archivos creados en los últimos 15 minutos")
            print(f"   - ⏰ Intentos máximos: {max_attempts} (espera de 10s entre intentos)")

            for attempt in range(max_attempts):
                print(f"\n   - 🔍 Intento {attempt + 1}/{max_attempts} de encontrar archivo Excel...")
                # Buscar archivos creados en los últimos 15 minutos (desde que inició el proceso de exportación)
                excel_file = find_latest_excel_file(max_age_minutes=15)

                if excel_file:
                    print(f"   - ✅ ¡Archivo Excel encontrado exitosamente!")
                    print(f"   - 📄 Nombre: {os.path.basename(excel_file)}")
                    print(f"   - 📂 Ubicación: {os.path.dirname(excel_file)}")
                    break
                else:
                    remaining_attempts = max_attempts - attempt - 1
                    if remaining_attempts > 0:
                        print(f"   - ⏳ Archivo no encontrado, esperando 10 segundos... ({remaining_attempts} intentos restantes)")
                        time.sleep(10)
                    else:
                        print(f"   - ❌ No se pudo encontrar el archivo Excel después de {max_attempts} intentos")
                        print(f"   - 💡 Sugerencias:")
                        print(f"      • Verificar que la descarga de Oracle se completó correctamente")
                        print(f"      • Revisar si el archivo se guardó en una ubicación diferente")
                        print(f"      • Verificar permisos de escritura en la carpeta Downloads")

            if excel_file:
                # PASO 16: Subir archivo Excel a SQL Server
                print("16. Subiendo archivo Excel a SQL Server...")
                success = upload_excel_to_sql_server(excel_file, 'tb_toa_reporte_diario')

                if success:
                    print("✅ Proceso completo primera área: Archivo exportado y subido a SQL Server exitosamente!")
                    # Guardar timestamp del primer archivo para filtrar el segundo
                    first_file_timestamp = os.path.getmtime(excel_file)
                    print(f"   - 📅 Timestamp del primer archivo: {time.ctime(first_file_timestamp)}")
                else:
                    print("⚠️  Archivo exportado pero error al subir a SQL Server")
                    first_file_timestamp = None
            else:
                print("❌ No se pudo encontrar el archivo Excel descargado")
                first_file_timestamp = None

        except Exception as e:
            print(f"   - ERROR: No se pudo encontrar o interactuar con el botón 'Exportar': {str(e)}")
            driver.save_screenshot("error_exportar.png")
            print("   - Continuando con el flujo a pesar del error")

        # PASO 17: Navegar a segunda área - "Area operacion Metropolitana"
        print("\n" + "="*80)
        print("17. NAVEGANDO A SEGUNDA ÁREA - AREA OPERACION METROPOLITANA")
        print("="*80)

        try:
            print("   - Buscando elemento 'Area operacion Metropolitana'...")

            # Estrategia múltiple para encontrar el elemento
            area_metropolitana = None

            # Estrategia 1: Por ID específico (si existe)
            try:
                print("   - Intentando encontrar por ID 'elId1579'...")
                area_metropolitana = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.ID, "elId1579"))
                )
                print("   - Elemento encontrado por ID 'elId1579'")
            except Exception as e:
                print(f"   - No encontrado por ID 'elId1579': {str(e)}")

                # Estrategia 2: Por texto visible
                try:
                    print("   - Buscando por texto 'Area operacion Metropolitana'...")
                    area_metropolitana = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Area operacion Metropolitana')]"))
                    )
                    print("   - Elemento encontrado por texto visible")
                except Exception as e:
                    print(f"   - No encontrado por texto completo: {str(e)}")

                    # Estrategia 3: Por texto parcial
                    try:
                        print("   - Buscando por texto parcial 'Metropolitana'...")
                        area_metropolitana = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), 'Metropolitana')]"))
                        )
                        print("   - Elemento encontrado por texto parcial 'Metropolitana'")
                    except Exception as e:
                        print(f"   - No encontrado por texto parcial: {str(e)}")

                        # Estrategia 4: Buscar en todos los elementos clickeables
                        try:
                            print("   - Buscando en todos los elementos con texto relacionado...")
                            all_clickable = driver.find_elements(By.XPATH, "//button | //a | //div[@role='button'] | //span[@role='button']")

                            for elem in all_clickable:
                                try:
                                    text = elem.text.strip()
                                    if 'metropolitana' in text.lower() or 'metropolitana' in elem.get_attribute('title').lower() if elem.get_attribute('title') else False:
                                        if elem.is_displayed() and elem.is_enabled():
                                            area_metropolitana = elem
                                            print(f"   - Elemento encontrado en búsqueda general: '{text}'")
                                            break
                                except:
                                    continue
                        except Exception as e:
                            print(f"   - Error en búsqueda general: {str(e)}")

            # Verificar si se encontró el elemento
            if not area_metropolitana:
                raise Exception("No se pudo encontrar el elemento 'Area operacion Metropolitana' con ninguna estrategia")

            # Tomar screenshot antes de hacer clic
            driver.save_screenshot("before_click_area_metropolitana.png")

            # Hacer clic en el elemento
            print("   - Elemento encontrado. Haciendo clic en 'Area operacion Metropolitana'...")
            try:
                area_metropolitana.click()
                print("   - Clic realizado correctamente en 'Area operacion Metropolitana'")
            except Exception as e:
                print(f"   - Error al hacer clic normal: {str(e)}")
                # Intentar con JavaScript como alternativa
                try:
                    driver.execute_script("arguments[0].click();", area_metropolitana)
                    print("   - Clic realizado con JavaScript en 'Area operacion Metropolitana'")
                except Exception as js_e:
                    print(f"   - Error también con JavaScript: {str(js_e)}")
                    # Intentar con Actions como último recurso
                    try:
                        from selenium.webdriver.common.action_chains import ActionChains
                        actions = ActionChains(driver)
                        actions.move_to_element(area_metropolitana).pause(1).click().perform()
                        print("   - Clic realizado con Actions en 'Area operacion Metropolitana'")
                    except Exception as act_e:
                        print(f"   - Error también con Actions: {str(act_e)}")
                        raise Exception(f"Todas las estrategias de clic fallaron: Normal={str(e)}, JS={str(js_e)}, Actions={str(act_e)}")

            # Tomar screenshot después de hacer clic
            driver.save_screenshot("after_click_area_metropolitana.png")

            # Esperar 10 segundos como solicitado
            print("   - Esperando 10 segundos para que la página se cargue completamente...")
            time.sleep(10)

            print("   - ✅ Navegación a segunda área completada")

        except Exception as e:
            print(f"   - ❌ ERROR: No se pudo navegar a 'Area operacion Metropolitana': {str(e)}")
            driver.save_screenshot("error_area_metropolitana.png")
            print("   - Continuando con el flujo a pesar del error")

        # PASO 18: Repetir proceso de exportación en segunda área
        print("\n" + "="*80)
        print("18. REPITIENDO PROCESO DE EXPORTACIÓN EN SEGUNDA ÁREA")
        print("="*80)

        try:
            # PASO 18.1: Buscar e interactuar con el elemento "Acciones" (segunda vez)
            print("18.1. Buscando e interactuando con el elemento 'Acciones' (segunda área)...")
            try:
                # Esperar adicional para asegurar que la página esté completamente cargada
                print("   - Esperando que la página esté completamente cargada...")
                time.sleep(3)  # Espera adicional antes de buscar elementos

                # Tomar screenshot antes de buscar el elemento
                driver.save_screenshot("before_search_acciones_second.png")

                # Múltiples estrategias para encontrar el botón "Acciones"
                acciones_button = None

                # Estrategia 1: XPath por texto (original)
                try:
                    print("   - Buscando por XPath con texto 'Acciones'...")
                    acciones_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Acciones')]"))
                    )
                    print("   - Elemento encontrado por XPath con texto")
                except Exception as e:
                    print(f"   - No encontrado por XPath texto: {str(e)}")

                    # Estrategia 2: Buscar por clase o atributos específicos
                    try:
                        print("   - Buscando por selectores CSS más específicos...")
                        # Buscar botones que contengan "Acciones" en cualquier atributo
                        all_buttons = driver.find_elements(By.TAG_NAME, "button")
                        for btn in all_buttons:
                            try:
                                btn_text = btn.text.strip()
                                if "Acciones" in btn_text or "acciones" in btn_text.lower():
                                    if btn.is_displayed() and btn.is_enabled():
                                        acciones_button = btn
                                        print(f"   - Elemento encontrado por búsqueda de texto: '{btn_text}'")
                                        break
                            except:
                                continue
                    except Exception as e:
                        print(f"   - Error en búsqueda por texto: {str(e)}")

                    # Estrategia 3: Buscar por data attributes o aria labels
                    if not acciones_button:
                        try:
                            print("   - Buscando por atributos data-* o aria-*...")
                            acciones_button = WebDriverWait(driver, 5).until(
                                EC.element_to_be_clickable((By.CSS_SELECTOR, "[aria-label*='Acciones'], [data-tooltip*='Acciones'], button[title*='Acciones']"))
                            )
                            print("   - Elemento encontrado por atributos data/aria")
                        except Exception as e:
                            print(f"   - No encontrado por atributos: {str(e)}")

                # Verificar si se encontró el elemento
                if not acciones_button:
                    raise Exception("No se pudo encontrar el botón 'Acciones' con ninguna estrategia")

                # Tomar screenshot antes de hacer clic
                driver.save_screenshot("before_click_acciones_second.png")

                # Hacer clic en el elemento "Acciones"
                print("   - Elemento 'Acciones' encontrado. Haciendo clic...")
                try:
                    # Scroll al elemento antes de hacer clic
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", acciones_button)
                    time.sleep(1)  # Pequeña pausa después del scroll

                    acciones_button.click()
                    print("   - Clic realizado correctamente en el elemento 'Acciones'")
                except Exception as e:
                    print(f"   - Error al hacer clic normal: {str(e)}")
                    # Intentar con JavaScript como alternativa
                    try:
                        print("   - Intentando hacer clic con JavaScript...")
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'}); arguments[0].click();", acciones_button)
                        print("   - Clic realizado con JavaScript en elemento 'Acciones'")
                    except Exception as js_e:
                        print(f"   - Error también con JavaScript: {str(js_e)}")
                        # Intentar con Actions como último recurso
                        try:
                            print("   - Intentando con Actions (move and click)...")
                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(driver)
                            actions.move_to_element(acciones_button).pause(1).click().perform()
                            print("   - Clic realizado con Actions en elemento 'Acciones'")
                        except Exception as act_e:
                            print(f"   - Error también con Actions: {str(act_e)}")
                            raise Exception(f"Todas las estrategias de clic fallaron: Normal={str(e)}, JS={str(js_e)}, Actions={str(act_e)}")

                # Esperar más tiempo para que aparezca el menú modal
                print("   - Esperando que aparezca el menú modal...")
                time.sleep(3)

                # Verificar que el modal apareció buscando el botón "Exportar"
                try:
                    export_check = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                    )
                    print("   - Modal 'Acciones' abierto correctamente (botón Exportar detectado)")
                except Exception:
                    print("   - Modal puede haber aparecido pero botón Exportar no detectado aún")

                # Tomar screenshot después de hacer clic
                driver.save_screenshot("after_click_acciones_second.png")
                print("   - Interacción con elemento 'Acciones' completada (segunda área)")

            except Exception as e:
                print(f"   - ERROR: No se pudo encontrar o interactuar con el elemento 'Acciones': {str(e)}")
                driver.save_screenshot("error_acciones_second.png")
                print("   - Continuando con el flujo a pesar del error")

            # PASO 18.2: Buscar e interactuar con el botón "Exportar" (segunda vez)
            print("18.2. Buscando e interactuando con el botón 'Exportar' (segunda área)...")
            try:
                # Esperar más tiempo para que el modal se cargue completamente
                print("   - Esperando que el modal 'Acciones' se cargue completamente...")
                time.sleep(5)  # Aumentado de 3 a 5 segundos

                # Tomar screenshot del estado del modal
                driver.save_screenshot("modal_acciones_state_second.png")

                # Múltiples estrategias para encontrar el botón "Exportar"
                exportar_button = None

                # Estrategia 1: XPath por texto (original)
                try:
                    print("   - Buscando 'Exportar' por XPath con texto...")
                    exportar_button = WebDriverWait(driver, 15).until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                    )
                    print("   - Botón 'Exportar' encontrado por XPath con texto")
                except Exception as e:
                    print(f"   - No encontrado por XPath texto: {str(e)}")

                    # Estrategia 2: Buscar por texto en todos los botones dentro del modal
                    try:
                        print("   - Buscando 'Exportar' por búsqueda de texto en botones...")
                        # Buscar específicamente en elementos que podrían estar dentro de un modal/dialog
                        modal_selectors = [
                            ".modal button", ".dialog button", ".popup button",
                            "[role='dialog'] button", "[role='menu'] button",
                            ".dropdown-menu button", ".menu button"
                        ]

                        for selector in modal_selectors:
                            try:
                                modal_buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                                for btn in modal_buttons:
                                    try:
                                        btn_text = btn.text.strip()
                                        if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                            if btn.is_displayed() and btn.is_enabled():
                                                exportar_button = btn
                                                print(f"   - Botón 'Exportar' encontrado en modal: '{btn_text}'")
                                                break
                                    except:
                                        continue
                                if exportar_button:
                                    break
                            except:
                                continue
                    except Exception as e:
                        print(f"   - Error en búsqueda modal: {str(e)}")

                    # Estrategia 3: Búsqueda general en todos los botones visibles
                    if not exportar_button:
                        try:
                            print("   - Buscando 'Exportar' en todos los botones visibles...")
                            all_buttons = driver.find_elements(By.TAG_NAME, "button")
                            for btn in all_buttons:
                                try:
                                    if btn.is_displayed() and btn.is_enabled():
                                        btn_text = btn.text.strip()
                                        if "Exportar" in btn_text or "exportar" in btn_text.lower():
                                            exportar_button = btn
                                            print(f"   - Botón 'Exportar' encontrado globalmente: '{btn_text}'")
                                            break
                                except:
                                    continue
                        except Exception as e:
                            print(f"   - Error en búsqueda global: {str(e)}")

                # Verificar si se encontró el elemento
                if not exportar_button:
                    # Último intento: esperar más tiempo y buscar de nuevo
                    print("   - Último intento: esperando más tiempo y buscando nuevamente...")
                    time.sleep(3)
                    try:
                        exportar_button = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Exportar')]"))
                        )
                        print("   - Botón 'Exportar' encontrado en último intento")
                    except Exception:
                        raise Exception("No se pudo encontrar el botón 'Exportar' con ninguna estrategia después de múltiples intentos")

                # Tomar screenshot antes de hacer clic
                driver.save_screenshot("before_click_exportar_second.png")

                # Hacer clic en el botón "Exportar"
                print("   - Botón 'Exportar' encontrado. Haciendo clic...")
                try:
                    # Scroll al elemento antes de hacer clic
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", exportar_button)
                    time.sleep(1)  # Pequeña pausa después del scroll

                    exportar_button.click()
                    print("   - Clic realizado correctamente en el botón 'Exportar'")
                except Exception as e:
                    print(f"   - Error al hacer clic normal: {str(e)}")
                    # Intentar con JavaScript como alternativa
                    try:
                        print("   - Intentando hacer clic con JavaScript...")
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'}); arguments[0].click();", exportar_button)
                        print("   - Clic realizado con JavaScript en botón 'Exportar'")
                    except Exception as js_e:
                        print(f"   - Error también con JavaScript: {str(js_e)}")
                        # Intentar con Actions como último recurso
                        try:
                            print("   - Intentando con Actions (move and click)...")
                            from selenium.webdriver.common.action_chains import ActionChains
                            actions = ActionChains(driver)
                            actions.move_to_element(exportar_button).pause(1).click().perform()
                            print("   - Clic realizado con Actions en botón 'Exportar'")
                        except Exception as act_e:
                            print(f"   - Error también con Actions: {str(act_e)}")
                            raise Exception(f"Todas las estrategias de clic fallaron: Normal={str(e)}, JS={str(js_e)}, Actions={str(act_e)}")

                # Esperar un momento para que aparezca el diálogo de exportación
                print("   - Esperando que aparezca el diálogo de exportación...")
                time.sleep(3)

                # Tomar screenshot después de hacer clic
                driver.save_screenshot("after_click_exportar_second.png")
                print("   - Interacción con botón 'Exportar' completada (segunda área)")

                # PASO 18.3: Esperar a que se complete la descarga del archivo Excel (segunda vez)
                print("18.3. Esperando descarga del archivo Excel (segunda área)...")
                time.sleep(10)  # Esperar 10 segundos para que inicie la descarga

                # Intentar encontrar el archivo descargado por varios minutos
                max_attempts = 18  # Intentar por 3 minutos (18 * 10 segundos)
                excel_file_second = None

                print(f"   - 🔄 Iniciando búsqueda de archivo Excel descargado (segunda área)...")
                print(f"   - 📊 Buscaré archivos creados en los últimos 15 minutos")
                print(f"   - ⏰ Intentos máximos: {max_attempts} (espera de 10s entre intentos)")

                for attempt in range(max_attempts):
                    print(f"\n   - 🔍 Intento {attempt + 1}/{max_attempts} de encontrar archivo Excel (segunda área)...")
                    # Buscar archivos creados después del primer archivo (desde que inició el proceso de exportación de segunda área)
                    # Si no hay timestamp del primer archivo, usar None para buscar cualquier archivo reciente
                    min_timestamp = first_file_timestamp if first_file_timestamp else None
                    excel_file_second = find_latest_excel_file(max_age_minutes=15, min_timestamp=min_timestamp)

                    if excel_file_second:
                        print(f"   - ✅ ¡Archivo Excel encontrado exitosamente (segunda área)!")
                        print(f"   - 📄 Nombre: {os.path.basename(excel_file_second)}")
                        print(f"   - 📂 Ubicación: {os.path.dirname(excel_file_second)}")
                        break
                    else:
                        remaining_attempts = max_attempts - attempt - 1
                        if remaining_attempts > 0:
                            print(f"   - ⏳ Archivo no encontrado, esperando 10 segundos... ({remaining_attempts} intentos restantes)")
                            time.sleep(10)
                        else:
                            print(f"   - ❌ No se pudo encontrar el archivo Excel después de {max_attempts} intentos")
                            print(f"   - 💡 Sugerencias:")
                            print(f"      • Verificar que la descarga de Oracle se completó correctamente")
                            print(f"      • Revisar si el archivo se guardó en una ubicación diferente")
                            print(f"      • Verificar permisos de escritura en la carpeta Downloads")

                if excel_file_second:
                    # PASO 18.4: Subir archivo Excel a SQL Server (modo APPEND)
                    print("18.4. Subiendo archivo Excel a SQL Server (modo APPEND)...")
                    success_second = upload_excel_to_sql_server(excel_file_second, 'tb_toa_reporte_diario', mode='append')

                    if success_second:
                        print("✅ Proceso completo segunda área: Archivo exportado y subido a SQL Server exitosamente!")
                        print("📊 Datos agregados a la tabla existente (sin eliminar datos previos)")
                    else:
                        print("⚠️  Archivo exportado pero error al subir a SQL Server (segunda área)")
                else:
                    print("❌ No se pudo encontrar el archivo Excel descargado (segunda área)")

            except Exception as e:
                print(f"   - ERROR: No se pudo completar el proceso de exportación en segunda área: {str(e)}")
                driver.save_screenshot("error_exportar_second.png")
                print("   - Continuando con el flujo a pesar del error")

        except Exception as e:
            print(f"   - ERROR: No se pudo completar el proceso en segunda área: {str(e)}")
            driver.save_screenshot("error_second_area.png")
            print("   - Continuando con el flujo a pesar del error")

        # FIN DEL CICLO - ESPERA 10 SEGUNDOS ANTES DEL SIGUIENTE CICLO
        print("\n" + "="*80)
        print(f"✅ CICLO #{cycle_count} COMPLETADO - {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        print("📊 Resumen del ciclo:")
        print(f"   • Centro: Procesado e integrado")
        print(f"   • Metropolitana: Procesado e integrado")
        print(f"   • Total ciclos completados: {cycle_count}")
        print("="*80)
        print("⏳ Esperando 10 segundos antes del siguiente ciclo...")
        print("⏹️  Presiona Ctrl+C para detener el ciclo permanente")
        print("="*80 + "\n")

        time.sleep(10)

        print("\n" + "🔄" * 40)
        print("REINICIANDO CICLO - VOLVIENDO A ZONA CENTRO")
        print("🔄" * 40 + "\n")

    except Exception as cycle_error:
        cycle_end_time = time.time()
        cycle_duration = cycle_end_time - cycle_start_time

        print("\n" + "❌" * 50)
        print(f"ERROR EN CICLO #{cycle_count} - Duración: {cycle_duration:.2f} segundos")
        print("❌" * 50)
        print(f"Error: {str(cycle_error)}")
        print("📊 Estadísticas del ciclo:")
        print(f"   • Ciclos completados: {cycle_count - 1}")
        print(f"   • Tiempo del ciclo fallido: {cycle_duration:.2f} segundos")
        print("🔄 El ciclo continuará en 30 segundos...")
        print("❌" * 50 + "\n")

        # Esperar 30 segundos antes de reintentar en caso de error
        time.sleep(30)

        print("\n" + "🔄" * 40)
        print("REINTENTANDO CICLO DESPUÉS DE ERROR")
        print("🔄" * 40 + "\n")
        # Removed continue - the while loop will continue naturally

    except Exception as e:
        print(f"   - ERROR: No se pudo navegar a Oracle Cloud: {str(e)}")
        logger.error(f"Error en la ejecución: {str(e)}")
        import traceback
        print("Detalles:")
        print(traceback.format_exc())
        raise

    finally:
        # Mostrar resumen final con información de tiempo transcurrido
        end_time = time.time()
        duration = end_time - start_time
        print("\n" + "="*80)
        print(f"PROCESO FINALIZADO - {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Tiempo de ejecución: {duration:.2f} segundos")
        print("="*80)
        
        # NO cerramos el navegador - mensaje explícito
        print("\n¡IMPORTANTE! El navegador permanecerá abierto para que puedas interactuar con Oracle Cloud.")
        print("Para cerrar el navegador manualmente, simplemente cierra la ventana cuando hayas terminado.")
        
        # Evitar que el script termine
        try:
            print("\nPresiona Ctrl+C en la consola cuando quieras terminar el script...")
            # Mantenemos el script en ejecución para evitar que el navegador se cierre
            while True:
                time.sleep(10)  # Comprobación cada 10 segundos
        except KeyboardInterrupt:
            print("\nScript terminado por el usuario. El navegador permanecerá abierto.")
            # No cerramos el driver intencionalmente

# Ejecutar la función principal
if __name__ == "__main__":
    run_oracle_navigation()