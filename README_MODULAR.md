# Oracle Cloud Automation - Modular Version

This is a refactored, modular version of the Oracle Cloud automation script. The original monolithic script has been broken down into separate, maintainable modules for better code organization, readability, and performance.

## 🏗️ Modular Architecture

### 📁 Project Structure

```
oracle-automation/
├── main.py                 # Entry point and main orchestration
├── config.py              # Configuration constants and settings
├── database.py            # SQL Server database operations
├── file_utils.py          # File handling and Excel operations
├── web_automation.py      # Selenium web automation
├── edge_script_oracle.py  # Original monolithic script (for reference)
└── README_MODULAR.md      # This documentation
```

### 🔧 Module Descriptions

#### 1. `config.py` - Configuration Management
- **Purpose**: Centralizes all configuration constants and settings
- **Features**:
  - SQL Server connection parameters
  - Oracle Cloud credentials and URLs
  - WebDriver configuration
  - Timing and retry settings
  - Element selectors for web automation
  - Error messages and logging configuration

#### 2. `database.py` - Database Operations
- **Purpose**: Handles all SQL Server interactions
- **Features**:
  - Excel file upload to database
  - Table creation and management
  - Batch data insertion with error handling
  - Column type analysis and mapping
  - Connection management

#### 3. `file_utils.py` - File Management
- **Purpose**: Manages file operations and Excel handling
- **Features**:
  - Excel file discovery and validation
  - Download monitoring and waiting
  - File age filtering and cleanup
  - File information retrieval

#### 4. `web_automation.py` - Web Automation
- **Purpose**: Selenium-based web automation for Oracle Cloud
- **Features**:
  - WebDriver setup and configuration
  - Oracle Cloud navigation and login
  - SSO authentication handling
  - Element interaction and clicking
  - Screenshot capture
  - Session management

#### 5. `main.py` - Main Controller
- **Purpose**: Orchestrates the entire automation workflow
- **Features**:
  - Permanent cycle management
  - Error handling and recovery
  - Signal handling for graceful shutdown
  - Progress tracking and logging

## 🚀 Usage

### Prerequisites

1. **Python Dependencies**:
   ```bash
   pip install selenium pandas pyodbc openpyxl
   ```

2. **WebDriver**:
   - Download Microsoft Edge WebDriver (msedgedriver.exe)
   - Place it in the same directory as the scripts

3. **SQL Server**:
   - Ensure SQL Server is accessible
   - Update connection parameters in `config.py` if needed

### Running the Automation

```bash
python main.py
```

The script will:
1. Initialize the web automation system
2. Navigate to Oracle Cloud and perform SSO login
3. Run permanent cycles: Centro → Metropolitana → Centro
4. Extract data and upload to SQL Server
5. Continue until manually stopped (Ctrl+C)

## ⚙️ Configuration

### Key Configuration Files

#### `config.py`
Update these sections as needed:

```python
# SQL Server Configuration
SQL_SERVER_CONFIG = {
    'server': 'YOUR_SERVER',
    'database': 'YOUR_DATABASE',
    'uid': 'YOUR_USERNAME',
    'pwd': 'YOUR_PASSWORD'
}

# Oracle Cloud Configuration
ORACLE_CONFIG = {
    'username': 'YOUR_ORACLE_USERNAME',
    'password': 'YOUR_ORACLE_PASSWORD',
    'email': '<EMAIL>'
}
```

#### Timing Configuration
```python
TIMING_CONFIG = {
    'cycle_pause': 10,        # Seconds between cycles
    'error_retry_delay': 30,  # Seconds to wait after errors
    'download_wait': 10,      # Seconds to wait for downloads
}
```

## 🔧 Best Practices Implemented

### 1. **Separation of Concerns**
- Each module has a single responsibility
- Clear interfaces between modules
- Easy to test and maintain individual components

### 2. **Error Handling**
- Comprehensive try-catch blocks
- Graceful degradation on failures
- Detailed logging for troubleshooting

### 3. **Configuration Management**
- All settings centralized in `config.py`
- Easy to modify without code changes
- Environment-specific configurations possible

### 4. **Resource Management**
- Proper connection cleanup
- Browser session management
- Memory-efficient batch processing

### 5. **Logging and Monitoring**
- Structured logging with different levels
- File and console output
- Performance metrics and cycle tracking

### 6. **Scalability**
- Batch processing for large datasets
- Configurable timeouts and retries
- Modular design allows easy extension

## 🐛 Troubleshooting

### Common Issues

1. **WebDriver Not Found**:
   - Ensure `msedgedriver.exe` is in the script directory
   - Check WebDriver version matches Edge browser

2. **Database Connection Failed**:
   - Verify SQL Server credentials in `config.py`
   - Check network connectivity
   - Ensure SQL Server allows remote connections

3. **Login Issues**:
   - Verify Oracle Cloud credentials
   - Check Microsoft account access
   - Ensure SSO is properly configured

4. **File Download Issues**:
   - Check download directory permissions
   - Verify Edge browser download settings
   - Ensure sufficient disk space

### Debug Mode

Enable detailed logging by modifying `config.py`:

```python
LOGGING_CONFIG = {
    'level': 'DEBUG',  # Change from 'INFO' to 'DEBUG'
    # ... other settings
}
```

## 📊 Performance Optimizations

1. **Batch Processing**: Database inserts use batches of 500 records
2. **Selective Downloads**: Only processes recent files (15-minute window)
3. **Connection Pooling**: Reuses database connections
4. **Lazy Loading**: Files are processed only when needed
5. **Memory Management**: Large datasets processed in chunks

## 🔄 Migration from Original Script

### Key Improvements

1. **Maintainability**: Code is now organized into logical modules
2. **Testability**: Each module can be tested independently
3. **Configurability**: All settings are centralized and easily modifiable
4. **Error Recovery**: Better error handling and recovery mechanisms
5. **Performance**: Optimized database operations and file handling
6. **Documentation**: Comprehensive comments and documentation

### Breaking Changes

- Configuration moved to `config.py`
- Function signatures may have changed for better modularity
- Logging configuration centralized
- Error handling improved (may behave differently in edge cases)

## 🧪 Testing

### Unit Testing

Each module can be tested independently:

```python
# Test database operations
from database import DatabaseManager
db = DatabaseManager()
# Test methods...

# Test file operations
from file_utils import FileManager
fm = FileManager()
# Test methods...

# Test web automation
from web_automation import OracleWebAutomation
automation = OracleWebAutomation()
# Test methods...
```

### Integration Testing

```python
# Test full workflow
from main import OracleAutomationController
controller = OracleAutomationController()
# Test initialization and cycles...
```

## 📈 Future Enhancements

### Potential Improvements

1. **Configuration Files**: Support for JSON/YAML configuration files
2. **Database Abstraction**: Support for multiple database types
3. **WebDriver Options**: Support for Chrome, Firefox in addition to Edge
4. **Monitoring Dashboard**: Web-based monitoring interface
5. **Parallel Processing**: Multi-threaded operations for better performance
6. **API Integration**: REST API endpoints for external control
7. **Notification System**: Email/SMS alerts for failures
8. **Data Validation**: Enhanced data quality checks
9. **Retry Logic**: Exponential backoff for failed operations
10. **Containerization**: Docker support for easier deployment

## 🤝 Contributing

When making changes:

1. Follow the modular structure
2. Update configuration in `config.py` for new settings
3. Add comprehensive error handling
4. Update logging appropriately
5. Test changes thoroughly
6. Update this documentation

## 📄 License

This project is proprietary software. All rights reserved.

---

## 📞 Support

For issues or questions:
1. Check the logs in `edge_script_oracle.log`
2. Review configuration in `config.py`
3. Test individual modules for isolation
4. Check database connectivity
5. Verify WebDriver installation

The modular structure makes debugging and maintenance much easier compared to the original monolithic script.