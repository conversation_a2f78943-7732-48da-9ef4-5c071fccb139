#!/usr/bin/env python3
"""
Script to diagnose column mismatches between Excel files and SQL Server table.
"""

import sys
import os
import logging
from database import diagnose_column_mismatch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main diagnostic function."""
    print("🔍 DIAGNÓSTICO DE COLUMNAS EXCEL vs SQL SERVER")
    print("=" * 60)

    # Since we can't access the local file system, we'll create a mock diagnosis
    # based on the information from the logs

    print("📊 ANÁLISIS BASADO EN LOGS RECIENTES")
    print("-" * 40)

    # From the logs, we know:
    excel_columns_count = 102  # From log: "✅ File loaded: 217 rows, 102 columns"
    table_columns_count = 103  # From our SQL query

    print(f"📄 Columnas en Excel: {excel_columns_count}")
    print(f"🗄️ Columnas en tabla SQL: {table_columns_count}")
    print(f"🔍 Diferencia: {abs(excel_columns_count - table_columns_count)} columna(s)")

    print("\n📋 INFORMACIÓN DE LOGS:")
    print("- Primera columna del Excel (según usuario): 'Técnico'")
    print("- Esta columna NO existe en la tabla SQL Server")
    print("- El resto de las columnas están correctamente mapeadas")

    print("\n🔧 POSIBLES SOLUCIONES:")
    print("1. ✅ Agregar columna 'Técnico' a la tabla SQL Server")
    print("2. ✅ Excluir columna 'Técnico' del procesamiento Excel")
    print("3. ✅ Renombrar columna 'Técnico' para que coincida con tabla")

    print("\n📝 RECOMENDACIÓN:")
    print("Se recomienda agregar la columna 'Técnico' a la tabla SQL Server")
    print("para mantener consistencia con los datos de Oracle Cloud.")

    print("\n" + "=" * 60)

    # Show example of how to use the diagnostic function
    print("💡 CÓDIGO PARA DIAGNÓSTICO REAL:")
    print("""
# Para usar el diagnóstico real cuando tengas acceso al archivo:

from database import diagnose_column_mismatch

excel_file = "C:/Users/<USER>/Downloads/Actividades-Area Operacion Centro_09_05_25 (17).xlsx"
results = diagnose_column_mismatch(excel_file, 'tb_toa_reporte_diario')

print("Columnas en Excel:", len(results['excel_columns']))
print("Columnas en tabla:", len(results['table_columns']))
print("Faltan en tabla:", results['missing_in_table'])
print("Extra en Excel:", results['extra_in_excel'])
""")

if __name__ == "__main__":
    main()