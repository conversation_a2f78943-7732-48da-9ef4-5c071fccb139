"""
Configuration module for Oracle Cloud automation script.
Contains all configuration constants and settings.
"""

import os

# SQL Server Configuration
SQL_SERVER_CONFIG = {
    'driver': 'ODBC Driver 17 for SQL Server',
    'server': '************',
    'database': 'telqway',
    'uid': 'ncornejo',
    'pwd': 'N1c0l7as17'
}

# Oracle Cloud Configuration
ORACLE_CONFIG = {
    'url': 'https://vtr.fs.ocs.oraclecloud.com/',
    'username': 'ncornejoh',
    'password': 'Telqway.202517',
    'email': '<EMAIL>'
}

# File and Directory Configuration
DEFAULT_DOWNLOAD_DIR = os.path.join(os.path.expanduser("~"), "Downloads")
DEFAULT_PATTERN = "*.xlsx"
DEFAULT_MAX_AGE_MINUTES = 15

# Project paths
PROJECT_ROOT = os.path.dirname(os.path.dirname(__file__))
DRIVERS_DIR = os.path.join(PROJECT_ROOT, 'drivers')
LOGS_DIR = os.path.join(PROJECT_ROOT, 'logs')

# WebDriver Configuration
WEBDRIVER_CONFIG = {
    'driver_path': os.path.join(DRIVERS_DIR, "msedgedriver.exe"),
    'options': [
        '--start-maximized',
        '--disable-gpu',
        '--no-sandbox',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-images',  # Speed up loading
        '--disable-javascript',  # Only if not needed
        '--disable-dev-shm-usage',
        '--no-first-run',
        '--disable-default-apps',
        '--disable-infobars',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--disable-features=VizDisplayCompositor'
    ],
    'experimental_options': {
        'excludeSwitches': ['enable-logging'],
        'useAutomationExtension': False,
        'prefs': {
            'download.default_directory': DEFAULT_DOWNLOAD_DIR,
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'profile.default_content_setting_values.notifications': 2
        }
    }
}

# Timing Configuration
TIMING_CONFIG = {
    'page_load_timeout': 30,
    'implicit_wait': 10,
    'explicit_wait': 15,
    'element_wait': 20,
    'download_wait': 25,  # Wait time between download and database integration
    'cycle_pause': 10,
    'error_retry_delay': 30,
    'vtr_download_timeout': 25  # Specific timeout for VTR unified download
}

# Business Hours Configuration
BUSINESS_HOURS_CONFIG = {
    'start_hour': 8,   # 8am
    'end_hour': 22,    # 10pm (22:00)
    'timezone': 'America/Santiago'  # Timezone for business hours
}

# Database Configuration
DATABASE_CONFIG = {
    'batch_size': 500,
    'max_retries': 3,
    'retry_delay': 5,
    'connection_timeout': 30
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': os.path.join(LOGS_DIR, 'error_log.txt'),
    'mode': 'a',  # Append to the file on each execution
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Element Selectors (for better maintainability)
ELEMENT_SELECTORS = {
    # Login elements
    'sso_button': "sign-in-with-sso",
    'username_field': "sso_username",
    'continue_button': "signin-with-sso-button",
    'email_field': "i0116",
    'password_field': "i0118",
    'signin_button': "idSIButton9",

    # Oracle Cloud elements (based on actual HTML structure)
    'acciones_button': [
        "//button[contains(@class, 'edt-label') and contains(., 'Acciones')]",
        "//button[contains(text(), 'Acciones')]",
        "//button[contains(., 'Acciones')]",
        "//span[contains(text(), 'Acciones')]/parent::button",
        "//div[contains(text(), 'Acciones')]/parent::button",
        "//a[contains(text(), 'Acciones')]",
        "//*[contains(@title, 'Acciones')]",
        "//button[@aria-label*='Acciones']",
        "//button[contains(@class, 'action') and contains(@class, 'button')]"
    ],

    'exportar_button': [
        "//button[contains(text(), 'Exportar')]",
        "//button[contains(., 'Exportar')]",
        "//span[contains(text(), 'Exportar')]/parent::button",
        "//div[contains(text(), 'Exportar')]/parent::button",
        "//a[contains(text(), 'Exportar')]",
        "//*[contains(@title, 'Exportar')]",
        "//button[@aria-label*='Exportar']",
        "//button[contains(@class, 'export')]"
    ],

    # Session detection elements
    'elid50': "elId50",
    'elid1540': "elId1540",  # Alternative session indicator
    'elid334': "elId334",  # Metropolitana area

    # Area navigation elements
    'metropolitana_area': [
        "#elId564",  # Specific ID for Area Operacion Metropolitana
        "//button[@id='elId564']",  # XPath with specific ID
        "//*[contains(text(), 'Area operacion Metropolitana')]",
        "//*[contains(text(), 'Metropolitana')]",
        "//button[contains(text(), 'Metropolitana')]",
        "//a[contains(text(), 'Metropolitana')]",
        "//*[contains(@title, 'Metropolitana')]"
    ],

    'centro_area': [
        "#elId334",  # Specific ID for Area Operacion Centro
        "//button[@id='elId334']",  # XPath with specific ID
        "//*[contains(text(), 'Area operacion Centro')]",
        "//*[contains(text(), 'Centro')]",
        "//button[contains(text(), 'Centro')]",
        "//a[contains(text(), 'Centro')]",
        "//*[contains(@title, 'Centro')]"
    ],

    # VTR unified button (includes all zones) - Search by text only
    'vtr_button': [
        "//*[contains(text(), 'VTR')]",  # Primary: Direct text search
        "//button[contains(text(), 'VTR')]",  # Button with VTR text
        "//span[contains(text(), 'VTR')]/parent::button",  # Span with VTR text, get parent button
        "//span[contains(text(), 'VTR')]/ancestor::button",  # Span with VTR, find ancestor button
        "//*[text()='VTR']/parent::button",  # Exact text match, get parent button
        "//*[text()='VTR']/ancestor::button"  # Exact text match, find ancestor button
    ],

    # Vista dropdown configuration elements (for first cycle only)
    'vista_button': [
        "//button[contains(text(), 'Vista')]",
        "//button[contains(@aria-label, 'Vista')]",
        "//*[contains(text(), 'Vista')]/parent::button",
        "//button[contains(., 'Vista')]"
    ],

    'todos_datos_checkbox': [
        "//input[@type='checkbox' and following-sibling::*[contains(text(), 'Todos los datos de hijos')]]",
        "//input[@type='checkbox'][contains(@aria-label, 'Todos los datos de hijos')]",
        "//*[contains(text(), 'Todos los datos de hijos')]/preceding-sibling::input[@type='checkbox']",
        "//*[contains(text(), 'Todos los datos de hijos')]/..//input[@type='checkbox']"
    ],

    'aplicar_button': [
        "//button[contains(text(), 'Aplicar')]",
        "//button[contains(@aria-label, 'Aplicar')]",
        "//*[contains(text(), 'Aplicar')]/parent::button",
        "//button[contains(., 'Aplicar')]"
    ],

    # Alternative selectors for different Oracle Cloud versions
    'acciones_alt': [
        "//button[contains(@class, 'dropdown-toggle')]",
        "//button[contains(@data-toggle, 'dropdown')]",
        "//div[contains(@class, 'dropdown')]//button",
        "//li[contains(@class, 'dropdown')]//a"
    ]
}

# Error Messages
ERROR_MESSAGES = {
    'driver_not_found': "No se encontró msedgedriver.exe en la carpeta del script",
    'file_not_found': "El archivo especificado no existe",
    'empty_file': "El archivo Excel está vacío",
    'connection_failed': "Error al conectar a SQL Server",
    'upload_failed': "Error al subir archivo a SQL Server",
    'element_not_found': "Elemento no encontrado en la página",
    'download_timeout': "Timeout esperando descarga del archivo"
}
