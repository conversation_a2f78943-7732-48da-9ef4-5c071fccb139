#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import logging
import zipfile
import webbrowser
import traceback

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('msedgedriver_download.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def download_direct_from_website():
    """Descarga directamente desde el sitio web oficial mediante descarga asistida"""
    try:
        # URL del sitio web oficial
        url = "https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/"
        
        # Abrir el sitio web en el navegador
        logger.info(f"Abriendo el sitio web oficial: {url}")
        webbrowser.open(url)
        
        print("\n==========================================================")
        print("INSTRUCCIONES PARA DESCARGA MANUAL:")
        print("1. Se ha abierto el sitio web oficial en su navegador")
        print("2. Haga clic en el enlace 'x64' en la sección 'Stable Channel'")
        print("3. Guarde el archivo descargado en esta carpeta")
        print("4. Una vez descargado, presione ENTER para continuar")
        print("==========================================================")
        
        input("\nPresione ENTER cuando haya completado la descarga...")
        
        # Verificar si hay archivos edgedriver en la carpeta actual
        script_dir = os.path.dirname(os.path.abspath(__file__))
        files_in_dir = [f for f in os.listdir(script_dir) 
                       if f.startswith('edgedriver_') and f.endswith('.zip')]
        
        if files_in_dir:
            # Usar el archivo más reciente
            newest_file = max(files_in_dir, key=lambda x: os.path.getmtime(os.path.join(script_dir, x)))
            download_path = os.path.join(script_dir, newest_file)
            logger.info(f"Archivo encontrado en la carpeta actual: {newest_file}")
        else:
            # Buscar en la carpeta de descargas
            user_profile = os.environ.get('USERPROFILE', os.path.expanduser('~'))
            downloads_folder = os.path.join(user_profile, 'Downloads')
            
            if os.path.exists(downloads_folder):
                potential_files = [f for f in os.listdir(downloads_folder) 
                                  if f.startswith('edgedriver_') and f.endswith('.zip') and 
                                  os.path.getmtime(os.path.join(downloads_folder, f)) > time.time() - 300]  # Archivos de los últimos 5 minutos
                
                if potential_files:
                    newest_file = max(potential_files, key=lambda x: os.path.getmtime(os.path.join(downloads_folder, x)))
                    source_path = os.path.join(downloads_folder, newest_file)
                    # Copiar el archivo a la carpeta del script
                    import shutil
                    download_path = os.path.join(script_dir, newest_file)
                    shutil.copy2(source_path, download_path)
                    logger.info(f"Archivo copiado desde Downloads: {newest_file}")
                else:
                    logger.error("No se encontraron archivos edgedriver recientes en la carpeta de descargas")
                    return None
            else:
                logger.error(f"No se encontró la carpeta de descargas: {downloads_folder}")
                return None
        
        # Descomprimir el archivo
        extract_path = script_dir
        logger.info(f"Descomprimiendo en: {extract_path}")
        
        try:
            with zipfile.ZipFile(download_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            
            # Verificar que el archivo msedgedriver.exe exista
            msedgedriver_path = os.path.join(extract_path, "msedgedriver.exe")
            if os.path.exists(msedgedriver_path):
                logger.info(f"msedgedriver.exe extraído exitosamente en: {msedgedriver_path}")
                
                # Eliminar el archivo zip
                os.remove(download_path)
                logger.info("Archivo zip eliminado")
                
                return msedgedriver_path
            else:
                logger.error("No se pudo encontrar msedgedriver.exe después de descomprimir")
                return None
        except Exception as e:
            logger.error(f"Error al descomprimir: {e}")
            logger.error(traceback.format_exc())
            return None
            
    except Exception as e:
        logger.error(f"Error durante la descarga directa: {e}")
        logger.error(traceback.format_exc())
        return None

def main():
    """Función principal"""
    logger.info("=== INICIO DE DESCARGA DIRECTA DE MSEDGEDRIVER ===")
    
    msedgedriver_path = download_direct_from_website()
    
    if msedgedriver_path:
        logger.info("\n=== INSTALACIÓN COMPLETADA ===")
        logger.info(f"msedgedriver ha sido instalado en: {msedgedriver_path}")
        logger.info("Ahora puede ejecutar el script NdcBot.py")
    else:
        logger.error("\n=== ERROR EN LA INSTALACIÓN ===")
        logger.error("No se pudo descargar e instalar msedgedriver")
        logger.error("Por favor, descárguelo manualmente desde:")
        logger.error("https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        logger.error("y descomprímalo en esta carpeta")

if __name__ == "__main__":
    main()