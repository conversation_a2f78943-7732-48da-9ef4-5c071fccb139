import pandas as pd
import os
import pyodbc
import mysql.connector
import logging
import traceback
from datetime import datetime
import sys

# Configuración de logging
def setup_logging():
    """Configura el sistema de logging"""
    # Crear directorio de logs si no existe
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Configurar nombre de archivo de log
    log_file = os.path.join(log_dir, f'calidadnew23_{datetime.now().strftime("%Y%m%d")}.log')
    
    # Configurar logging básico
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('CalidadNew23')

# Obtener logger
logger = setup_logging()

# Variables globales
URL_LINK = "C:\\Users\\<USER>\\OneDrive - kayze\\ETL PYTHON\\CalidadTQW.txt"
FECHA_INICIO = '202405'
FECHA_FIN = '202509'

# Configuraciones de conexión
SQL_SERVER_CONFIG = {
    'driver': 'ODBC Driver 17 for SQL Server',
    'server': '************',
    'database': 'telqway',
    'uid': 'ncornejo',
    'pwd': 'N1c0l7as17'
}

MYSQL_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'ncornejo',
    'password': 'N1c0l7as17',
    'database': 'operaciones_tqw'
}

def connect_to_sql_server():
    """Establece conexión con SQL Server"""
    logger.info("Conectando a SQL Server...")
    try:
        conn_str = f"DRIVER={{{SQL_SERVER_CONFIG['driver']}}};SERVER={SQL_SERVER_CONFIG['server']};DATABASE={SQL_SERVER_CONFIG['database']};UID={SQL_SERVER_CONFIG['uid']};PWD={SQL_SERVER_CONFIG['pwd']}"
        conn = pyodbc.connect(conn_str)
        logger.info("Conexión a SQL Server establecida")
        return conn
    except Exception as e:
        logger.error(f"Error al conectar a SQL Server: {e}")
        raise

def connect_to_mysql():
    """Establece conexión con MySQL"""
    logger.info("Conectando a MySQL...")
    try:
        conn = mysql.connector.connect(
            host=MYSQL_CONFIG['host'],
            port=MYSQL_CONFIG['port'],
            user=MYSQL_CONFIG['user'],
            password=MYSQL_CONFIG['password'],
            database=MYSQL_CONFIG['database']
        )
        logger.info("Conexión a MySQL establecida")
        return conn
    except Exception as e:
        logger.error(f"Error al conectar a MySQL: {e}")
        raise

def read_data_file():
    """Lee el archivo de datos con manejo robusto de codificación"""
    logger.info(f"Leyendo archivo: {URL_LINK}")
    
    # Verificar si el archivo existe
    if not os.path.exists(URL_LINK):
        raise FileNotFoundError(f"El archivo no existe: {URL_LINK}")
    
    file_size = os.path.getsize(URL_LINK)
    if file_size == 0:
        raise ValueError("El archivo está vacío")
    
    logger.info(f"Tamaño del archivo: {file_size} bytes")
    
    # Codificaciones a probar
    encodings_to_try = ['utf-16', 'utf-16-le', 'utf-8', 'utf-8-sig', 'latin-1']
    
    # Intentar leer con diferentes codificaciones
    for encoding in encodings_to_try:
        try:
            logger.info(f"Intentando leer con encoding: {encoding}")
            
            # Primero leer las primeras líneas para verificar la estructura
            test_df = pd.read_csv(URL_LINK, encoding=encoding, delimiter='\t', nrows=5, header=None)
            
            # Buscar la fila que contiene los nombres de las columnas
            header_row = None
            for i, row in test_df.iterrows():
                row_str = ' '.join([str(x) for x in row if pd.notna(x)])
                if 'Base_Calidad_Reactiva New' in row_str:
                    header_row = i
                    logger.info(f"Nombres de columnas encontrados en la fila {header_row}")
                    break
            
            if header_row is not None:
                # Leer el archivo completo usando la fila identificada como encabezado
                df = pd.read_csv(URL_LINK, encoding=encoding, delimiter='\t', header=header_row)
                
                # Limpiar los nombres de las columnas
                new_columns = []
                for col in df.columns:
                    try:
                        col_str = str(col)
                        col_str = col_str.replace("Base_Calidad_Reactiva New[", "").replace("]", "")
                        new_columns.append(col_str)
                    except Exception as e:
                        logger.warning(f"Error limpiando nombre de columna: {e}")
                        new_columns.append(f"columna_{len(new_columns)}")
                
                df.columns = new_columns
                
                # Verificar que tenemos datos válidos
                if not df.empty and len(df.columns) > 1:
                    logger.info(f"✓ Lectura exitosa con {encoding}")
                    logger.info(f"Filas: {len(df)}, Columnas: {len(df.columns)}")
                    logger.info(f"Primeras columnas: {df.columns.tolist()[:10]}")
                    return df
        
        except Exception as e:
            logger.info(f"Error con {encoding}: {str(e)}")
            continue
    
    # Si llegamos aquí, intentar método alternativo
    logger.info("Intentando método alternativo...")
    
    for encoding in encodings_to_try:
        try:
            # Leer sin especificar header
            df_temp = pd.read_csv(URL_LINK, encoding=encoding, delimiter='\t', header=None)
            
            # Encontrar la fila con los nombres de columnas
            header_row = None
            for idx, row in df_temp.iterrows():
                if row.astype(str).str.contains('Base_Calidad_Reactiva New', na=False).any():
                    header_row = idx
                    logger.info(f"Headers encontrados en fila {header_row}")
                    break
            
            if header_row is not None:
                # Extraer los nombres de las columnas
                column_names = df_temp.iloc[header_row].astype(str).tolist()
                
                # Limpiar los nombres
                column_names = [col.replace("Base_Calidad_Reactiva New[", "").replace("]", "") for col in column_names]
                
                # Crear el DataFrame con los datos reales
                df = df_temp.iloc[header_row + 1:].reset_index(drop=True)
                df.columns = column_names
                
                if not df.empty:
                    logger.info(f"✓ Lectura exitosa con método alternativo usando {encoding}")
                    logger.info(f"Filas: {len(df)}, Columnas: {len(df.columns)}")
                    return df
        
        except Exception as e:
            logger.info(f"Error con método alternativo y {encoding}: {str(e)}")
            continue
    
    # Si todo falla, último intento con chunks
    logger.info("Último intento con lectura por chunks...")
    chunk_size = 10000
    
    for encoding in encodings_to_try:
        try:
            # Intentar con skiprows=2 (saltando las filas vacías y de headers)
            chunks = pd.read_csv(URL_LINK, encoding=encoding, delimiter='\t', 
                               skiprows=2, chunksize=chunk_size)
            
            # Procesar chunk por chunk
            df = pd.DataFrame()
            chunk_count = 0
            
            for chunk in chunks:
                chunk_count += 1
                logger.info(f"Procesando chunk {chunk_count}: {len(chunk)} filas")
                
                # Limpiar los nombres de las columnas
                chunk.columns = chunk.columns.astype(str).str.replace(r"Base_Calidad_Reactiva New\[", "", regex=True)
                chunk.columns = chunk.columns.str.replace("]", "", regex=True)
                
                # Acumular los resultados
                df = pd.concat([df, chunk], ignore_index=True)
            
            if not df.empty:
                logger.info(f"✓ Lectura por chunks exitosa con {encoding}")
                return df
        
        except Exception as e:
            logger.info(f"Error con chunks y {encoding}: {str(e)}")
            continue
    
    raise Exception("No se pudo leer el archivo con ninguna configuración")

def upload_to_sql_server(df, conn):
    """Sube los datos al SQL Server"""
    logger.info("Subiendo datos a SQL Server...")
    cursor = conn.cursor()
    
    # Eliminar tabla si existe
    try:
        cursor.execute("IF OBJECT_ID('TB_SHARE_CALIDAD_REACTIVA_NEW', 'U') IS NOT NULL DROP TABLE TB_SHARE_CALIDAD_REACTIVA_NEW")
        conn.commit()
        logger.info("Tabla TB_SHARE_CALIDAD_REACTIVA_NEW eliminada si existía")
    except Exception as e:
        logger.error(f"Error al eliminar tabla: {e}")
        conn.rollback()
        raise
    
    # Crear tabla
    try:
        # Definir columnas
        columns_def = []
        for col in df.columns:
            # Escapar nombres de columnas
            col_escaped = col.replace("'", "''").replace('"', '""')
            col_name = f"[{col_escaped}]"
            columns_def.append(f"{col_name} NVARCHAR(MAX)")
        
        # Agregar columna fecha_actualizacion
        fecha_actualizacion = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        columns_def.append("[fecha_actualizacion] DATETIME")
        
        # Ejecutar creación
        create_table_sql = f"CREATE TABLE TB_SHARE_CALIDAD_REACTIVA_NEW ({', '.join(columns_def)})"
        cursor.execute(create_table_sql)
        conn.commit()
        logger.info("Tabla TB_SHARE_CALIDAD_REACTIVA_NEW creada exitosamente")
    except Exception as e:
        logger.error(f"Error al crear tabla: {e}")
        logger.error(f"SQL: {create_table_sql[:100]}...")
        conn.rollback()
        raise
    
    # Preparar datos para inserción
    df = df.copy()  # Evitar SettingWithCopyWarning
    df['fecha_actualizacion'] = fecha_actualizacion
    
    # Insertar datos en lotes
    batch_size = 500
    total_rows = len(df)
    logger.info(f"Insertando {total_rows} filas en lotes de {batch_size}...")
    
    # Preparar la consulta SQL
    columns_str = ", ".join([f"[{col}]" for col in df.columns])
    placeholders = ", ".join(["?" for _ in df.columns])
    insert_sql = f"INSERT INTO TB_SHARE_CALIDAD_REACTIVA_NEW ({columns_str}) VALUES ({placeholders})"
    
    # Insertar por lotes
    for i in range(0, total_rows, batch_size):
        try:
            batch_df = df.iloc[i:min(i+batch_size, total_rows)]
            batch_data = []
            
            for _, row in batch_df.iterrows():
                # Convertir valores
                row_data = []
                for val in row:
                    if pd.isna(val) or val is None:
                        row_data.append(None)
                    else:
                        if isinstance(val, (datetime, pd.Timestamp)):
                            row_data.append(val)
                        else:
                            # Convertir a string y limitar longitud
                            str_val = str(val)
                            if isinstance(str_val, str):
                                str_val = str_val[:32000]  # Límite seguro para SQL Server
                            row_data.append(str_val)
                batch_data.append(tuple(row_data))
            
            # Ejecutar la inserción
            cursor.executemany(insert_sql, batch_data)
            conn.commit()
            logger.info(f"Lote {i//batch_size + 1}/{(total_rows-1)//batch_size + 1} insertado exitosamente")
        
        except Exception as e:
            logger.error(f"Error insertando lote {i//batch_size + 1}: {e}")
            conn.rollback()
            # Continuar con el siguiente lote
    
    # SP_INSERT_CALIDAD_REACTIVA
    try:
        cursor.execute("EXEC SP_INSERT_CALIDAD_REACTIVA 1")
        conn.commit()
        logger.info("✓ SP_INSERT_CALIDAD_REACTIVA ejecutado exitosamente")
    except Exception as e:
        logger.error(f"Error al ejecutar SP_INSERT_CALIDAD_REACTIVA: {e}")
        conn.rollback()

    # Registro de ejecución exitosa
    try:
        fecha_actual = datetime.today().date()
        proceso_nombre = 'Calidad_comisiones_tqw'
        cursor.execute(f"INSERT INTO tb_py_procesoEjecucion (FechaEjecucion, EjecucionExitosa, Proceso) VALUES ('{fecha_actual}', 1, '{proceso_nombre}')")
        conn.commit()
        logger.info("✓ Marca de ejecución registrada")
    except Exception as e:
        logger.error(f"Error al registrar ejecución: {e}")
        conn.rollback()

    logger.info("✓ Datos subidos exitosamente a SQL Server")
    return True

def execute_stored_procedures(conn):
    """Ejecuta los procedimientos almacenados necesarios"""
    logger.info("Ejecutando procedimientos almacenados...")
    cursor = conn.cursor()
    
    # SP_FACTURA_2023
    try:
        cursor.execute(f"EXEC SP_FACTURA_2023 '{FECHA_FIN}'")
        conn.commit()
        logger.info("✓ SP_FACTURA_2023 ejecutado exitosamente")
    except Exception as e:
        logger.error(f"Error al ejecutar SP_FACTURA_2023: {e}")
        conn.rollback()
    
    # SP_TQW_COMISION_2023
    try:
        cursor.execute(f"EXEC SP_TQW_COMISION_2023 '{FECHA_FIN}'")
        conn.commit()
        logger.info("✓ SP_TQW_COMISION_2023 ejecutado exitosamente")
    except Exception as e:
        logger.error(f"Error al ejecutar SP_TQW_COMISION_2023: {e}")
        conn.rollback()
    
    # SP_TQW_COMISION_2023_V2
    try:
        cursor.execute(f"EXEC SP_TQW_COMISION_2023_V2 '{FECHA_FIN}'")
        conn.commit()
        logger.info("✓ SP_TQW_COMISION_2023_V2 ejecutado exitosamente")
    except Exception as e:
        logger.error(f"Error al ejecutar SP_TQW_COMISION_2023_V2: {e}")
        conn.rollback()
    
    logger.info("✓ Procedimientos almacenados ejecutados exitosamente")

def get_sql_data(sql_conn):
    """Obtiene datos de la vista vw_CalidadFlujo"""
    logger.info("Obteniendo datos de vw_CalidadFlujo...")
    query = f"SELECT * FROM vw_CalidadFlujo WHERE FORMAT(MES_CONTABLE,'yyyyMM') BETWEEN '{FECHA_INICIO}' AND '{FECHA_FIN}'"
    df = pd.read_sql(query, sql_conn)
    df['fecha_actualizacion'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"✓ Datos obtenidos: {len(df)} filas")
    return df

def migrate_to_mysql(data_df, mysql_conn):
    """Migra los datos a MySQL"""
    logger.info("Subiendo datos a MySQL...")
    
    # Verificar la conexión MySQL y reconectar si es necesario
    try:
        mysql_conn.ping(reconnect=True, attempts=3, delay=5)
        logger.info("Conexión a MySQL verificada")
    except mysql.connector.Error as err:
        logger.warning(f"Reconectando a MySQL: {err}")
        # Si la reconexión falla, intentar crear una nueva conexión
        mysql_conn = connect_to_mysql()
    
    cursor = mysql_conn.cursor()
    
    # Eliminar tabla si existe
    cursor.execute("DROP TABLE IF EXISTS tb_py_flujo_calidad")
    mysql_conn.commit()
    
    # Crear tabla
    columns_sql = []
    for col in data_df.columns:
        col_name = f"`{col}`"
        columns_sql.append(f"{col_name} TEXT")
    
    create_table_sql = f"CREATE TABLE tb_py_flujo_calidad ({', '.join(columns_sql)})"
    cursor.execute(create_table_sql)
    mysql_conn.commit()
    
    # Insertar datos en lotes
    batch_size = 1000
    total_rows = len(data_df)
    
    # Preparar consulta
    placeholders = ', '.join(['%s' for _ in data_df.columns])
    columns_names = ', '.join([f"`{col}`" for col in data_df.columns])
    insert_sql = f"INSERT INTO tb_py_flujo_calidad ({columns_names}) VALUES ({placeholders})"
    
    # Insertar en lotes
    for i in range(0, total_rows, batch_size):
        batch_df = data_df.iloc[i:i+batch_size]
        batch_data = []
        
        for _, row in batch_df.iterrows():
            row_data = []
            for val in row:
                if pd.isna(val) or val is None:
                    row_data.append(None)
                else:
                    if isinstance(val, (datetime, pd.Timestamp)):
                        row_data.append(val.strftime("%Y-%m-%d %H:%M:%S"))
                    else:
                        row_data.append(str(val))
            batch_data.append(tuple(row_data))
        
        cursor.executemany(insert_sql, batch_data)
        mysql_conn.commit()
        logger.info(f"MySQL: Lote {i//batch_size + 1}/{(total_rows-1)//batch_size + 1} insertado")
    
    logger.info("✓ tb_py_flujo_calidad actualizada")

def migrate_table(query, mysql_table_name, sql_conn, mysql_conn):
    """Migra una tabla de SQL Server a MySQL"""
    logger.info(f"Migrando datos a tabla MySQL: {mysql_table_name}")
    
    # Obtener datos de SQL Server
    try:
        df = pd.read_sql(query, sql_conn)
        df['fecha_actualizacion'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logger.error(f"Error al obtener datos para {mysql_table_name}: {e}")
        raise
    
    # Verificar la conexión MySQL y reconectar si es necesario
    try:
        mysql_conn.ping(reconnect=True, attempts=3, delay=5)
        logger.info(f"Conexión a MySQL verificada para {mysql_table_name}")
    except mysql.connector.Error as err:
        logger.warning(f"Reconectando a MySQL para {mysql_table_name}: {err}")
        # Si la reconexión falla, intentar crear una nueva conexión
        mysql_conn = connect_to_mysql()
    
    # Eliminar tabla destino si existe
    mysql_cursor = mysql_conn.cursor()
    try:
        mysql_cursor.execute(f"DROP TABLE IF EXISTS {mysql_table_name}")
        mysql_conn.commit()
    except Exception as e:
        logger.error(f"Error al eliminar tabla {mysql_table_name}: {e}")
        raise
    
    # Crear tabla
    try:
        columns_sql = []
        for col in df.columns:
            col_name = f"`{col}`"
            columns_sql.append(f"{col_name} TEXT")
        
        create_table_sql = f"CREATE TABLE {mysql_table_name} ({', '.join(columns_sql)})"
        mysql_cursor.execute(create_table_sql)
        mysql_conn.commit()
    except Exception as e:
        logger.error(f"Error al crear tabla {mysql_table_name}: {e}")
        raise
    
    # Verificar si hay datos
    total_rows = len(df)
    if total_rows == 0:
        logger.warning(f"No hay datos para migrar a {mysql_table_name}")
        return 0
    
    # Insertar datos en lotes
    batch_size = 1000
    
    # Preparar consulta
    placeholders = ', '.join(['%s' for _ in df.columns])
    columns_names = ', '.join([f"`{col}`" for col in df.columns])
    insert_sql = f"INSERT INTO {mysql_table_name} ({columns_names}) VALUES ({placeholders})"
    
    # Insertar en lotes
    for i in range(0, total_rows, batch_size):
        try:
            batch_df = df.iloc[i:i+batch_size]
            batch_data = []
            
            for _, row in batch_df.iterrows():
                row_data = []
                for val in row:
                    if pd.isna(val) or val is None:
                        row_data.append(None)
                    else:
                        if isinstance(val, (datetime, pd.Timestamp)):
                            row_data.append(val.strftime("%Y-%m-%d %H:%M:%S"))
                        else:
                            row_data.append(str(val))
                batch_data.append(tuple(row_data))
            
            mysql_cursor.executemany(insert_sql, batch_data)
            mysql_conn.commit()
            logger.info(f"MySQL {mysql_table_name}: Lote {i//batch_size + 1}/{(total_rows-1)//batch_size + 1} insertado")
        except Exception as e:
            logger.error(f"Error insertando lote {i//batch_size + 1} en {mysql_table_name}: {e}")
            mysql_conn.rollback()
    
    logger.info(f"✓ {mysql_table_name} actualizada con {total_rows} filas")
    return total_rows

def migrate_all_tables(sql_conn, mysql_conn):
    """Migra todas las tablas necesarias"""
    # Verificar y posiblemente reconectar a MySQL antes de iniciar las migraciones
    try:
        mysql_conn.ping(reconnect=True, attempts=3, delay=5)
        logger.info("Conexión a MySQL verificada antes de migrar todas las tablas")
    except mysql.connector.Error as err:
        logger.warning(f"Reconectando a MySQL antes de migrar todas las tablas: {err}")
        mysql_conn = connect_to_mysql()
    
    # Migrar tablas
    tables_to_migrate = [
        ("SELECT * FROM TB_TQW_COMISION_2023", "tb_paso_kpi2023"),
        ("SELECT * FROM vw_CalidadFlujo_kpi", "vw_calidadflujo_kpi"),
        ("SELECT * FROM TB_KPI_GERENCIA_CALIDAD_TECNICO", "tb_kpi_gerencia_calidad_tecnico")
    ]
    
    for query, table_name in tables_to_migrate:
        try:
            migrate_table(query, table_name, sql_conn, mysql_conn)
        except Exception as e:
            logger.error(f"Error migrando tabla {table_name}: {e}")
    
    # Verificar de nuevo la conexión antes de la última tabla importante
    try:
        mysql_conn.ping(reconnect=True, attempts=3, delay=5)
        logger.info("Conexión a MySQL verificada antes de migrar TB_TQW_COMISION_RENEW")
    except mysql.connector.Error as err:
        logger.warning(f"Reconectando a MySQL antes de migrar TB_TQW_COMISION_RENEW: {err}")
        mysql_conn = connect_to_mysql()
    
    # Migrar TB_TQW_COMISION_RENEW
    logger.info("Migrando TB_TQW_COMISION_RENEW a MySQL...")
    try:
        rows_migrated = migrate_table("SELECT * FROM TB_TQW_COMISION_RENEW", "tb_tqw_comision_renew", sql_conn, mysql_conn)
        logger.info(f"✓ TB_TQW_COMISION_RENEW migrada exitosamente a MySQL con {rows_migrated} filas")
    except Exception as e:
        logger.error(f"Error migrando TB_TQW_COMISION_RENEW: {e}")
        with open('error_log.txt', 'a') as log_file:
            log_file.write(f"{datetime.now()} - Error migrando TB_TQW_COMISION_RENEW: {str(e)}\n")

def main():
    """Función principal del script"""
    logger.info("=== INICIANDO PROCESO CALIDAD NEW 23 ===")
    logger.info("VALIDACIÓN DE FECHA DE ARCHIVO DESHABILITADA TEMPORALMENTE PARA HOY")
    
    sql_conn = None
    mysql_conn = None
    
    try:
        # Conexiones a bases de datos
        sql_conn = connect_to_sql_server()
        mysql_conn = connect_to_mysql()
        
        # Leer archivo de datos
        df_data = read_data_file()
        
        # Subir datos a SQL Server
        upload_to_sql_server(df_data, sql_conn)
        
        # Ejecutar SPs
        execute_stored_procedures(sql_conn)
        
        # Obtener datos para MySQL
        data_df = get_sql_data(sql_conn)
        
        # Verificar conexión MySQL antes de migrar
        try:
            mysql_conn.ping(reconnect=True, attempts=3, delay=5)
            logger.info("Conexión a MySQL verificada antes de iniciar migraciones")
        except mysql.connector.Error as err:
            logger.warning(f"Reconectando a MySQL antes de iniciar migraciones: {err}")
            mysql_conn = connect_to_mysql()
        
        # Migrar a MySQL
        migrate_to_mysql(data_df, mysql_conn)
        
        # Migrar todas las tablas necesarias
        migrate_all_tables(sql_conn, mysql_conn)
        
        logger.info("=== PROCESO COMPLETADO EXITOSAMENTE ===")
        
    except FileNotFoundError as e_fnf:
        logger.error(f"Error Crítico: Archivo no encontrado.")
        logger.error(f"Detalles: {e_fnf}")
        logger.error(f"Traceback completo:\n{traceback.format_exc()}")
        sys.exit(1)
        
    except ValueError as e_val:
        logger.error(f"Error Crítico: Problema con los datos o valores.")
        logger.error(f"Detalles: {e_val}")
        logger.error(f"Traceback completo:\n{traceback.format_exc()}")
        sys.exit(1)
        
    except pyodbc.Error as e_sql:
        logger.error(f"Error Crítico: Problema con la conexión o consulta a SQL Server.")
        logger.error(f"Detalles: {e_sql}")
        logger.error(f"Traceback completo:\n{traceback.format_exc()}")
        sys.exit(1)
        
    except mysql.connector.Error as e_mysql:
        logger.error(f"Error Crítico: Problema con la conexión o consulta a MySQL.")
        logger.error(f"Detalles: {e_mysql}")
        logger.error(f"Traceback completo:\n{traceback.format_exc()}")
        sys.exit(1)
        
    except Exception as e_main:
        logger.error(f"Error Crítico: Ocurrió un error inesperado en el script.")
        logger.error(f"Tipo de error: {type(e_main).__name__}")
        logger.error(f"Detalles: {e_main}")
        logger.error(f"Traceback completo:\n{traceback.format_exc()}")
        sys.exit(1)
        
    finally:
        # Cerrar conexiones
        if sql_conn:
            sql_conn.close()
            logger.info("Conexión a SQL Server cerrada")
            
        if mysql_conn:
            mysql_conn.close()
            logger.info("Conexión a MySQL cerrada")
        
        logger.info("Proceso finalizado")

if __name__ == "__main__":
    main()