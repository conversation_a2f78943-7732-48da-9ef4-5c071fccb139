from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import <PERSON>Chains
from selenium.common.exceptions import TimeoutException
import time
import os
from PIL import Image
import io

def capture_power_bi_report(url, output_path):
    # Configuración del WebDriver para Edge
    edge_options = webdriver.EdgeOptions()
    edge_options.add_argument('--headless')
    edge_options.add_argument('--ignore-certificate-errors')
    edge_options.add_argument('--disable-dev-shm-usage')
    edge_options.add_argument('--no-sandbox')
    edge_options.add_argument('--disable-extensions')
    edge_options.add_argument('--disable-gpu')
    edge_options.add_argument('--start-maximized')
    edge_options.add_argument('--window-size=1920,1080')
    
    # Agregar argumentos para manejar Power BI específicamente
    edge_options.add_argument('--disable-blink-features=AutomationControlled')
    edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    edge_options.add_experimental_option('useAutomationExtension', False)
    
    # Usar driver local
    script_dir = os.path.dirname(os.path.abspath(__file__))
    msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")

    if not os.path.exists(msedgedriver_path):
        print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
        print("Por favor, descargue la versión más reciente desde:")
        print("https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
        print("y descomprímalo en esta carpeta")
        exit(1)

    print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
    service = Service(msedgedriver_path)
    driver = webdriver.Edge(service=service, options=edge_options)
    
    try:
        # Navegar a la URL
        print(f"Navegando a: {url}")
        driver.get(url)
        
        # Esperar a que se cargue Power BI
        print("Esperando carga de Power BI...")
        time.sleep(15)  # Tiempo inicial de carga
        
        # Esperar a que aparezca el contenedor principal del reporte
        try:
            report_container = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.CLASS_NAME, 'reportContainer'))
            )
            print("Contenedor del reporte encontrado")
        except TimeoutException:
            print("No se encontró el contenedor del reporte, continuando...")
        
        # Configurar viewport
        driver.set_window_size(1920, 1080)
        
        # Obtener dimensiones totales
        total_height = driver.execute_script("""
            return Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            )
        """)
        
        viewport_height = driver.execute_script("return window.innerHeight")
        viewport_width = driver.execute_script("return window.innerWidth")
        
        print(f"Altura total: {total_height}px, Viewport: {viewport_height}px")
        
        # Lista para almacenar capturas
        screenshots = []
        current_position = 0
        
        # Capturar en secciones
        while current_position < total_height:
            # Scroll a la posición actual
            driver.execute_script(f"window.scrollTo(0, {current_position})")
            
            # Esperar a que se cargue el contenido
            time.sleep(3)
            
            # Intentar hacer clic en elementos expandibles si existen
            try:
                expand_buttons = driver.find_elements(By.CSS_SELECTOR, '[aria-expanded="false"]')
                for btn in expand_buttons:
                    ActionChains(driver).move_to_element(btn).click().perform()
                    time.sleep(1)
            except:
                pass
            
            # Capturar pantalla
            screenshot = driver.get_screenshot_as_png()
            img = Image.open(io.BytesIO(screenshot))
            
            # Recortar al viewport
            img = img.crop((0, 0, viewport_width, viewport_height))
            screenshots.append(img)
            
            # Mover a la siguiente sección
            current_position += viewport_height - 100  # Superposición para evitar cortes
            
            print(f"Capturado sección {len(screenshots)}, posición: {current_position}/{total_height}")
        
        # Combinar todas las capturas
        if screenshots:
            final_height = sum(img.height for img in screenshots)
            final_width = screenshots[0].width
            final_image = Image.new('RGB', (final_width, final_height), (255, 255, 255))
            
            y_offset = 0
            for img in screenshots:
                final_image.paste(img, (0, y_offset))
                y_offset += img.height
            
            final_image.save(output_path, 'PNG', optimize=True)
            print(f"Captura completa guardada en: {output_path}")
            print(f"Dimensiones finales: {final_width}x{final_height}")
        else:
            print("No se pudo capturar ninguna imagen")
    
    except Exception as e:
        print(f"Error durante la captura: {str(e)}")
        # Captura de emergencia
        driver.save_screenshot(output_path)
        print(f"Captura de emergencia guardada en: {output_path}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    # URL del reporte de Power BI
    url = "https://app.powerbi.com/view?r=eyJrIjoiNzA2ZjEwZmYtMzUyZS00OTBjLThjMTQtNzY3N2NlNDI5YmZiIiwidCI6ImE0ZDNhYWYwLWJlZjAtNDAzMS1iZGQ3LTM1MzZkYTFmMjQ2ZCJ9"
    
    # Ruta de salida
    output_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "powerbi_report_full.png")
    
    print("Iniciando captura de reporte de Power BI...")
    capture_power_bi_report(url, output_path)
    print("Proceso completado.")