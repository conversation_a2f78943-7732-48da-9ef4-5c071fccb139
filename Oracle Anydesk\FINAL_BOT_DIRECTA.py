# from selenium import webdriver
# from selenium.webdriver.common.by import By  # Importa la clase By
# from selenium.webdriver.common.keys import Keys
import time
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriveFr.support import expected_conditions as EC

from sqlalchemy import create_engine
import pandas as pd
from sqlalchemy.orm import sessionmaker

import psutil
from sqlalchemy import text
import sqlalchemy

import os
import glob
import platform
import subprocess

import pyautogui
import cv2
import numpy as np
import sys
from datetime import datetime

import signal

import win32gui
import win32com.client
from aux_mov import DatabaseManager


def cerrar_proceso(nombre_proceso):
    procesos_cerrados = 0
    for proc in psutil.process_iter():
        try:
            if proc.name().lower() == nombre_proceso.lower():
                proc.terminate()
                procesos_cerrados += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if procesos_cerrados > 0:
        print(f"Se cerraron {procesos_cerrados} procesos de {nombre_proceso}.")
    else:
        print(f"No se encontraron procesos de {nombre_proceso}.")



def cerrar_ventana_edge():
    def enum_windows_callback(hwnd, results):
        if win32gui.IsWindowVisible(hwnd):
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            print(f"Ventana encontrada - Título: '{title}', Clase: '{class_name}'")
            if "Edge" in title or "Microsoft" in title:
                results.append((hwnd, title, class_name))

    edge_windows = []
    win32gui.EnumWindows(enum_windows_callback, edge_windows)

    if edge_windows:
        print("Ventanas de Edge encontradas:")
        for hwnd, title, class_name in edge_windows:
            print(f"  Handle: {hwnd}, Título: '{title}', Clase: '{class_name}'")
        
        # Intentar cerrar la primera ventana encontrada
        edge_hwnd, _, _ = edge_windows[0]
        shell = win32com.client.Dispatch("WScript.Shell")
        win32gui.SetForegroundWindow(edge_hwnd)
        shell.SendKeys("%{F4}")
        print(f"Intento de cerrar ventana con handle {edge_hwnd}")
    else:
        print("No se encontraron ventanas que parezcan ser Edge.")

# Usa la función
cerrar_ventana_edge()



class ImageNotFoundError(Exception):
    pass

def buscar_imagen_en_pantalla(imagen_a_buscar):
    try:
        # Imprime el nombre del archivo
        print(f"Buscando la imagen: {imagen_a_buscar}")

        # Toma una captura de pantalla
        screenshot = pyautogui.screenshot()
        screenshot_np = np.array(screenshot)
        screenshot_gray = cv2.cvtColor(screenshot_np, cv2.COLOR_BGR2GRAY)

        # Lee la imagen a buscar y conviértela a escala de grises
        template = cv2.imread(imagen_a_buscar, cv2.IMREAD_GRAYSCALE)

        w, h = template.shape[::-1]

        # Usa el método matchTemplate para buscar la imagen en la captura de pantalla
        result = cv2.matchTemplate(screenshot_gray, template, cv2.TM_CCOEFF_NORMED)
        loc = np.where(result >= 0.90)  # Puedes ajustar el umbral según tus necesidades

        # Si encontró la imagen, devuelve las coordenadas del centro
        for pt in zip(*loc[::-1]):
            centro_x = pt[0] + w // 2
            centro_y = pt[1] + h // 2
            return (centro_x, centro_y)

        # Si no encontró la imagen, verifica si es una de las excepciones
        if imagen_a_buscar in ["C:\\Users\\<USER>\\Desktop\\SecurityBox.png",
                                "C:\\Users\\<USER>\\Desktop\\SecurityRun.png",
                                "C:\\Users\\<USER>\\Desktop\\UsuarioChrome.png",
                                "C:\\Users\\<USER>\\Desktop\\CloseRestore.png",
                                "C:\\Users\\<USER>\\Desktop\\edge_search.png",
                                "C:\\Users\\<USER>\\Desktop\\edge_conservar.png",
                                "C:\\Users\\<USER>\\Desktop\\G40Stgo_directo.png",
                                "C:\\Users\\<USER>\\Desktop\\G40Stgo_directo2.png",                    
                                "C:\\Users\\<USER>\\Desktop\\D31Vina_directa2.png",
                                "C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png", 
                                "C:\\Users\\<USER>\\Desktop\\J63Conce_directa.png", 
                                "C:\\Users\\<USER>\\Desktop\\J63Conce_directa2.png",
                                "C:\\Users\\<USER>\\Desktop\\A90Anto_directa.png",
                                "C:\\Users\\<USER>\\Desktop\\A90Anto_directa2.png",
                                "C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta.png",
                                "C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta2.png",                                                                    
                                "C:\\Users\\<USER>\\Desktop\\UsuarioOracleJcepeda.png"]:            
            print(f"No se encontró la imagen {imagen_a_buscar}, pero se permite continuar.")
            return None

        cerrar_proceso("jp2launcher.exe")
        cerrar_ventana_edge()
        # cerrar_proceso("msedge.exe")
        # cerrar_chrome_suavemente()
        return "IMAGE_NOT_FOUND"

    except Exception as e:

                
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge.png')
        if coordenadas:
            pyautogui.rightClick(coordenadas[0], coordenadas[1])


        time.sleep(3)
        coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CloseWindows.png')
        if coordenadas:
            pyautogui.click(coordenadas[0], coordenadas[1])

            
        print(f"Ocurrió un error durante la búsqueda de la imagen: {str(e)}")
        return "IMAGE_NOT_FOUND"



coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(10)

archivo1 = 'C:\\Users\\<USER>\\Desktop\\edge_search.png'
archivo2 = 'C:\\Users\\<USER>\\Desktop\\edge_search2.png'

coordenadas = buscar_imagen_en_pantalla(archivo1)
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])
else:
    coordenadas = buscar_imagen_en_pantalla(archivo2)
    if coordenadas:
        pyautogui.click(coordenadas[0], coordenadas[1])
    else:
        print("No se encontró ninguna de las imágenes")



time.sleep(7)

paralabra = "ebs.andes.vtr.cl:8000/OA_HTML/AppsLocalLogin.jsp?requestUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FOA.jsp%3Fpage%3D%2Foracle%2Fapps%2Ffnd%2Fframework%2Fnavigate%2Fwebui%2FNewHomePG%26homePage%3DY%26OAPB%3DFWK_HOMEPAGE_BRAND%26oapc%3D2%26transactionid%3D146981584%26oas%3DCfzXvWhkd4wc5PbN_4wdyQ..&cancelUrl=http%3A%2F%2Febs.andes.vtr.cl%3A8000%2FOA_HTML%2FAppsLogin&langCode=US" # Escribe la palabra en el campo de texto
pyautogui.write(paralabra)


time.sleep(8)

# Presiona la tecla Enter para confirmar la entrada (opcional)
pyautogui.press('enter')

time.sleep(8)


clave = "jcepeda"
pyautogui.write(clave)

time.sleep(2)
pyautogui.press('tab')
time.sleep(2)
clave = "peru2025"
pyautogui.write(clave)


time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\LoginOracle_edge.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(7)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OracleNivel1.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(6)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OracleNivel2.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\OracleNivel3.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(5)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge_conservar.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(5)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\edge_abrir_archivo.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])





##############################################################
##############################################################
##############################################################
######################### ASDSADS #########################
##############################################################
##############################################################
##############################################################

time.sleep(5)


# # Obtener la ruta de la carpeta de Descargas
# carpeta_descargas = os.path.expanduser("~/Downloads")

# # Obtener el archivo más reciente
# ultimo_archivo = max(
#     [os.path.join(carpeta_descargas, f) for f in os.listdir(carpeta_descargas)],
#     key=os.path.getctime
# )

# print(f"Abriendo el archivo más reciente: {os.path.basename(ultimo_archivo)}")

# # Abrir el archivo con la aplicación predeterminada
# if os.name == 'nt':  # Para Windows
#     os.startfile(ultimo_archivo)
# else:  # Para macOS y Linux
#     subprocess.call(('xdg-open', ultimo_archivo))


##############################################################
##############################################################



# time.sleep(10)
# coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\abrirArchivo.png')
# if coordenadas:
#     pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(15)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\SecurityBox.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(14)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\SecurityRun.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


######## LINEA 153 OK


time.sleep(14)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX_azul.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(14)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\STGO_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(35)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\G40Stgo_directo.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\G40Stgo_directo2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(55)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


# jp2launcher.exe

time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(160)

engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@************/telqway?driver=ODBC Driver 17 for SQL Server')
Session = sessionmaker(bind=engine)
session = Session()
session.execute(text("TRUNCATE TABLE TB_PASO_0_ORACLE_DIRECTAX"))
session.commit()

list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')
# Crear el diccionario de tipos de datos personalizados
custom_dtypes = {
'Serial': sqlalchemy.types.String(50),
'Item': sqlalchemy.types.String(50),
'Org': sqlalchemy.types.String(50),
'Revision': sqlalchemy.types.String(50),
'Subinventory': sqlalchemy.types.String(50),
'Locator': sqlalchemy.types.String(50),
'Operation': sqlalchemy.types.String(50),
'Job': sqlalchemy.types.String(50),
'Step': sqlalchemy.types.String(50),
'Lot': sqlalchemy.types.String(50),
'State': sqlalchemy.types.String(50),
'Status': sqlalchemy.types.String(50),
'Receipt Date': sqlalchemy.types.String(50),
'Ship Date': sqlalchemy.types.String(50),
'Supplier Name': sqlalchemy.types.String(50),
'Supplier Lot': sqlalchemy.types.String(50),
'Supplier Serial': sqlalchemy.types.String(50),
'Unit Number': sqlalchemy.types.String(50),
'Attributes': sqlalchemy.types.String(50),
'[  ]': sqlalchemy.types.String(50),
'Unnamed: 20': sqlalchemy.types.String(50)
}

# Truncar las cadenas a un máximo de 100 caracteres
#  df_truncated = df_dtype_str.applymap(lambda x: x[:100])
# Insertar el DataFrame en la tabla SQL
df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")

time.sleep(14)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Vina.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D31Vina_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\D31Vina_directa2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(20)
coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(24)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(24)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(20)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(120)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(4)

df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame: {len(df)}")

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)

############################################################
########## CONCE ###########################################
############################################################

time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Conce.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)


pyautogui.scroll(-1200)

pyautogui.scroll(-1200)

pyautogui.scroll(-1200)

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J63Conce_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J63Conce_directa2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(36)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(15)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(15)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(14)

df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame de CONCE: {len(df)}")

time.sleep(12)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(12)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])






############################################################
########## ANTO ###########################################
############################################################

time.sleep(7)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\AntoDirecta.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(8)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(8)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(12)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\A90Anto_directa.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\A90Anto_directa2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(24)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(12)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(12)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(10)

df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame de CONCE: {len(df)}")

time.sleep(10)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(9)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Sombrero.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(9)





############################################################
########## TEMUCO ###########################################
############################################################

time.sleep(9)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\TemucoDirecta.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])



time.sleep(9)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccion.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])




time.sleep(9)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ConsultaTransaccionSeries.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])

time.sleep(3)


pyautogui.scroll(-1200)

pyautogui.scroll(-1200)

pyautogui.scroll(-1200)



time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(3)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\J67_TemucoDirecta2.png')
if coordenadas:
    pyautogui.doubleClick(coordenadas[0], coordenadas[1])


time.sleep(5)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Find.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(12)


coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\File.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])




time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\Export.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])



time.sleep(4)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\ContinueEnd.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])


time.sleep(60)


list_of_files = glob.glob('C:/Users/<USER>/Downloads/*') # * means all if need specific format then *.csv
latest_file = max(list_of_files, key=os.path.getctime)

print(latest_file)

df = pd.read_csv(latest_file, sep='\t')

time.sleep(4)







df.to_sql('TB_PASO_0_ORACLE_DIRECTAX', con=engine, if_exists='append', index=False,dtype=custom_dtypes)
print(f"Total de registros en el DataFrame de CONCE: {len(df)}")

time.sleep(2)

coordenadas = buscar_imagen_en_pantalla('C:\\Users\\<USER>\\Desktop\\CierreX.png')
if coordenadas:
    pyautogui.click(coordenadas[0], coordenadas[1])

time.sleep(3)



def ejecutar_sincronizacion():
    db_manager = DatabaseManager()
    try:
        db_manager.process_oracle_data()
    finally:
        db_manager.cleanup()

if __name__ == "__main__":
    ejecutar_sincronizacion()





