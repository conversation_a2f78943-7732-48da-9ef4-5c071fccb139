import pandas as pd
from sqlalchemy import create_engine
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import os

# Configuración del servidor SMTP y credenciales
smtp_server = 'mail.telqway.cl'
smtp_port = 465
smtp_username = '<EMAIL>'
smtp_password = 'N1c0l7as17#'

# Detalles del correo electrónico
remitente = '<EMAIL>'

# Configuración de la conexión a la base de datos
engineMYsql = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@**************:3306/operaciones_tqw', echo=False)

# Consulta SQL
query = """
 select email, nombre, correo_super, B.pass_new
 from tb_user_tqw A
 left join tb_claves_usuarios B
 ON A.email = B.usuario
 where id >= 1194
"""

# Ejecutar la consulta y obtener los resultados
df = pd.read_sql(query, engineMYsql)

# Función para enviar correo
def enviar_correo(destinatario, cc, nombre, email, clave):
    mensaje = MIMEMultipart()
    mensaje['From'] = remitente
    mensaje['To'] = destinatario
    mensaje['Cc'] = f"{cc}, <EMAIL>"
    mensaje['Subject'] = "Credenciales de acceso al portal de operaciones"
    
    cuerpo = f"""
    Buenas tardes {nombre},

    Con saludar te recordamos a través de este correo las credenciales de acceso al portal de operaciones https://operacionesapp.telqway.cl/

    Usuario: {email}
    Clave: {clave}

    Atentamente,
    Equipo Telqway
    """
    
    mensaje.attach(MIMEText(cuerpo, 'plain'))
    
    try:
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(smtp_username, smtp_password)
            server.send_message(mensaje)
        print(f"Correo enviado exitosamente a {destinatario}")
    except Exception as e:
        print(f"Error al enviar correo a {destinatario}: {str(e)}")

# Enviar correos para cada registro
for index, row in df.iterrows():
    enviar_correo(row['email'], row['correo_super'], row['nombre'], row['email'], row['pass_new'])

print("Proceso de envío de correos completado.")