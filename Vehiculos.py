import pandas as pd
import datetime as date
import requests
import json
from sqlalchemy import create_engine, <PERSON><PERSON><PERSON>, Integer, String, Float
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timedelta

import sqlalchemy
import mysql.connector
from sqlalchemy import create_engine


url = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vR9SH5FHbRyI6o4ECSpYPVWhhslpRa_fmOErkDc9h5SRUjbm3ifeDuMLM9g-RprXLB2aZcrbLQKDxMS/pub?gid=507285432&single=true&output=csv'
df = pd.read_csv(url)

num_datos, num_columnas = df.shape
print(f"El DataFrame tiene {num_datos} datos distribuidos en {num_columnas} columnas.")


engine = create_engine('mssql+pyodbc://ncornejo:N1c0l7as17@20.20.20.207/telqway?driver=ODBC Driver 17 for SQL Server')
#engine = create_engine(conn_str = 'mssql+pyodbc://sa:N1c0l7as@181.212.32.10/master?driver=ODBC+Driver+17+for+SQL+Server')
Session = sessionmaker(bind=engine)
session = Session()


data_types = {
'Patente': sqlalchemy.types.VARCHAR(50),
'Conductor': sqlalchemy.types.VARCHAR(50),
'RUT conductor': sqlalchemy.types.VARCHAR(50),
'fecha ulti movimiento': sqlalchemy.types.VARCHAR(50),
'Q dias': sqlalchemy.types.VARCHAR(50),
'Observacion formulario': sqlalchemy.types.VARCHAR(50),
'Modelo': sqlalchemy.types.VARCHAR(50),
'Año': sqlalchemy.types.VARCHAR(50),
'Propiedad': sqlalchemy.types.VARCHAR(50),
'Región': sqlalchemy.types.VARCHAR(50),
'Area responsable': sqlalchemy.types.VARCHAR(50),
'Proyecto': sqlalchemy.types.VARCHAR(50),
'Supervisor': sqlalchemy.types.VARCHAR(50),
'Observacion': sqlalchemy.types.VARCHAR(50),
'Estado': sqlalchemy.types.VARCHAR(50),
'Complejidad': sqlalchemy.types.VARCHAR(50),
'revision tecnica': sqlalchemy.types.VARCHAR(50),
'Gases': sqlalchemy.types.VARCHAR(50),
'Estado RT': sqlalchemy.types.VARCHAR(50),
'Estado Gases': sqlalchemy.types.VARCHAR(50),
'contar': sqlalchemy.types.VARCHAR(50),
'Estacionamiento': sqlalchemy.types.VARCHAR(50),
'Extintor fecha vencimiento': sqlalchemy.types.VARCHAR(50),
'Caja Herramientas': sqlalchemy.types.VARCHAR(50),
'LLAVE COBRO': sqlalchemy.types.VARCHAR(50),
'AREA COBRO': sqlalchemy.types.VARCHAR(50),
'COSTO DIA': sqlalchemy.types.VARCHAR(50),
    }

df.to_sql(name='tb_py_vehiculos_operaciones', con=engine, if_exists = 'replace', index=False)


session.execute("exec SP_INSERT_VEHICULOS")
session.commit()

## Close the session
session.close()




