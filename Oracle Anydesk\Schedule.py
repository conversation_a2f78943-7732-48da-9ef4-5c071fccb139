import schedule
import time
import subprocess
import pyautogui
from datetime import datetime

from sqlalchemy import text
from sqlalchemy import create_engine

pyautogui.FAILSAFE = False

engineMYsql_new = create_engine('mysql+mysqlconnector://ncornejo:N1c0l7as17@170.239.85.233:3306/operaciones_tqw', echo=False)



# def insert_log_etl_proceso(texto, total_de_datos):
#     hora_actual = datetime.now()
    
#     sql_insert = text("""
#         INSERT INTO tb_log_etl_procesos (fecha_hora, texto, total_de_datos)
#         VALUES (:fecha_hora, :texto, :total_de_datos)
#     """)
    
#     with engineMYsql_new.connect() as connection:
#         connection.execute(sql_insert, {
#             'fecha_hora': hora_actual,
#             'texto': texto,
#             'total_de_datos': total_de_datos
#         })

# Usage example:
# insert_log_etl_proceso('tabla directa', total_registros)


# Usage example:
# insert_log_etl_proceso(engineMYsql, 'tabla directa', total_registros)


# Variable global para contar los minutos
contador_minutos = 0

def ejecutar_archivo(ruta_archivo):
    result = subprocess.run(["python", ruta_archivo], capture_output=True, text=True)
    if result.returncode == 0:
        return True  # La ejecución fue exitosa
    elif "IMAGE_NOT_FOUND" in result.stdout:
        return "IMAGE_NOT_FOUND"  # No se encontró la imagen
    else:
        return False  # Hubo un error en la ejecución

def ejecutar_y_esperar():
    global contador_minutos  # Usar la variable global
    
    # Verificar la hora actual
    hora_actual = datetime.now().hour

    print(f"Hora actual: {hora_actual}")
    if hora_actual >= 7 and hora_actual < 24:   
        archivo1 = "C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Anydesk\\OPTIMIZED_BOT_DIRECTA.py"
        resultado1 = ejecutar_archivo(archivo1)
        if resultado1:
            time.sleep(10)
            print("Proceso de directa completado")
            archivo2 = "C:\\Users\\<USER>\\OneDrive - kayze\\ORACLE\\Oracle Anydesk\\OPTIMIZED_BOT_REVERSA.py"
            resultado2 = ejecutar_archivo(archivo2)
            if resultado2:
                print("Proceso de reversa completado")
                time.sleep(1800)  # Espera 30 minutos (1800 segundos) si todo se ejecutó correctamente
            else:
                print("Hubo un error en la ejecución de FINAL_BOT_REVERSA.py")
                time.sleep(300)  # Espera 90 segundos si resultado2 falla
        else:
            print("Hubo un error en la ejecución de FINAL_BOT_DIRECTA.py")
            time.sleep(10)  # Espera 90 segundos si resultado1 falla
            # insert_log_etl_proceso("logistica directa",0)

        # Incrementar el contador de minutos
        contador_minutos += 30  # Suma 30 minutos ya que se programa para ejecutar cada 30 minutos
        imprimir_contador()  # Imprime el contador actualizado
    else:
        print("Fuera del horario de ejecución (7 am - 8 pm)")
        time.sleep(1800)  # Espera 30 minutos fuera del horario de ejecución

def imprimir_contador():
    print(f"Han transcurrido {contador_minutos} minutos desde el inicio del programa.")


while True:
    ejecutar_y_esperar()

