{"tasks": {"daily": {"ReporteOtDigital": {"name": "ReporteOtDigital", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\ReporteOtDigital.py", "status": "failed", "success_count": 145, "failure_count": 1, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-09-20T11:40:00.307705", "last_duration": 2.011169, "execution_time": "11:40"}, "PyTOA_Flujo23": {"name": "PyTOA_Flujo23", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyTOA_Flujo23.py", "status": "completed", "success_count": 161, "failure_count": 2, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-09-20T05:40:00.354568", "last_duration": 2.006908, "execution_time": "05:40"}, "PyTOAFinal": {"name": "PyTOAFinal", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyTOAFinal.py", "status": "completed", "success_count": 159, "failure_count": 0, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-09-20T05:30:00.930782", "last_duration": 2.013727, "execution_time": "05:30"}, "CalidadNaranja_11AM": {"name": "CalidadNaranja_11AM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNaranja.py", "status": "completed", "success_count": 46, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-16T11:00:00.811832", "last_duration": 0.972208, "execution_time": "11:00"}, "CalidadNaranja_3PM": {"name": "CalidadNaranja_3PM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNaranja.py", "status": "completed", "success_count": 44, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-16T15:00:00.670262", "last_duration": 0.950887, "execution_time": "15:00"}, "CalidadNaranja_6PM": {"name": "CalidadNaranja_6PM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNaranja.py", "status": "completed", "success_count": 43, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-16T18:00:00.594561", "last_duration": 1.074502, "execution_time": "18:00"}, "CalidadNew23_11AM": {"name": "CalidadNew23_11AM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNew23.py", "status": "completed", "success_count": 149, "failure_count": 9, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-20T11:00:00.787302", "last_duration": 2.009393, "execution_time": "11:00"}, "CalidadNew23_3PM": {"name": "CalidadNew23_3PM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNew23.py", "status": "completed", "success_count": 145, "failure_count": 8, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-20T15:00:00.760948", "last_duration": 2.012263, "execution_time": "15:00"}, "CalidadNew23_6PM": {"name": "CalidadNew23_6PM", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNew23.py", "status": "completed", "success_count": 145, "failure_count": 2, "max_runtime": 300, "priority": 2, "retries": 2, "last_run": "2025-09-20T18:00:00.134671", "last_duration": 2.013664, "execution_time": "18:00"}, "PyDesafioTecnico": {"name": "PyDesafioTecnico", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyDesafioTecnico.py", "status": "completed", "success_count": 172, "failure_count": 1, "max_runtime": 300, "priority": 3, "retries": 2, "last_run": "2025-09-20T10:00:00.915123", "last_duration": 2.065426, "execution_time": "10:00"}, "PyDesafioTecnico2": {"name": "PyDesafioTecnico2", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyDesafioTecnico.py", "status": "completed", "success_count": 146, "failure_count": 1, "max_runtime": 300, "priority": 3, "retries": 2, "last_run": "2025-09-20T16:00:00.215574", "last_duration": 2.018112, "execution_time": "16:00"}}, "interval": {"NdcBot": {"name": "NdcBot", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\NdcBot.py", "status": "completed", "success_count": 1487, "failure_count": 2, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-09-20T19:58:32.231866", "last_duration": 2.137433, "interval": 120}, "Py_INSERT_SQL_SERVER": {"name": "Py_INSERT_SQL_SERVER", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\Py_INSERT_SQL_SERVER.py", "status": "completed", "success_count": 1683, "failure_count": 18, "max_runtime": 300, "priority": 1, "retries": 2, "last_run": "2025-09-20T19:58:32.231866", "last_duration": 2.218173, "interval": 120}, "PyTOA30": {"name": "PyTOA30", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyTOA30.py", "status": "failed", "success_count": 2052, "failure_count": 641, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-09-20T19:58:53.422709", "last_duration": 1.809258, "interval": 60, "time_window": {"start": "11:00", "end": "20:30"}}, "FlujoMysql_i": {"name": "FlujoMysql_i", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\FlujoMysql_i.py", "status": "completed", "success_count": 6678, "failure_count": 6, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-09-20T20:00:03.805496", "last_duration": 2.001216, "interval": 30}, "Turnos_Python": {"name": "Turnos_Python", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyTurnos.py", "status": "completed", "success_count": 6215, "failure_count": 20, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-09-20T20:00:03.805496", "last_duration": 2.003245, "interval": 30}, "PyLogisticaMat": {"name": "PyLogisticaMat", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyLogisticaMat.py", "status": "completed", "success_count": 4300, "failure_count": 4, "max_runtime": 180, "priority": 1, "retries": 2, "last_run": "2025-09-17T09:58:33.881584", "last_duration": 0.894262, "interval": 30}}, "manual": {"AnexoLiquidacion": {"name": "AnexoLiquidacion", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\AnexoLiquidacion.py", "status": "completed", "success_count": 9, "failure_count": 1, "max_runtime": 600, "priority": 2, "retries": 1, "last_run": "2025-05-05T12:08:09.680736"}, "AnexoPDF": {"name": "AnexoPDF", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\AnexoPDF.py", "status": "pending", "success_count": 7, "failure_count": 0, "max_runtime": 600, "priority": 2, "retries": 1}, "ETL_inventarios": {"name": "ETL_inventarios", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\ETL_inventarios.py", "status": "pending", "success_count": 12, "failure_count": 2, "max_runtime": 900, "priority": 1, "retries": 2}, "PyFormDeclaMaterial": {"name": "PyFormDeclaMaterial", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PyFormDeclaMaterial.py", "status": "pending", "success_count": 5, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 1}, "PDF_Masivo_Inventarios": {"name": "PDF_Masivo_Inventarios", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\PDF_Masivo_Inventarios.py", "status": "pending", "success_count": 4, "failure_count": 1, "max_runtime": 1200, "priority": 3, "retries": 1}, "TQW_CALIDAD_REACTIVA_mysql": {"name": "TQW_CALIDAD_REACTIVA_mysql", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\TQW_CALIDAD_REACTIVA_mysql.py", "status": "pending", "success_count": 10, "failure_count": 1, "max_runtime": 300, "priority": 2, "retries": 2}, "CalidadNew23": {"name": "CalidadNew23", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNew23.py", "status": "pending", "success_count": 9, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2}, "CalidadNaranja": {"name": "CalidadNaranja", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\CalidadNaranja.py", "status": "pending", "success_count": 9, "failure_count": 0, "max_runtime": 300, "priority": 2, "retries": 2}, "ImportSQLserve": {"name": "ImportSQLserve", "path": "c:\\\\Users\\\\<USER>\\\\Nextcloud\\\\03 Reportes Operaciones\\\\02FlujosDatos\\ImportSQLserve.py", "status": "completed", "success_count": 32, "failure_count": 3, "max_runtime": 600, "priority": 1, "retries": 2, "last_run": "2025-09-17T09:43:53.281742", "last_duration": 2.243658}}}}