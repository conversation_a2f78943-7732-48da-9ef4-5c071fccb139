2025-09-05 12:19:41,662 - INFO - ➕ APPEND mode: Table exists
2025-09-05 12:19:41,662 - INFO - 🔧 Ensuring existing table columns support unlimited text...
2025-09-05 12:19:41,662 - ERROR - ❌ Error al subir archivo a SQL Server: local variable 'sql_columns' referenced before assignment
2025-09-05 12:19:41,662 - ERROR - Traceback: Traceback (most recent call last):
  File "c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\database.py", line 510, in upload_excel_to_sql_server
    text_columns = [col for col in sql_columns if col != 'fecha_integracion']
UnboundLocalError: local variable 'sql_columns' referenced before assignment

2025-09-05 12:19:41,662 - INFO - Database connection closed
2025-09-05 12:19:41,662 - ERROR - ❌ Failed to upload Centro data to database
2025-09-05 12:19:41,662 - WARNING - ⚠️ Cycle failed, but continuing...
2025-09-05 12:19:41,662 - INFO - ====================================================================================================
2025-09-05 12:19:41,662 - INFO - 🚀 STARTING CYCLE #5 - 2025-09-05 12:19:41
2025-09-05 12:19:41,662 - INFO - ====================================================================================================
2025-09-05 12:19:41,662 - INFO - 📊 Cycle: Centro → Metropolitana → Centro → Metropolitana...
2025-09-05 12:19:41,662 - INFO - ⏹️  Press Ctrl+C to stop the permanent cycle
2025-09-05 12:19:41,662 - INFO - ====================================================================================================
2025-09-05 12:19:41,662 - INFO - 🏭 Processing Centro area...
2025-09-05 12:19:41,662 - INFO - 📊 Performing export actions...
2025-09-05 12:19:41,662 - INFO - 🖱️ Clicking Acciones button with multiple strategies...
2025-09-05 12:19:41,662 - INFO -    🔍 Trying XPath with text 'Acciones'...
2025-09-05 12:19:52,215 - WARNING -    ⚠️ XPath text failed: Message:
Stacktrace:
        GetHandleVerifier [0x0x7ff6d4428c85+23461]
        (No symbol) [0x0x7ff6d437cd70]
        GetHandleVerifier [0x0x7ff6d46a0bb8+2611928]
        (No symbol) [0x0x7ff6d41991a8]
        (No symbol) [0x0x7ff6d419946b]
        (No symbol) [0x0x7ff6d41d9a67]
        (No symbol) [0x0x7ff6d41ba6ff]
        (No symbol) [0x0x7ff6d418f58d]
        (No symbol) [0x0x7ff6d41d754f]
        (No symbol) [0x0x7ff6d41ba423]
        (No symbol) [0x0x7ff6d418ea86]
        (No symbol) [0x0x7ff6d418dd11]
        (No symbol) [0x0x7ff6d418e8b3]
        (No symbol) [0x0x7ff6d428e6fd]
        (No symbol) [0x0x7ff6d429ba88]
        GetHandleVerifier [0x0x7ff6d4508acb+940523]
        GetHandleVerifier [0x0x7ff6d4511821+976705]
        (No symbol) [0x0x7ff6d438a961]
        (No symbol) [0x0x7ff6d4383344]
        (No symbol) [0x0x7ff6d4383493]
        (No symbol) [0x0x7ff6d4374f36]
        BaseThreadInitThunk [0x0x7fffea4a84d4+20]
        RtlUserThreadStart [0x0x7fffea6e1a11+33]

2025-09-05 12:19:52,215 - INFO -    🔍 Trying CSS selectors...
2025-09-05 12:19:52,354 - INFO -    ✅ Element found by text search: 'Acciones'
2025-09-05 12:19:52,354 - INFO -    🖱️ Clicking Acciones button...
2025-09-05 12:19:53,443 - INFO - ✅ Acciones button clicked successfully
2025-09-05 12:19:56,444 - INFO - 📤 Clicking Exportar button with multiple strategies...
2025-09-05 12:19:56,444 - INFO -    🔍 Trying immediate fast search...
2025-09-05 12:20:07,002 - WARNING -    ⚠️ Immediate search failed: Message:
Stacktrace:
        GetHandleVerifier [0x0x7ff6d4428c85+23461]
        (No symbol) [0x0x7ff6d437cd70]
        GetHandleVerifier [0x0x7ff6d46a0bb8+2611928]
        (No symbol) [0x0x7ff6d41991a8]
        (No symbol) [0x0x7ff6d419946b]
        (No symbol) [0x0x7ff6d41d9a67]
        (No symbol) [0x0x7ff6d41ba6ff]
        (No symbol) [0x0x7ff6d418f58d]
        (No symbol) [0x0x7ff6d41d754f]
        (No symbol) [0x0x7ff6d41ba423]
        (No symbol) [0x0x7ff6d418ea86]
        (No symbol) [0x0x7ff6d418dd11]
        (No symbol) [0x0x7ff6d418e8b3]
        (No symbol) [0x0x7ff6d428e6fd]
        (No symbol) [0x0x7ff6d429ba88]
        GetHandleVerifier [0x0x7ff6d4508acb+940523]
        GetHandleVerifier [0x0x7ff6d4511821+976705]
        (No symbol) [0x0x7ff6d438a961]
        (No symbol) [0x0x7ff6d4383344]
        (No symbol) [0x0x7ff6d4383493]
        (No symbol) [0x0x7ff6d4374f36]
        BaseThreadInitThunk [0x0x7fffea4a84d4+20]
        RtlUserThreadStart [0x0x7fffea6e1a11+33]

2025-09-05 12:20:07,002 - INFO -    🔍 Trying XPath with text 'Exportar'...
2025-09-05 12:20:17,519 - WARNING -    ⚠️ XPath text failed: Message:
Stacktrace:
        GetHandleVerifier [0x0x7ff6d4428c85+23461]
        (No symbol) [0x0x7ff6d437cd70]
        GetHandleVerifier [0x0x7ff6d46a0bb8+2611928]
        (No symbol) [0x0x7ff6d41991a8]
        (No symbol) [0x0x7ff6d419946b]
        (No symbol) [0x0x7ff6d41d9a67]
        (No symbol) [0x0x7ff6d41ba6ff]
        (No symbol) [0x0x7ff6d418f58d]
        (No symbol) [0x0x7ff6d41d754f]
        (No symbol) [0x0x7ff6d41ba423]
        (No symbol) [0x0x7ff6d418ea86]
        (No symbol) [0x0x7ff6d418dd11]
        (No symbol) [0x0x7ff6d418e8b3]
        (No symbol) [0x0x7ff6d428e6fd]
        (No symbol) [0x0x7ff6d429ba88]
        GetHandleVerifier [0x0x7ff6d4508acb+940523]
        GetHandleVerifier [0x0x7ff6d4511821+976705]
        (No symbol) [0x0x7ff6d438a961]
        (No symbol) [0x0x7ff6d4383344]
        (No symbol) [0x0x7ff6d4383493]
        (No symbol) [0x0x7ff6d4374f36]
        BaseThreadInitThunk [0x0x7fffea4a84d4+20]
        RtlUserThreadStart [0x0x7fffea6e1a11+33]

2025-09-05 12:20:17,519 - INFO -    🔍 Trying modal button search...
2025-09-05 12:20:49,270 - INFO -    ✅ Exportar button found in modal: 'Exportar'
2025-09-05 12:20:50,342 - INFO - ✅ Exportar button clicked
2025-09-05 12:20:50,342 - INFO - ✅ Export actions completed
2025-09-05 12:20:50,343 - INFO - ⏳ Waiting for Excel download from Centro...
2025-09-05 12:20:50,344 - INFO - ⏳ Waiting for download completion (timeout: 180s)...
2025-09-05 12:20:50,352 - INFO -    📄 New file detected! (976 total files)
2025-09-05 12:20:50,353 - INFO - 🔍 Searching for RECENT Excel files in: C:\Users\<USER>\Downloads
2025-09-05 12:20:50,353 - INFO -    📋 Pattern: *.xlsx
2025-09-05 12:20:50,353 - INFO -    ⏰ Max age: 5 minutes (optimized for recent downloads)
2025-09-05 12:20:50,361 - INFO -    🔍 Reviewing 976 Excel files for very recent ones...
2025-09-05 12:20:50,367 - INFO -    ✅ Very recent file: Actividades-Area Operacion Centro_09_05_25 (14).xlsx (age: 4.8 min)
2025-09-05 12:20:50,367 - INFO -    ✅ Very recent file: Actividades-Area Operacion Centro_09_05_25 (15).xlsx (age: 3.5 min)
2025-09-05 12:20:50,367 - INFO -    ✅ Very recent file: Actividades-Area Operacion Centro_09_05_25 (16).xlsx (age: 2.3 min)
2025-09-05 12:20:50,368 - INFO -    ✅ Very recent file: Actividades-Area Operacion Centro_09_05_25 (17).xlsx (age: 1.1 min)
2025-09-05 12:20:50,409 - INFO -    🎯 SELECTED MOST RECENT FILE: Actividades-Area Operacion Centro_09_05_25 (17).xlsx
2025-09-05 12:20:50,410 - INFO -    📅 Modified: Fri Sep  5 12:19:41 2025
2025-09-05 12:20:50,410 - INFO -    📏 Size: 211983 bytes
2025-09-05 12:20:50,410 - INFO -    ⏱️  Age: 1.1 minutes
2025-09-05 12:20:50,410 - INFO -    📂 Location: C:\Users\<USER>\Downloads
2025-09-05 12:20:50,411 - INFO - 📤 Uploading Centro data to SQL Server...
2025-09-05 12:20:50,411 - INFO - 📊 Starting Excel upload to SQL Server...
2025-09-05 12:20:50,411 - INFO -    📄 File: C:\Users\<USER>\Downloads\Actividades-Area Operacion Centro_09_05_25 (17).xlsx
2025-09-05 12:20:50,412 - INFO -    🗄️  Table: tb_toa_reporte_diario
2025-09-05 12:20:50,412 - INFO -    🔄 Mode: append
2025-09-05 12:20:50,412 - INFO - 📖 Reading Excel file...
2025-09-05 12:20:50,807 - INFO - ✅ File loaded: 217 rows, 102 columns
2025-09-05 12:20:50,808 - INFO - 🕒 Integration timestamp: 2025-09-05 12:20:50.807917
2025-09-05 12:20:50,824 - INFO - ✅ Connected to SQL Server successfully
2025-09-05 12:20:50,824 - INFO - 🔍 Analyzing data structure...
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Orden de Trabajo': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Tipo de Actividad': NVARCHAR(MAX) (max 24 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Subtipo': NVARCHAR(MAX) (max 25 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Tipo de Orden': NVARCHAR(MAX) (max 9 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Franja': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Inicio': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Fin': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Cliente': NVARCHAR(MAX) (max 43 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Dirección': NVARCHAR(MAX) (max 45 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Ciudad': NVARCHAR(MAX) (max 13 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Número Cliente': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Celular': NVARCHAR(MAX) (max 13 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Clase de Vivienda': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Código Ciudad': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Código Localidad': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Código Territorio': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,824 - INFO - 📊 Column 'Código Zona': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,842 - INFO - 📊 Column 'Comentarios de la actividad': NVARCHAR(MAX) (max 463 chars)
2025-09-05 12:20:50,843 - INFO - 📊 Column 'Coord X': NVARCHAR(MAX) (max 11 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Coord Y': NVARCHAR(MAX) (max 11 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Fecha': NVARCHAR(MAX) (max 8 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Ventana de Entrega': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Descripción de la actividad': NVARCHAR(MAX) (max 36 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Duración': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'email': NVARCHAR(MAX) (max 36 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Estado de la actividad': NVARCHAR(MAX) (max 11 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Fecha Certificada': NVARCHAR(MAX) (max 19 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Flag Estado Aprovisión': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Flag Fallas Masivas': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Flag Materiales': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Notas Materiales': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Flag Niveles': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Grupo Socioeconomico': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Indicador Capacidad': NVARCHAR(MAX) (max 3 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Area derivación': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Nodo': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Nombre Completo Persona': NVARCHAR(MAX) (max 43 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Nro Orden': NVARCHAR(MAX) (max 14 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Nro Solicitud de Servicio': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Teléfono': NVARCHAR(MAX) (max 11 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Prioridad': NVARCHAR(MAX) (max 3 chars)
2025-09-05 12:20:50,844 - INFO - 📊 Column 'Razón de Cancelación': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,861 - INFO - 📊 Column 'Inicio - Fin': NVARCHAR(MAX) (max 13 chars)
2025-09-05 12:20:50,862 - INFO - 📊 Column 'Estado': NVARCHAR(MAX) (max 9 chars)
2025-09-05 12:20:50,862 - INFO - 📊 Column 'Provincia': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,864 - INFO - 📊 Column 'Subnodo': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,865 - INFO - 📊 Column 'Territorio': NVARCHAR(MAX) (max 12 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Tipo de Vivienda': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Tiempo de viaje': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Usuario Creador Actividad': NVARCHAR(MAX) (max 12 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Zona de trabajo': NVARCHAR(MAX) (max 13 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Código postal': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Zona': NVARCHAR(MAX) (max 11 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Ventana de Servicio': NVARCHAR(MAX) (max 13 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Código de Cierre': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Notas de Cierre (máx 500 caract. , sin caract especial "&")': NVARCHAR(MAX) (max 49 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'ID de actividad': NVARCHAR(MAX) (max 8 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Rut o Bucket': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Pasos': NVARCHAR(MAX) (max 1235 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Hora de reserva de actividad': NVARCHAR(MAX) (max 14 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag Corte de Acometida': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag Retiro de Materiales': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag Televisión Análoga': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag que indica si hay Internet': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag que indica si hay Telefonía': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Flag que indica si hay Televisión': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Equipos sin retirar': NVARCHAR(MAX) (max 83 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Inicio SLA': NVARCHAR(MAX) (max 14 chars)
2025-09-05 12:20:50,866 - INFO - 📊 Column 'Activity status change by': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,882 - INFO - 📊 Column 'MAC del MTA': NVARCHAR(MAX) (max 31 chars)
2025-09-05 12:20:50,884 - INFO - 📊 Column 'Tipo Red': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Código GIS': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Categorización del número de derivaciones': NVARCHAR(MAX) (max 18 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Agenda confirmada': NVARCHAR(MAX) (max 2 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Criterios de priorización': NVARCHAR(MAX) (max 21 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Cantidad de derivaciones NO terreno': NVARCHAR(MAX) (max 3 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Cantidad de derivaciones terreno': NVARCHAR(MAX) (max 3 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Bucket Original': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Tipo de work skill Siebel': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Items Orden': NVARCHAR(MAX) (max 12871 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Notas de Suspensión (máx 500 caract. , sin caract especial "&")': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Usuario que suspende': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Hora de asignación de actividad': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Cierre Suspender': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Marca': NVARCHAR(MAX) (max 5 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Tipo red producto': NVARCHAR(MAX) (max 4 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Access ID': NVARCHAR(MAX) (max 10 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Aptitud laboral': NVARCHAR(MAX) (max 95 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Flag Resultado DROP': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Flag Resultado NAP': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Tipo NAP': NVARCHAR(MAX) (max 8 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'QR DROP': NVARCHAR(MAX) (max 12 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Nap ID': NVARCHAR(MAX) (max 12 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Nap Ubicación': NVARCHAR(MAX) (max 35 chars)
2025-09-05 12:20:50,886 - INFO - 📊 Column 'Puerto NAP': NVARCHAR(MAX) (max 14 chars)
2025-09-05 12:20:50,902 - INFO - 📊 Column 'EOS asociado': NVARCHAR(MAX) (max 162 chars)
2025-09-05 12:20:50,904 - INFO - 📊 Column 'Flag Cambio Pelo': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,904 - INFO - 📊 Column 'Respuesta Intervencion Cambio Pelo': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,904 - INFO - 📊 Column 'Flag Consulta Vecino': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,904 - INFO - 📊 Column 'Notas Consulta Vecino': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,905 - INFO - 📊 Column 'Uso interno plugin cambio de pelo (no modificar)': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,905 - INFO - 📊 Column 'Intervención neutra': NVARCHAR(MAX) (empty column)
2025-09-05 12:20:50,907 - INFO - 📊 Column 'fecha_integracion': NVARCHAR(MAX) (max 26 chars)
2025-09-05 12:20:50,908 - INFO - ➕ APPEND mode: Table exists
2025-09-05 12:20:50,908 - INFO - 🔧 Ensuring existing table columns support unlimited text...
2025-09-05 12:20:50,908 - ERROR - ❌ Error al subir archivo a SQL Server: local variable 'sql_columns' referenced before assignment
2025-09-05 12:20:50,909 - ERROR - Traceback: Traceback (most recent call last):
  File "c:\Users\<USER>\Nextcloud\03 Reportes Operaciones\02FlujosDatos\database.py", line 510, in upload_excel_to_sql_server
    text_columns = [col for col in sql_columns if col != 'fecha_integracion']
UnboundLocalError: local variable 'sql_columns' referenced before assignment

2025-09-05 12:20:50,909 - INFO - Database connection closed
2025-09-05 12:20:50,909 - ERROR - ❌ Failed to upload Centro data to database
2025-09-05 12:20:50,909 - WARNING - ⚠️ Cycle failed, but continuing...
2025-09-05 12:20:50,909 - INFO - ====================================================================================================
2025-09-05 12:20:50,909 - INFO - 🚀 STARTING CYCLE #6 - 2025-09-05 12:20:50
2025-09-05 12:20:50,909 - INFO - ====================================================================================================
2025-09-05 12:20:50,909 - INFO - 📊 Cycle: Centro → Metropolitana → Centro → Metropolitana...
2025-09-05 12:20:50,909 - INFO - ⏹️  Press Ctrl+C to stop the permanent cycle
2025-09-05 12:20:50,909 - INFO - ====================================================================================================
2025-09-05 12:20:50,909 - INFO - 🏭 Processing Centro area...
2025-09-05 12:20:50,909 - INFO - 📊 Performing export actions...
2025-09-05 12:20:50,909 - INFO - 🖱️ Clicking Acciones button with multiple strategies...
2025-09-05 12:20:50,909 - INFO -    🔍 Trying XPath with text 'Acciones'...
