{"permissions": {"allow": ["Bash(cp:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(pip show:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(touch:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(sudo npm i -g @anthropic-ai/claude-code)", "Bash(npm i:*)", "Bash(pip3 list:*)", "mcp__sqlserver-database__describe_table", "mcp__sqlserver-database__read_query", "mcp__mysql-database__describe_table", "mcp__mysql-database__connect_db", "mcp__mysql-database__query", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(mysql:*)", "mcp__sqlserver-database__list_tables", "mcp__sqlserver-database__alter_table", "mcp__sqlserver-database__write_query", "Read(//home/<USER>", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(claude-code:*)"], "deny": []}, "enableAllProjectMcpServers": false}