#!/usr/bin/env python3
"""
Test script for unified VTR button functionality.
Tests the new single-button approach for all zones.
"""

import time
import os
import sys
from unittest.mock import Mock, patch

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'oracle_cloud_automation', 'src'))

from src.config import ELEMENT_SELECTORS, TIMING_CONFIG

def test_vtr_button_selectors():
    """Test that VTR button selectors are properly configured."""
    print("🧪 TESTING VTR BUTTON SELECTORS")
    print("=" * 50)

    vtr_selectors = ELEMENT_SELECTORS.get('vtr_button', [])

    if not vtr_selectors:
        print("❌ ERROR: No VTR button selectors found in config")
        return False

    print(f"✅ Found {len(vtr_selectors)} VTR button selectors:")

    for i, selector in enumerate(vtr_selectors, 1):
        print(f"   {i}. {selector}")

        # Validate selector format
        if selector.startswith("#"):
            print("      📍 Type: CSS ID selector")
        elif selector.startswith("//"):
            print("      📍 Type: XPath selector")
        elif "[" in selector or "." in selector:
            print("      📍 Type: CSS selector")
        else:
            print("      📍 Type: Text-based selector")
    # Check for the primary elId314 selector
    primary_selector = "#elId314"
    if primary_selector in vtr_selectors:
        print(f"✅ Primary selector '{primary_selector}' found")
    else:
        print(f"⚠️  Primary selector '{primary_selector}' not found")

    return True

def test_timing_configuration():
    """Test that timing configuration is updated for larger files."""
    print("\n⏱️  TESTING TIMING CONFIGURATION")
    print("=" * 50)

    download_wait = TIMING_CONFIG.get('download_wait', 0)
    vtr_download_timeout = TIMING_CONFIG.get('vtr_download_timeout', 0)

    print(f"📊 Download wait time: {download_wait} seconds")
    print(f"📊 VTR download timeout: {vtr_download_timeout} seconds")

    if download_wait == 25:
        print("✅ Download wait time correctly set to 25 seconds")
    else:
        print(f"⚠️  Download wait time is {download_wait}, expected 25")

    if vtr_download_timeout == 25:
        print("✅ VTR download timeout correctly set to 25 seconds")
    else:
        print(f"⚠️  VTR download timeout is {vtr_download_timeout}, expected 25")

    return download_wait == 25

def test_cycle_logic_simulation():
    """Simulate the new unified cycle logic."""
    print("\n🔄 TESTING UNIFIED CYCLE LOGIC")
    print("=" * 50)

    print("📋 New Cycle Flow:")
    print("   1. Click unified VTR button (elId2898)")
    print("   2. Perform export actions (Acciones → Exportar)")
    print("   3. Wait for download (larger file with all zones)")
    print("   4. Wait 25 seconds for file processing")
    print("   5. Upload to SQL Server (APPEND mode)")
    print("   6. Wait 10 seconds before next cycle")
    print("   7. Repeat")

    print("\n✅ Cycle logic simulation completed")
    print("   • No more Centro/Metropolitana navigation")
    print("   • Single button click per cycle")
    print("   • Optimized timing for larger files")

    return True

def test_main_controller_changes():
    """Test that main controller has been updated for unified approach."""
    print("\n🎮 TESTING MAIN CONTROLLER CHANGES")
    print("=" * 50)

    try:
        from src.main import OracleAutomationController

        # Check if the controller has the new methods
        controller = OracleAutomationController()

        # Check for new unified methods
        has_vtr_click = hasattr(controller, '_click_vtr_unified_button')
        has_unified_process = hasattr(controller, '_process_unified_data')

        # Check for removed methods
        has_navigate_metropolitana = hasattr(controller, '_navigate_to_metropolitana')
        has_navigate_centro = hasattr(controller, '_navigate_to_centro')

        print("🔍 Method Analysis:")
        print(f"   ✅ _click_vtr_unified_button: {'Found' if has_vtr_click else 'Missing'}")
        print(f"   ✅ _process_unified_data: {'Found' if has_unified_process else 'Missing'}")
        print(f"   🗑️  _navigate_to_metropolitana: {'Still exists' if has_navigate_metropolitana else 'Removed'}")
        print(f"   🗑️  _navigate_to_centro: {'Still exists' if has_navigate_centro else 'Removed'}")

        if has_vtr_click and has_unified_process:
            print("✅ Main controller updated correctly for unified approach")
            return True
        else:
            print("❌ Main controller missing unified methods")
            return False

    except ImportError as e:
        print(f"❌ Cannot import main controller: {e}")
        return False

def run_all_tests():
    """Run all tests for the unified VTR button functionality."""
    print("🚀 RUNNING UNIFIED VTR BUTTON TESTS")
    print("=" * 60)

    tests = [
        ("VTR Button Selectors", test_vtr_button_selectors),
        ("Timing Configuration", test_timing_configuration),
        ("Cycle Logic Simulation", test_cycle_logic_simulation),
        ("Main Controller Changes", test_main_controller_changes),
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append(result)
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {status}")
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            results.append(False)

    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = sum(results)
    total = len(results)

    print(f"✅ Tests Passed: {passed}/{total}")
    print(f"❌ Tests Failed: {total - passed}/{total}")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Unified VTR button implementation is ready")
        return True
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("🔧 Please review and fix the failed tests")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)