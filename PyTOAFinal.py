from selenium import webdriver
from selenium.webdriver.common.keys import Keys
import pandas as pd
import time
import os
import sys
from selenium.webdriver.edge.service import Service
from datetime import datetime

# Configuración básica
start_time = datetime.now()

# Mostrar mensajes de inicio
print("="*50)
print("INICIO DE PYTOA - DESCARGA DE CONSOLIDADO VTR")
print(f"Fecha y hora: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
print("="*50)

# Usar el driver local en vez de la descarga automática
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))
    msedgedriver_path = os.path.join(script_dir, "msedgedriver.exe")
    
    if not os.path.exists(msedgedriver_path):
        print(f"ERROR: No se encontró msedgedriver.exe en la carpeta: {script_dir}")
        print("Por favor, ejecute descargar_directo.py para obtener el driver")
        sys.exit(1)
    
    print(f"Usando msedgedriver local encontrado en: {msedgedriver_path}")
    service = Service(msedgedriver_path)
    driver = webdriver.Edge(service=service)
    print("Driver Edge inicializado correctamente")
except Exception as e:
    print(f"Error de inicialización: {str(e)}")
    sys.exit(1)

# Configurar el periodo actual
periodo = 'Sep'  # Cambiar según el mes necesario
print(f"\nPeriodo configurado: {periodo}")

# Definir la ruta del archivo a descargar
filePath = f'C:\\Users\\<USER>\\Downloads\\Consolidado Actividades Andes VTR RGU {periodo}-25.xlsx'
print(f"Archivo a descargar: {filePath}")

# Definir la URL de descarga
download_url = f"https://desarrollo.telqway.cl/public.php/dav/files/ir4pDYEpc8gsc3j/Consolidado%20Actividades%20Andes%20VTR%20RGU%20{periodo}-25.xlsx"

# Verificar si el archivo ya existe y eliminarlo si es necesario
try:
    if os.path.exists(filePath):
        print(f"\nArchivo existente encontrado: {filePath}")
        print("Eliminando archivo existente...")
        os.remove(filePath)
        print("Archivo eliminado correctamente")
    else:
        print("\nNo se encontró archivo existente, procediendo con descarga nueva")
        
    # Iniciar descarga
    print("\n" + "-"*50)
    print("INICIANDO DESCARGA DEL ARCHIVO")
    print("-"*50)
    print(f"URL de descarga: {download_url}")
    
    # Navegar a la URL de descarga
    driver.get(download_url)
    
    # Esperar y mostrar progreso
    total_wait = 300  # 5 minutos
    interval = 10     # Actualizar cada 10 segundos
    
    print(f"Esperando descarga (esto puede tomar hasta {total_wait} segundos)")
    
    for i in range(0, total_wait, interval):
        time.sleep(interval)
        # Verificar si el archivo ya existe
        if os.path.exists(filePath):
            print(f"Archivo descargado exitosamente después de {i+interval} segundos")
            break
        else:
            print(f"Esperando... {i+interval}/{total_wait} segundos transcurridos")
    
    # Verificación final
    if os.path.exists(filePath):
        file_size = os.path.getsize(filePath) / (1024 * 1024)  # Tamaño en MB
        print(f"\nDescarga completada: {filePath}")
        print(f"Tamaño del archivo: {file_size:.2f} MB")
    else:
        print("\nAdvertencia: No se pudo verificar la descarga del archivo")
        
except Exception as e:
    print(f"\nError durante el proceso: {str(e)}")

finally:
    # Cerrar el navegador
    print("\nCerrando el navegador...")
    driver.quit()
    
    # Mostrar resumen final
    end_time = datetime.now()
    duration = end_time - start_time
    print("\n" + "="*50)
    print("FINALIZADO")
    print(f"Tiempo de ejecución: {duration.total_seconds():.2f} segundos")
    print(f"Fecha y hora: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50)

