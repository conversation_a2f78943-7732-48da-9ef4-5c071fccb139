#!/usr/bin/env python3
"""
Debug script to test element detection in Oracle Cloud.
This script helps identify the exact selectors for buttons.
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_driver():
    """Setup Edge WebDriver."""
    try:
        edge_options = Options()
        edge_options.add_argument('--start-maximized')
        edge_options.add_argument('--disable-gpu')
        edge_options.add_argument('--no-sandbox')

        service = Service("msedgedriver.exe")
        driver = webdriver.Edge(service=service, options=edge_options)

        driver.implicitly_wait(10)
        driver.set_page_load_timeout(30)

        logger.info("✅ Edge WebDriver setup completed")
        return driver

    except Exception as e:
        logger.error(f"❌ Error setting up WebDriver: {str(e)}")
        return None

def debug_elements(driver):
    """Debug and list all clickable elements on the page."""
    try:
        logger.info("🔍 Debugging page elements...")

        # Get all buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        logger.info(f"📊 Found {len(buttons)} buttons on page")

        exportar_buttons = []
        acciones_buttons = []

        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                btn_id = btn.get_attribute('id')
                btn_class = btn.get_attribute('class')
                is_displayed = btn.is_displayed()
                is_enabled = btn.is_enabled()

                logger.info(f"   🔘 Button {i+1}: '{text}' (ID: {btn_id}, Class: {btn_class}, Displayed: {is_displayed}, Enabled: {is_enabled})")

                # Check for specific buttons
                if "exportar" in text.lower():
                    exportar_buttons.append((text, btn_id, btn_class))
                if "acciones" in text.lower():
                    acciones_buttons.append((text, btn_id, btn_class))

            except Exception as e:
                logger.warning(f"   ⚠️ Error reading button {i+1}: {str(e)}")
                continue

        # Get all links
        links = driver.find_elements(By.TAG_NAME, "a")
        logger.info(f"📊 Found {len(links)} links on page")

        for i, link in enumerate(links[:20]):  # Show first 20 links
            try:
                text = link.text.strip()
                href = link.get_attribute('href')
                logger.info(f"   🔗 Link {i+1}: '{text}' (href: {href})")
            except:
                continue

        # Get all clickable divs
        clickable_divs = driver.find_elements(By.XPATH, "//div[@onclick or @role='button']")
        logger.info(f"📊 Found {len(clickable_divs)} clickable divs")

        for i, div in enumerate(clickable_divs[:20]):
            try:
                text = div.text.strip()
                div_id = div.get_attribute('id')
                logger.info(f"   📦 Div {i+1}: '{text}' (ID: {div_id})")
            except:
                continue

        # Summary
        logger.info("📋 SUMMARY:")
        logger.info(f"   🎯 Exportar buttons found: {len(exportar_buttons)}")
        for text, btn_id, btn_class in exportar_buttons:
            logger.info(f"      - '{text}' (ID: {btn_id}, Class: {btn_class})")

        logger.info(f"   🎯 Acciones buttons found: {len(acciones_buttons)}")
        for text, btn_id, btn_class in acciones_buttons:
            logger.info(f"      - '{text}' (ID: {btn_id}, Class: {btn_class})")

        logger.info("✅ Page element debugging completed")

    except Exception as e:
        logger.error(f"❌ Error debugging page elements: {str(e)}")

def main():
    """Main debug function."""
    try:
        logger.info("🚀 Starting Oracle Cloud element debugging...")

        # Setup driver
        driver = setup_driver()
        if not driver:
            return

        # Navigate to Oracle Cloud
        logger.info("🌐 Navigating to Oracle Cloud...")
        driver.get("https://vtr.fs.ocs.oraclecloud.com/")

        # Wait for page to load
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        logger.info("✅ Oracle Cloud page loaded")

        # Wait for user to login and navigate to the right page
        input("🔐 Please login to Oracle Cloud and navigate to the page with the buttons, then press Enter to start debugging...")

        # Debug elements
        debug_elements(driver)

        # Keep browser open for manual inspection
        input("🔍 Press Enter to close the browser...")

    except KeyboardInterrupt:
        logger.info("🛑 Debug stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {str(e)}")
    finally:
        if 'driver' in locals():
            driver.quit()
            logger.info("🛑 Browser closed")

if __name__ == "__main__":
    main()